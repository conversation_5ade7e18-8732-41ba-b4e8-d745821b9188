import { fetchGet, fetchPost, fetchPatch } from "../fetch";
import Vue from "vue";
import axios from "../http";

// 获取沟通记录列表
function getCommunicationList(data) {
  return fetchGet("/api/market-service/communication/list", { params: data });
}
// 创建沟通记录
function createCommunication(data) {
  return fetchPost("/api/market-service/communication/create", data);
}
// 用id查询沟通记录
function getCommunicationById(data) {
  return fetchGet(
    `/api/market-service/communication/info?communication_id=${data}`
  );
}
// 修改沟通记录
function updateCommunicationById(data) {
  return fetchPatch(
    `/api/market-service/communication/update?communication_id=${data.id}`,
    data
  );
}
// 沟通记录类别
function getCategroy(data) {
  return fetchGet("/api/market-service/communication/map/category", {
    params: data
  });
}
// 沟通记录批阅创建
function createCommunicationCommit(data) {
  return fetchPost("/api/market-service/communication-commit/create", data);
}
// 沟通记录批阅更新
function updateCommunicationCommit(data) {
  return fetchPatch(
    `/api/market-service/communication-commit/update?commit_id=${data.id}`,
    data
  );
}

// 班级导出
function connectRecordExport(data) {
  return axios
    .get(`/api/market-service/communication/export`, {
      params: { ...data, exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async getCommunicationList(data) {
    return getCommunicationList(data);
  },
  async createCommunication(data) {
    return createCommunication(data);
  },
  async getCommunicationById(data) {
    return getCommunicationById(data);
  },
  async updateCommunicationById(data) {
    return updateCommunicationById(data);
  },
  async getCategroy(data) {
    return getCategroy(data);
  },
  async createCommunicationCommit(data) {
    return createCommunicationCommit(data);
  },
  async updateCommunicationCommit(data) {
    return updateCommunicationCommit(data);
  },
  async ConnectRecordExport(data) {
    return connectRecordExport(data);
  }
};
