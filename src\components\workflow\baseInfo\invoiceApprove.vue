<template>
  <div class="content">
    <div class="receipt-no-box">
      <span class="item__label">收据号</span>
      <span
        v-for="(item, index) in basic_info?.receipt_infos || []"
        :key="index"
        @click="toDetail(item)"
        class="item__content text-primary"
      >
        {{ item.receipt_no
        }}<span
          style="color: #333"
          v-if="index !== basic_info.receipt_infos.length - 1"
          >、</span
        >
      </span>
    </div>
    <div class="detail content-bottom">
      <div class="item">
        <span class="item__label">申请编号</span>
        <span class="item__content">{{ approve_info.serial_number }}</span>
      </div>
      <div class="item">
        <span class="item__label">审批状态</span>
        <span class="item__content">{{
          approve_info.status | getStatusCn
        }}</span>
      </div>
      <div class="item">
        <span class="item__label">业务类型</span>
        <span class="item__content">{{ approve_info.template_name }}</span>
      </div>
      <!-- <div class="item">
        <span class="item__label">校区</span>
        <span class="item__content">{{ promoter_info.department_name }}</span>
      </div> -->
      <div v-if="basic_info.head_type === 1" class="item">
        <span class="item__label">企业名称</span>
        <span class="item__content">{{ basic_info.company_name }}</span>
      </div>
      <div v-if="basic_info.head_type === 1" class="item">
        <span class="item__label">纳税人识别号</span>
        <span class="item__content">{{ basic_info.tax_number }}</span>
      </div>
      <div v-if="basic_info.head_type === 2" class="item">
        <span class="item__label">姓名</span>
        <span class="item__content">{{ basic_info.full_name }}</span>
      </div>
      <div class="item">
        <span class="item__label">电话号码</span>
        <span class="item__content">{{ basic_info.phone_number }}</span>
      </div>
      <div class="item">
        <span class="item__label">地址</span>
        <span class="item__content">{{ basic_info.address }}</span>
      </div>
      <div class="item">
        <span class="item__label">银行账号</span>
        <span class="item__content">{{ basic_info.bank_account }}</span>
      </div>
      <div class="item">
        <span class="item__label">开户银行</span>
        <span class="item__content">{{ basic_info.bank_name }}</span>
      </div>
      <div class="item">
        <span class="item__label">邮箱</span>
        <span class="item__content">{{ basic_info.email }}</span>
      </div>
    </div>
    <div
      class="attachment-preview"
      v-if="basic_info.attachment && basic_info.attachment.length"
    >
      <div class="item__label">图片附件</div>
      <div class="preview-imgs">
        <img
          v-for="(item, index) in basic_info.attachment"
          :key="index"
          :src="item"
          preview="preview"
          preview-text=""
        />
      </div>
    </div>
    <!-- 收费收据 -->
    <receipt-detail
      v-if="receipt_detail_visible"
      :receiptNo="receipt_no"
      :receiptType="receipt_type"
      :payTime="pay_time"
      :status="receipt_status"
      :readonly="true"
      @close="receipt_detail_visible = false"
    ></receipt-detail>
  </div>
</template>
<script>
import { base_info_components_name, workflow_status } from "@/public/dict";
import { downLoadFileByHref } from "@/public/downLoadFile";
import receiptDetail from "@/views/receiptManagement/components/detail.vue";
export default {
  name: base_info_components_name.receipt_invoice,
  components: {
    receiptDetail
  },
  data() {
    return {
      receipt_detail_visible: false,
      receipt_no: "",
      receipt_type: "",
      pay_time: "",
      receipt_status: ""
    };
  },
  props: {
    approve_info: Object,
    basic_info: Object,
    promoter_info: Object
  },
  filters: {
    getStatusCn(val) {
      const item = workflow_status.find((item) => val === item.id);
      return typeof item === "undefined" ? "" : item.name;
    }
  },
  methods: {
    downloadFile(files) {
      downLoadFileByHref(files);
    },
    // 查看详情
    toDetail(item) {
      this.receipt_no = item.receipt_no;
      this.receipt_type = item.receipt_type;
      this.pay_time = item.pay_time;
      this.receipt_status = item.receipt_status;
      this.receipt_detail_visible = true;
    }
  }
};
</script>
<style lang="less" scoped>
@import "~@/assets/workflow/css/detailInfo.less";
.receipt-no-box {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  .item__label {
    width: 100px;
    font-size: 14px;
    color: #8492a6;
  }
  .item__content {
    font-size: 14px;
    margin-left: 10px;
    cursor: pointer;
    color: #2d80ed;
  }
}
/deep/ .detail .item .item__content.tg_down-btn {
  color: #2d80ed;
  cursor: pointer;
}
.attachment-preview {
  font-size: 13px;
  font-family: "PingFangSC-Medium, sans-serif,Arial";
  color: #8492a6;
  .item__label {
    margin: 16px 0;
  }
  .preview-imgs {
    img {
      width: 140px;
      height: 140px;
      background-color: #f3f3f3;
      margin-right: 10px;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}
</style>
