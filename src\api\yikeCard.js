import axios from "../http";
import Vue from "vue";
import qs from "qs";

function createMsembership(data) {
  return axios
    .post(`/api/student-service/yikeCard/create-membership`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}

function createPrepaid(data) {
  return axios
    .post(`/api/student-service/yikeCard/create-prepaid`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 发卡
function sendMembership(data) {
  return axios
    .post(`/api/student-service/yikeCard/send-membership`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 导出
function membershipExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/membership-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function prepaidExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/prepaid-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function membershipList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/membership-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function prepaidList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/yikeCard/prepaid-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async createMsembership(data) {
    return createMsembership(data);
  },
  async createPrepaid(data) {
    return createPrepaid(data);
  },
  async membershipList(data) {
    return membershipList(data);
  },

  async prepaidList(data) {
    return prepaidList(data);
  },
  async membershipExport(data) {
    return membershipExport(data);
  },
  async prepaidExport(data) {
    return prepaidExport(data);
  },
  async sendMembership(data) {
    return sendMembership(data);
  }
};
