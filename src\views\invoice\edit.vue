<template>
  <div class="invoice-edit">
    <el-dialog
      :title="title"
      :visible="true"
      width="1016px"
      :before-close="close"
    >
      <div>
        <div class="section-content">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            class="tg-form"
            label-position="right"
            label-width="88px"
            :inline="true"
          >
            <div class="section-title">
              <span></span>
              <span>发票详情</span>
            </div>
            <div>
              <el-form-item label="抬头类型" prop="head_type" required>
                <el-radio
                  :disabled="formInputDisabled"
                  v-model="form.head_type"
                  label="1"
                  >企业单位</el-radio
                >
                <el-radio
                  :disabled="formInputDisabled"
                  v-model="form.head_type"
                  label="2"
                  >个人/非企业单位</el-radio
                >
              </el-form-item>
            </div>
            <div v-if="form.head_type == 1" class="tg_form-item-row">
              <el-form-item required prop="name">
                <div class="tg-label--custom tg-form--required">企业名称</div>
                <el-input
                  :disabled="formInputDisabled"
                  type="input"
                  v-model="form.name"
                  placeholder="公司名称（必填）"
                ></el-input>
              </el-form-item>
              <el-form-item required prop="tax_number">
                <div class="tg-label--custom tg-form--required">税号</div>
                <el-input
                  :disabled="formInputDisabled"
                  type="text"
                  v-model="form.tax_number"
                  minlength="15"
                  maxlength="20"
                  placeholder="15-20位数和字母（必填）"
                ></el-input>
              </el-form-item>
            </div>
            <div v-if="form.head_type == 2" class="tg_form-item-row">
              <el-form-item required prop="name">
                <div class="tg-label--custom tg-form--required">姓名</div>
                <el-input
                  :disabled="formInputDisabled"
                  type="text"
                  v-model="form.name"
                  placeholder="请填写抬头名称（必填）"
                ></el-input>
              </el-form-item>
            </div>
            <div style="margin-top: 16px" class="section-title">
              <span></span>
              <span>更多内容</span>
            </div>
            <div v-if="form.head_type == 1">
              <div class="tg_form-item-row">
                <el-form-item prop="mobile">
                  <div class="tg-label--custom">电话号码</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    maxlength="11"
                    v-model="form.mobile"
                    :placeholder="formInputDisabled ? '' : '请输入电话号码'"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="address">
                  <div class="tg-label--custom">地址</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.address"
                    :placeholder="
                      formInputDisabled
                        ? ''
                        : form.head_type == 1
                        ? '请输入企业地址'
                        : '请输入联系地址'
                    "
                  ></el-input>
                </el-form-item>
              </div>
              <div class="tg_form-item-row">
                <el-form-item prop="bank_account">
                  <div class="tg-label--custom">银行账号</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.bank_account"
                    :placeholder="formInputDisabled ? '' : '请输入银行卡号'"
                    @blur="bankCardNumBlur"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="bank_name">
                  <div class="tg-label--custom">开户银行</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.bank_name"
                    :placeholder="formInputDisabled ? '' : '请输入开户银行'"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="tg_form-item-row">
                <el-form-item prop="bank_name">
                  <div class="tg-label--custom">邮箱</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.email"
                    :placeholder="formInputDisabled ? '' : '请输入邮箱'"
                  ></el-input>
                </el-form-item>
              </div>
            </div>
            <div v-else-if="form.head_type == 2">
              <div class="tg_form-item-row">
                <el-form-item prop="">
                  <div class="tg-label--custom">电话号码</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    maxlength="11"
                    v-model="form.mobile"
                    :placeholder="formInputDisabled ? '' : '请输入电话号码'"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="">
                  <div class="tg-label--custom">地址</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.address"
                    :placeholder="formInputDisabled ? '' : '请输入联系地址'"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="tg_form-item-row">
                <el-form-item prop="">
                  <div class="tg-label--custom">银行账号</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.bank_account"
                    :placeholder="formInputDisabled ? '' : '请输入银行卡号'"
                    @blur="bankCardNumBlur"
                  ></el-input>
                </el-form-item>
                <el-form-item prop="">
                  <div class="tg-label--custom">开户银行</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.bank_name"
                    :placeholder="formInputDisabled ? '' : '请输入开户银行'"
                  ></el-input>
                </el-form-item>
              </div>
              <div class="tg_form-item-row">
                <el-form-item prop="">
                  <div class="tg-label--custom">邮箱</div>
                  <el-input
                    :disabled="formInputDisabled"
                    type="text"
                    v-model="form.email"
                    :placeholder="formInputDisabled ? '' : '请输入邮箱'"
                  ></el-input>
                </el-form-item>
              </div>
            </div>

            <div class="tg_form-item-row">
              <el-form-item
                label="附件"
                label-width="50px"
                class="attachment-container"
              >
                <el-upload
                  v-if="!formInputDisabled"
                  action="#"
                  :accept="file_accept"
                  :file-list="fileList"
                  :limit="10"
                  ref="uploadImgs"
                  :on-preview="handlePreview"
                  :before-upload="beforeUpload"
                  :on-remove="handleRemove"
                  :http-request="uploadImg"
                >
                  <el-button
                    class="tg-button--primary upload-button"
                    type="primary"
                    >请选择</el-button
                  >
                  <div slot="tip" class="el-upload__tip">
                    只能上传jpg、png、gif、jpeg文件，且不超过20Mb
                  </div>
                </el-upload>
                <div v-else class="img-container">
                  <div
                    class="tg_down-btn"
                    v-if="form.attachment && form.attachment.length"
                    @click="downloadFile(form.attachment)"
                  >
                    下载附件
                  </div>
                  <div>
                    <el-image
                      v-for="(item, index) in form?.attachment || []"
                      :key="index"
                      style="width: 100px; height: 100px"
                      :src="item"
                      @click="handlePreview(item)"
                      fit="fill"
                    ></el-image>
                  </div>
                </div>
                <el-dialog
                  title="预览"
                  :append-to-body="true"
                  :visible.sync="dialogVisible"
                >
                  <img width="100%" :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
            </div>
            <div v-if="form.review_attachment && form.review_attachment.length">
              <el-form-item prop="review_status" label="">
                <div class="tg-label--custom">
                  审核附件
                  <span
                    class="tg_down-btn"
                    @click="downloadFile(form.review_attachment)"
                  >
                    下载附件
                  </span>
                </div>
                <div class="review-attachment-list">
                  <p
                    v-for="(item, index) in form.review_attachment"
                    :key="index"
                    @click="handlePreview(item, 'review_attachment')"
                  >
                    {{ item | file_name }}
                  </p>
                </div>
              </el-form-item>
            </div>
          </el-form>
          <div class="amout-num">
            <span>总金额</span>
            <span>{{ (form.price / 100).toFixed(2) }}</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="close">
          {{ pageType == "edit" ? "取消" : "关闭" }}
        </el-button>
        <el-button
          v-if="pageType == 'edit'"
          class="tg-button--primary"
          type="primary"
          v-throttle="confirm"
        >
          确定
        </el-button>
        <el-button
          v-if="pageType == 'agree'"
          class="tg-button--primary"
          type="primary"
          @click="showFileUploadDialog = true"
        >
          同意
        </el-button>
      </span>
    </el-dialog>
    <file-upload-dialog
      v-if="showFileUploadDialog"
      ref="fileUploadDialog"
      type="agree"
      :review_attachment.sync="review_attachment"
      @close="showFileUploadDialog = false"
      @confirm="agree"
    />
  </div>
</template>

<script>
import invoiceApi from "@/api/invoice";
import { Message } from "element-ui";
import { downLoadFileByHref } from "@/public/downLoadFile";
import { v4 as uuidv4 } from "uuid";
import fileUploadDialog from "./fileUploadDialog.vue";
import {
  verifyBankCardNumber,
  getBankCardAttribution
} from "@/public/bankCard";
const validBankNum = (rule, value, callback) => {
  if (value) {
    if (!verifyBankCardNumber(value)) {
      Message.info("银行卡号不正确");
      callback(new Error(""));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
const validTaxNumber = (rule, value, callback) => {
  if (value) {
    if (!/^[a-zA-Z0-9]{15,20}$/.test(value)) {
      Message.info("税号格式不正确");
      callback(new Error(""));
    } else {
      callback();
    }
  } else {
    callback(new Error(""));
  }
};
const validMobile = (rule, value, callback) => {
  if (value) {
    if (!/^(?:(?:\+|00)86)?1\d{10}$/.test(value)) {
      Message.info("手机号码格式不正确");
      callback(new Error(""));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
export default {
  components: {
    fileUploadDialog
  },
  props: {
    invoice_id: {
      type: String,
      required: false,
      default: ""
    },
    receipt_infos: {
      type: Array,
      required: false,
      default: () => []
    },
    pageType: {
      type: String,
      required: true,
      default: "edit"
    },
    price: {
      type: Number,
      required: false,
      default: 0
    }
  },

  data() {
    return {
      formInputDisabled: false,
      form: {
        head_type: "1",
        address: "",
        bank_account: "",
        bank_name: "",
        mobile: "",
        name: "",
        tax_number: "",
        email: "",
        price: 0,
        review_attachment: [], // 审核附件
        attachment: [] // 发票附件
      },
      rules: {
        bank_account: [{ validator: validBankNum, trigger: "blur" }],
        tax_number: [{ validator: validTaxNumber, trigger: "blur" }],
        mobile: [{ validator: validMobile, trigger: "blur" }]
      },
      fileList: [],
      review_attachment: [], // 审批附件
      dialogVisible: false,
      dialogImageUrl: "",
      showFileUploadDialog: false
    };
  },
  filters: {
    file_name(val) {
      return val.split("/").pop();
    }
  },
  computed: {},
  created() {
    this.Oss.getAliyun();
    this.file_accept = "image/png,image/jpg,image/gif,image/jpeg";
    this.title = this.pageType === "edit" ? "开具发票" : "详情";
    this.formInputDisabled =
      this.pageType === "info" || this.pageType === "agree";
    this.form.price = this.price;
    if (this.pageType === "info" || this.pageType === "agree") {
      this.getInfo();
    }
  },
  methods: {
    bankCardNumBlur() {
      const { bank_account } = this.form;
      if (bank_account) {
        const bankAttrs = getBankCardAttribution(bank_account);
        this.form.bank_name = bankAttrs.bankName;
      }
    },
    getInfo() {
      const { invoice_id } = this;
      invoiceApi
        .getInfo({
          invoice_id
        })
        .then((res) => {
          const { code, message, data } = res.data;
          if (code === 0) {
            this.form = data.content;
          } else {
            this.$message.error(message);
          }
        });
    },
    changeType() {
      this.form.dayTime = "";
      this.form.time = "";
    },
    handlePreview(file, type) {
      const url = this.pageType === "edit" ? file.url : file;
      if (type === "review_attachment") {
        if (file.endsWith(".pdf")) {
          window.open(url, "_blank");
        } else {
          this.dialogImageUrl = url;
          this.dialogVisible = true;
        }
      } else {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      }
    },
    beforeUpload(file) {
      // const { uploadFiles } = this.$refs["uploadImgs"];
      const isLt20M = file.size / 1024 / 1024 > 20;
      if (isLt20M) {
        this.$message.info("上传头像图片大小不能超过 20MB!");
        return false;
      }
    },
    uploadImg(upload) {
      const f = upload.file;
      const orginName = f.name.substring(0, f.name.lastIndexOf("."));
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${orginName}_${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          this.fileList.push({
            name: res.objectKey,
            url: res.url
          });
        }
      });
    },
    handleRemove(file) {
      this.fileList.map((item, index) => {
        if (item.uid === file.uid) {
          this.fileList.splice(index, 1);
        }
      });
    },
    downloadFile(file) {
      if (file && file.length) {
        downLoadFileByHref(file);
      }
    },
    // 收据管理页面创建发票
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const { price, receipt_infos } = this;
          const files = this.fileList.map((item) => item?.url);

          const params = {
            content: {
              ...this.form,
              attachment: files,
              price
            },
            receipt_infos
          };
          invoiceApi.apply(params).then((res) => {
            const { code, data, message } = res.data;
            if (code === 0) {
              this.$message.success("提交成功！");
              // setTimeout(() => {
              this.$parent?.clearSelection();
              this.$parent?.search();
              this.$emit("close");
              // }, 3000);
            } else if (code === 2) {
              const receipt_list = data.filter((item) => !item?.is_apply);

              this.$parent.invoiced_list = receipt_list;
              this.$parent.invoiced_visible = true;
            } else {
              this.$message.error(message);
            }
          });
        }
      });
    },
    agree() {
      const review_attachment = this.review_attachment.map((item) => item?.url);
      invoiceApi
        .agree({
          review_attachment,
          invoice_id: this.invoice_id
        })
        .then((res) => {
          const { code, message } = res.data;
          if (code === 0) {
            this.$message.success("提交成功！");
            this.$parent.search();
            this.$emit("close");
          } else {
            this.$message.error(message);
          }
        });
    },
    close(type) {
      this.$emit("close", type);
    }
  }
};
</script>
<style scoped lang="less">
.invoice-edit {
  ::v-deep .el-dialog__body {
    padding: 16px;
    .section-content {
      .section-title {
        display: flex;
        align-items: center;
        font-weight: 500;
        margin-bottom: 6px;
        span:nth-child(1) {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: #2d80ed;
          margin-right: 6px;
        }
      }
      .tg-form .tg-form-item--custom {
        width: 100%;
      }
      .tg-label--custom {
        padding-left: 10px;
      }

      .tg-form--required {
        &::before {
          content: "*";
          color: #ff0317;
          margin-right: 4px;
        }
      }
      .tg_form-item-row {
        display: flex;
        justify-content: space-between;
        .el-form-item:nth-child(2) {
          margin-right: 10px;
        }
      }
      .el-input {
        width: 456px;
        padding-left: 10px;
      }
    }
  }
  .tg_down-btn {
    color: #2d80ed;
    // padding-bottom: 16px;
    cursor: pointer;
  }
  .img-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    .el-image {
      margin-right: 10px;
    }
  }
  .amout-num {
    margin-top: 100px;
    display: flex;
    height: 48px;
    border: 1px solid#2d80ed;
    align-content: center;
    box-sizing: border-box;
    border-radius: 4px;
    overflow: hidden;
    line-height: 48px;
    span:nth-child(1) {
      width: 104px;
      background: #f5f8fc;
      display: block;

      font-weight: 500;
      text-align: center;
    }
    span:nth-child(2) {
      display: block;
      flex: 1;
      color: #ff0317;
      font-size: 20px;
      padding-left: 20px;
      &::after {
        content: "元";
        font-size: 14px;
        color: #38485d;
        margin-left: 5px;
        vertical-align: bottom;
      }
    }
  }
  .upload-button {
    margin-top: 10px;
  }
  .attachment-container {
    ::v-deep .el-form-item__content {
      width: calc(100% - 50px);
    }
  }
  .review-attachment-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 0 0 0 16px;
    p {
      color: #2d80ed;
      cursor: pointer;
    }
  }
}
</style>
