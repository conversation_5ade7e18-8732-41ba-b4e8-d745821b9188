import axios from "../http";
import Vue from "vue";
import qs from "qs";

// new 内容查找
function getDeductData(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/deduct-report?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课消明细合计
function getDeductDataTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/deduct-report-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课消明细导出
function exportCourse(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/deduct-list-report?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课消明细
function getDeductDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/student-deduct-detail?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getDeductDetailTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/order-service/admin/deduct/student-deduct-detail-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课消汇总
function getDeductSummary(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/student-deduct-summary?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getDeductSummaryTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/order-service/admin/deduct/student-deduct-summary-total?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportDeductDetailList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/order-service/admin/deduct/student-deduct-detail-report?${new_data}`,
      {
        params: { exportData: 1 }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportDeductSummary(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/order-service/admin/deduct/student-deduct-summary-report?${new_data}`,
      {
        params: { exportData: 1 }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课消汇总导出
function deductReportExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/deduct/deduct-report-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async ExportCourse(data) {
    return exportCourse(data);
  },
  async getDeductData(data) {
    return getDeductData(data);
  },
  async getDeductDataTotal(data) {
    return getDeductDataTotal(data);
  },
  async getDeductDetail(data) {
    return getDeductDetail(data);
  },
  async getDeductDetailTotal(data) {
    return getDeductDetailTotal(data);
  },
  async getDeductSummary(data) {
    return getDeductSummary(data);
  },
  async getDeductSummaryTotal(data) {
    return getDeductSummaryTotal(data);
  },
  async exportDeductDetailList(data) {
    return exportDeductDetailList(data);
  },
  async exportDeductSummary(data) {
    return exportDeductSummary(data);
  },

  deductReportExport
};
