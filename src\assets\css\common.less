@text-famliy_normal: "PingFangSC-Regular, sans-serif,Arial"; //苹方-简 常规体
@text-famliy_ultralight: "PingFangSC-Ultralight, sans-serif,Arial"; //苹方-简 极细体
@text-famliy_light: "PingFangSC-Light, sans-serif,Arial"; // 苹方-简 细体
@text-famliy_thin: "PingFangSC-Thin, sans-serif,Arial"; //苹方-简 纤细体
@text-famliy_medium: "PingFangSC-Medium, sans-serif,Arial"; // 苹方-简 中黑体
@text-famliy_semibold: "PingFangSC-Semibold, sans-serif,Arial"; // 苹方-简 中粗体
@text-size_special: 12px;
@text-size_small: 13px;
@text-size_normal: 14px;
@text-size_medium: 16px;
@text-size_large: 24px;
@text-size_most: 28px;
@text-color_first: #1f2d3d;
@text-color_second: #475669;
@text-color_third: #8492a6;
@text-color_unimportant: #c0ccda;
@input-border_color: #d3dce6;
@text-color_white: #fff;
@base-color: #2d80ed;
@light-color: #ebf4ff;
@base-gray: #9fa1a0;
@base-red: #ff0317;
@base-shadow-color:rgba(101, 181, 255, .4);

.tg-header__title {
  font-size: @text-size_large;
  font-family: @text-famliy_semibold;
  height: 68px;
  line-height: 68px;
  color: @text-color_first;
  padding-left: 6px;
  padding-right: 6px;
}

button.tg-button--primary {
  height: 32px;
  font-family: @text-famliy_light;
  border-radius: 4px;
  padding: 0 22px;
  font-weight: normal;
}

button.tg-button--plain {
  background-color: transparent;
  border: 1px solid @base-color;
  color: @base-color;
  padding: 0 22px;
  height: 32px;
  font-family: @text-famliy_light;
  font-weight: normal;

  &:hover {
    background-color: @base-color;
    color: #fff;
  }
}

button.tg-button--mini {
  width: 64px;
  padding: 0;
}

button.tg-button--disabled {
  color: @text-color_unimportant;
  border-color: @text-color_unimportant;

  &:hover {
    background-color: transparent;
    color: @text-color_unimportant;
  }
}

.container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-direction: column;
  margin: 0;
  max-width: 100% !important;
}

/**表格**/
::v-deep .el-table {
  padding: 0 16px;
  height: calc(100% - 52px);
}


::v-deep.tg-table__box:not(:has(.tg-pagination)) {
  .el-table {
    height: 100%;
  }
}

.table-container:not(:has(.tg-pagination))::after {
  bottom: 0px;
}

::v-deep .el-table__header {
  // border: 1px solid @base-color!important;
  border-radius: 4px;
  box-shadow: 0 2px 0 0 @light-color;

  th {
    font-weight: 600;
  }
}

::v-deep .el-table__body-wrapper {
  height: calc(100% - 46px);
  z-index: 2;
}

::v-deep .el-table .cell {
  font-family: @text-famliy_medium;
  font-size: @text-size_normal;
}

::v-deep th.el-table-column--selection .cell {
  padding-left: 14px;
  padding-right: 14px;
}

::v-deep .el-table td,
::v-deep .el-table th {
  padding: 0 !important;
  height: 46px;
  user-select: auto;
}

/**复选框**/
::v-deep .el-checkbox__inner {
  border: 1px solid @base-color;
  border-radius: 1px;
  height: 14px;
  width: 14px;
}

div.tg-pagination {
  background-color: #fff;
  height: 52px;
  line-height: 52px;
  padding: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 17px;
  padding-right: 24px;
  justify-content: space-between;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

/**分页**/
::v-deep .el-pagination {
  background-color: transparent;
  height: 30px;

  .el-select .el-input {
    margin: 0;
  }

  .el-input.is-focus::after {
    height: 28px;
    border-radius: 5px;
  }
}

::v-deep .el-pagination__total {
  color: @text-color_third;
  font-size: @text-size_small;
}

::v-deep .el-pagination__editor.is-in-pagination {
  line-height: 28px;
}

::v-deep .el-pagination.is-background .btn-next,
::v-deep .el-pagination.is-background .btn-prev,
::v-deep .el-pagination.is-background .el-pager li {
  min-width: 28px !important;
  height: 28px;
  line-height: 28px;
  border-radius: 4px;
  padding: 0;
  margin: 0 4px;
  color: @text-color_second;
  border: 1px solid #e1e7ef;
  font-weight: normal;
  background-color: transparent;
}

::v-deep .el-pagination__jump {
  margin-left: 4px;
}

::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  color: @base-color;
  background-color: transparent;
  border-color: @base-color;
}

::v-deep .el-pagination__editor.el-input {
  width: 36px;
}

::v-deep .el-pagination.is-background .el-pager li {
  line-height: 26px;
}

::v-deep .el-pagination__sizes {
  display: inline-flex;

  .el-input__inner,
  .el-select .el-input .el-select__caret {
    height: 28px;
    line-height: 28px;
  }
}

.tg-box--margin {
  margin-top: 16px;
}

.tg-box--width {
  width: 100%;
}

button.tg-text--blue,
span.tg-text--blue {
  white-space: normal;
  word-break: break-all;
  margin-right: 8px;
  color: #157df0;
  font-family: @text-famliy_medium;
  font-size: @text-size_small;
  font-weight: normal;
}

button.tg-text--green,
span.tg-text--green {
  white-space: normal;
  word-break: break-all;
  margin-right: 8px;
  color: #2d80ed;
  font-family: @text-famliy_medium;
  font-size: @text-size_small;
  font-weight: normal;
}
span.tg-text--gray{
  color: @base-gray;
}
/**评分**/
::v-deep .el-rate {
  width: 124px;
  height: 16px;
  display: flex;
}

::v-deep .el-rate__icon.el-icon-star-off {
  // border: 1px solid @base-color!important;
  color: @base-color !important;
  font-size: @text-size_medium;
}

::v-deep .el-rate__icon.el-icon-star-on {
  color: @base-color !important;
}

/**input框**/
::v-deep .el-input__inner {
  height: 32px;
  line-height: 32px;
}

::v-deep .el-input,
::v-deep .el-date-editor.el-input,
::v-deep .el-date-editor.el-input__inner {
  width: 168px;
}

/**表单form**/
::v-deep .el-form-item {
  margin-bottom: 0;
}

::v-deep .el-form--inline div.el-form-item {
  margin-right: 20px;
}

::v-deep .el-form-item__label {
  padding: 0 10px 0 0;
}

::v-deep .el-form-item__error {
  padding-top: 0;
  top: 90%;
  display: none;
}

.tg_margin_top_second {
  margin-top: 10px;
}

.tg-shadow--margin {
  margin-left: 6px;
  margin-right: 6px;
}

.tg-box--shadow {
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
}

.tg-table {
  width: 100%;
  border-radius: 4px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.tg-table__box {
  box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
  margin: 16px 6px 6px 6px;
  border-radius: 4px;
  position: relative;
  width: 100%;
  flex: 1;
}

.tg-box--border {
  width: calc(100% - 2px);
  height: 44px;
  border: 1px solid @base-color;
  border-radius: 4px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  pointer-events: none;
  box-shadow: 0 2px 0 0 @light-color;
}

button.tg-button__icon {
  padding-left: 12px;
  padding-right: 12px;
}

.tg-button__icon--normal {
  width: 10px;
  height: 11px;
  margin-right: 9px;
}

::v-deep .el-select .el-input .el-select__caret,
::v-deep .el-cascader {
  height: 32px;
  line-height: 32px;
}

.tg-button__icon--right {
  float: right;
}

::v-deep .el-dialog {
  border: 1px solid @base-color;
  border-radius: 4px;
}

::v-deep .el-dialog__header,
::v-deep .el-drawer__header {
  padding: 0;
  height: 55px;
  line-height: 55px;
  padding-left: 24px;
  position: relative;
  border-bottom: 1px solid #e9f0f7;
  font-weight: 600;

  &::after {
    content: "";
    position: absolute;
    background-color: @base-color;
    top: 17px;
    left: 16px;
    height: 20px;
    width: 2px;
    z-index: 1;
    border-radius: 2px;
  }

  .el-dialog__title,
  .dialog-title {
    font-family: @text-famliy_semibold;
    font-size: 16px;
    font-weight: 600;
  }
}

::v-deep .el-dialog__footer {
  padding: 11px 16px;
  border-top: 1px solid #e9f0f7;

  button.tg-button--primary,
  button.tg-button--plain {
    padding: 0 18px;
  }
}

::v-deep .el-drawer__header {
  margin: 0;
}

/**复选框**/
::v-deep .el-checkbox__label {
  font-family: @text-famliy_medium;
  font-weight: normal;
}

::v-deep .el-checkbox__input.is-checked+.el-checkbox__label {
  color: @text-color_first !important;
}

::v-deep .el-date-editor .el-range-separator,
::v-deep .el-date-editor .el-range__icon {
  height: 32px;
}

::v-deep .el-range-editor.el-input__inner {
  width: 270px;
}

::v-deep .el-date-editor .el-range-separator {
  width: 20px;
}

::v-deep .el-date-editor.is-active {
  &::after {
    content: "";
    position: absolute;
    top: -3px;
    left: -3px;
    border: 2px solid @light-color;
    border-radius: 6px;
    width: calc(100% + 2px);
    height: 32px;
    z-index: 3000;
  }
}

::v-deep .el-input__icon.el-icon-date {
  &::before {
    content: "";
    display: block;
    width: 12px;
    height: 12px;
    background: url("~@/assets/图片/icon_date.png");
    background-size: cover;
    position: absolute;
    top: 9px;
    left: 5px;
  }
}

::v-deep .el-input__icon.el-range__icon.el-icon-date {
  &::before {
    left: 14px;
  }
}

::v-deep .el-date-editor .el-range-input:first-of-type {
  margin-left: 12px;
}

::v-deep .el-date-editor .el-input__icon {
  line-height: 32px;
}

::v-deep .el-date-editor .el-range__close-icon {
  line-height: 25px;
  color: #98a9bf;
  text-align: right;
}

/**下拉框**/
.el-select-dropdown {
  border: solid 1px #2d80ed !important;
  margin-top: 0px !important;
  margin-bottom: 0px !important;

  .popper__arrow {
    display: none !important;
  }
}

/***日期选择***/
.el-picker-panel {
  border: solid 1px #2d80ed !important;
}

::v-deep.el-popper[x-placement^="bottom"] {
  margin-top: 0;
}

.el-popper[x-placement^=bottom] .popper__arrow{
  border-bottom-color: #2d80ed!important;
}
// /deep/ .el-input.is-focus::after {
//   border: 0 !important;
// }
::v-deep .el-select-dropdown {
  border: 1px solid #2d80ed !important;
  margin-top: 0px !important;
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

::v-deep .el-select .el-select-dropdown .popper__arrow {
  display: none;
}

::v-deep .el-select-dropdown__item.selected {
  background-color: @light-color;
  font-weight: normal;
}

::v-deep .el-select-dropdown__item.hover {
  background-color: @light-color;
  font-weight: normal;
}

::v-deep .el-select-dropdown__item {
  padding: 0;
  margin: 0 10px;
  border-radius: 4px;
  text-align: center;
}

::v-deep .el-select-dropdown.el-popper {
  width: inherit;
}

::v-deep .el-input.is-focus {
  &::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    border: 2px solid @light-color;
    border-radius: 6px;
    width: 100%;
    height: 32px;
    z-index: 3000;
  }
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner,
::v-deep .el-date-editor .el-range-input {
  &::placeholder {
    color: #8492a6;
  }

  &::-webkit-input-placeholder {
    /* WebKit browsers 适配谷歌 */
    color: #8492a6;
  }

  &:-moz-placeholder {
    /* Mozilla Firefox 4 to 18 适配火狐 */
    color: #8492a6;
    opacity: 1;
  }

  &::-moz-placeholder {
    /* Mozilla Firefox 19+ 适配火狐 */
    color: #8492a6;
    opacity: 1;
  }

  &:-ms-input-placeholder {
    /* Internet Explorer 10+  适配ie*/
    color: #8492a6;
  }
}

::v-deep .el-input.is-disabled .el-input__inner {
  border-color: transparent;
}

::v-deep .el-input.is-disabled:hover,
::v-deep .el-select .el-input.is-disabled:hover {
  .el-input__inner {
    border-color: @base-color;
  }
}

::v-deep .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap>.el-form-item__label:before,
::v-deep .el-form-item.is-required:not(.is-no-asterisk)>.el-form-item__label:before {
  color: #ff0317;
}

::v-deep .el-radio__label {
  font-family: @text-famliy_medium;
  font-size: @text-size_normal;
  color: @text-color_second;
  font-weight: normal;
}

::v-deep .el-radio__label {
  padding-left: 12px;
}

::v-deep .el-radio__inner::after {
  width: 6px;
  height: 6px;
}

::v-deep .el-radio__input.is-checked .el-radio__inner::after {
  background-color: @base-color;
}

::v-deep .el-radio__input.is-checked .el-radio__inner {
  border-color: #bac8d9;
  background-color: transparent;
}

//表格固定
::v-deep .el-table__fixed {
  padding-right: 16px;

  .el-table__fixed-header-wrapper,
  .el-table__fixed-body-wrapper {
    left: 16px;
  }
}

::v-deep .el-table__fixed-right {
  padding-left: 16px;
  box-sizing: border-box;
  background-color: #fff;

  .el-table__fixed-header-wrapper,
  .el-table__fixed-body-wrapper {
    right: 16px;
  }

  th:last-child .cell,
  td:last-child .cell {
    padding-left: 26px;
  }
}

::v-deep .el-input__icon {
  height: 32px;
  line-height: 32px;
}

::v-deep .el-date-editor.el-date-editor--date:focus-within,
::v-deep .el-date-editor.el-date-editor--time:focus-within {
  &::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    border: 2px solid @light-color;
    border-radius: 6px;
    width: 100%;
    height: 32px;
    z-index: 3000;
  }
}

button.tg-text--red {
  color: @base-red;
  &:hover {
    color: @base-red;
    opacity: 0.8;
  }
}

/**树箭头*/
::v-deep .el-tree-node__expand-icon,
::v-deep .el-table__expand-icon {
  -webkit-transform: rotate(0deg);
  transform: rotate(0deg);
}

::v-deep .el-tree-node__expand-icon::before,
::v-deep .el-table__expand-icon i::before {
  content: "1";
  visibility: hidden;
}

::v-deep .el-tree-node__expand-icon {
  background: url("~@/assets/图片/icon_plus.png");
  background-size: cover;
  width: 16px;
  height: 16px;
  margin-right: 16px;
  margin-left: 13px;
  padding: 0;
}

::v-deep .el-table__expand-icon i {
  background: url("~@/assets/图片/icon_plus.png");
  background-size: cover;
  width: 14px;
  height: 14px;
  vertical-align: text-top;
  margin-top: 1px;
}

::v-deep .el-table__expand-icon {
  width: 14px;
  height: 14px !important;
  line-height: 14px !important;
}

::v-deep .el-tree-node.is-expanded .el-tree-node__expand-icon.el-icon-caret-right:before,
::v-deep .el-table__expand-icon--expanded i:before {
  content: "1";
  visibility: hidden;
}

::v-deep .el-tree-node.is-expanded .expanded.el-tree-node__expand-icon.el-icon-caret-right,
::v-deep .el-table__expand-icon--expanded i {
  background: url("~@/assets/图片/icon_minus.png");
  background-size: cover;
}

::v-deep .el-tree-node__expand-icon.is-leaf {
  visibility: hidden;
}

::v-deep .el-tree-node:focus>.el-tree-node__content {
  background-color: transparent !important;
}

.tg-text--black {
  color: @text-color_second;
}

/**tab**/
::v-deep .el-tabs__nav-wrap::after {
  background-color: transparent;
}

::v-deep .el-tabs__item:focus.is-active.is-focus:not(:active) {
  -webkit-box-shadow: none !important;
  box-shadow: none !important;
}

/**el-dialog*/
::v-deep .el-dialog,
::v-deep .el-drawer__wrapper {
  .el-icon-close:before {
    content: "1";
    visibility: hidden;
  }

  .el-icon-close {
    background-image: url("~@/assets/图片/icon_close.png");
    width: 12px;
    height: 12px;
    background-size: cover;

    &:hover {
      background-image: url("~@/assets/图片/icon_close_ac.png");
    }
  }
}

// ::v-deep .el-dialog__wrapper {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   justify-content: center;

//   .el-dialog__body {
//     max-height: 570px;
//     overflow: auto;
//   }
// }
.el-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 80px);
  max-width: calc(100% - 30px);
  border: 1px solid @base-color;
  border-radius: 4px;

  .el-select-dropdown {
    position: absolute !important;
    left: 0 !important;
    top: 32px !important;
  }

  .el-dialog__header,
  .el-drawer__header {
    padding: 0;
    height: 55px;
    line-height: 55px;
    padding-left: 24px;
    position: relative;
    border-bottom: 1px solid #e9f0f7;
    font-weight: 600;

    &::after {
      content: "";
      position: absolute;
      background-color: @base-color;
      top: 17px;
      left: 16px;
      height: 20px;
      width: 2px;
      z-index: 1;
      border-radius: 2px;
    }

    .el-dialog__title,
    .dialog-title {
      font-family: @text-famliy_semibold;
      font-size: 16px;
      font-weight: 600;
    }
  }

  ::v-deep .el-dialog__footer {
    padding: 11px 16px;
    border-top: 1px solid #e9f0f7;

    button.tg-button--primary,
    button.tg-button--plain {
      padding: 0 18px;
    }
  }
}

::v-deep .el-dialog__body {
  overflow: auto;
}

/**操作...**/
::v-deep .tg-table--operate {
  cursor: pointer;

  &:before {
    content: "";
    width: 16px;
    height: 4px;
    margin-left: 4px;
    vertical-align: middle;
    display: inline-block;
    background-image: url("~@/assets/图片/icon_more.png");
    background-size: cover;
  }

  &:hover:before {
    content: "";
    background-image: url("~@/assets/图片/icon_more_ac.png");
  }
}

/**el-select多个*/
::v-deep .el-select {
  .el-select__tags {
    overflow: scroll;
    box-sizing: border-box;
    max-width: unset;
    flex-wrap: unset;
  }

  .el-tag.el-tag--info {
    background-color: @light-color;
    border-radius: 4px;
    border-color: @base-color;
    height: 22px;
    line-height: 20px;
    display: inline-block;
    color: @text-color_second;
    font-family: @text-famliy_medium;
    font-size: @text-size_normal;
    padding-right: 30px;
    padding-left: 0;
    margin-top: 0;
    margin-bottom: 0;

    .el-select__tags-text {
      padding-left: 16px;
    }

    .el-tag__close.el-icon-close {
      background-image: url("~@/assets/图片/icon_close_green.png");
      width: 18px;
      height: 18px;
      color: transparent;
      right: -16px;
      display: inline-block;
      background-size: cover;
    }
  }
}

.tg-header__sub-title {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 6px;
  margin-right: 6px;
  height: 46px;
  line-height: 46px;
  border: 1px solid @base-color;
  border-radius: 4px;
  padding-left: 16px;
  background-color: #fff;

  img {
    width: 12px;
    height: 12px;
    margin-right: 10px;
  }

  span {
    color: @text-color_second;
    font-family: @text-famliy_medium;
    font-size: @text-size_normal;
  }
}

button.tg-span__divide-line {
  cursor: pointer;
  position: relative;

  &:after {
    content: "";
    background: #cbcfda;
    width: 1px;
    height: 13px;
    position: absolute;
    top: 9px;
    margin-left: 9px;
  }
}

button:last-child.tg-span__divide-line {
  &::after {
    background: transparent;
  }
}

button.tg-table__name--ellipsis {
  width: 102%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  text-align: left;
}

.tg_ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tg-header__subtitle {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0 6px 0 6px;
  height: 46px;
  line-height: 46px;
  border: 1px solid @base-color;
  border-radius: 4px;
  padding-left: 16px;
  background-color: #fff;

  img {
    width: 12px;
    height: 12px;
    margin-right: 10px;
  }

  span {
    color: @text-color_second;
    font-family: @text-famliy_medium;
    font-size: @text-size_normal;
  }
}

.tg-tab {
  ::v-deep .el-tabs__header {
    margin: 0;
    background: #fff;
    padding: 0 16px;
    box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    border-radius: 4px;
  }

  ::v-deep .el-tabs__item {
    height: 46px;
    line-height: 46px;
  }

  ::v-deep .el-tab-pane {
    margin: 0 6px;
  }

  ::v-deep .el-tab-pane:first-child {
    margin: 0;
  }
}

.tg-table--dark {
  ::v-deep .el-table {
    .el-table__header th {
      background: #f5f8fc;
    }
  }

  .tg-box--border {

    &::after,
    &::before {
      content: "";
      width: 16px;
      height: 44px;
      background: #f5f8fc;
      position: absolute;
      top: 0;
    }

    &::before {
      left: 0;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    &::after {
      right: 0;
      border-bottom-right-radius: 4px;
      border-top-right-radius: 4px;
    }
  }
}

/*隐藏bpmn.js水印**/
.bjs-powered-by {
  display: none !important;
}

.tg-select--dialog {
  ::v-deep .el-input__suffix {
    display: flex;
    flex-direction: row;
    align-items: center;

    .el-input__suffix-inner {
      display: flex;
    }
  }

  .btn__img--dotted {
    width: 16px;
    height: 4px;
    cursor: pointer;
  }

  ::v-deep .el-input__suffix {
    .btn__img--dotted {
      margin-right: 10px;
    }
  }
}

.tg-select {
  cursor: pointer;

  &:hover {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 100%;
      height: 32px;
      left: -2px;
      top: -2px;
      border: none;
      border-radius: 6px;
      z-index: 10;
    }

    ::v-deep .el-input__inner {
      border-color: @base-color;
    }
  }
}

::v-deep .el-select-dropdown__empty {
  color: @text-color_third;
}

/**单选对号**/
.radio--tick {
  ::v-deep .el-radio__inner {
    width: 16px;
    height: 16px;
  }
}

::v-deep .radio--tick.is-checked .el-radio__input .el-radio__inner {
  background-color: @base-color;
  border: none;
}

::v-deep .radio--tick.is-checked .el-radio__inner::after {
  content: "";
  background: none;
  width: 7px;
  height: 3px;
  border: 2px solid white;
  border-top: transparent;
  border-right: transparent;
  text-align: center;
  display: block;
  position: absolute;
  top: 4px;
  left: 4px;
  transform: rotate(-45deg);
  border-radius: 0px;
}
::v-deep .radio--tick .el-radio__input.is-disabled .el-radio__inner {
  border: none;
}
::v-deep .el-table__fixed-footer-wrapper {
  left: 16px;
}
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover{
  color: #2d80ed;
  font-weight: 600;
}
.message-success .el-notification__title{
  color: #5cb87a;
}
.operationLog .popper__arrow::after {
  top: 0px!important;
  border-bottom-color: #2d80ed!important;
}
/**第三方插件vue-photo-preview图片预览样式重置*/
.pswp {
  z-index: 6666 !important;
}