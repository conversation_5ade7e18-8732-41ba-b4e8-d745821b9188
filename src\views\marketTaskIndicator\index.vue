<template>
  <div class="student-analysis">
    <div class="student-tab">
      <span
        v-for="(item, index) in menuList"
        v-has="{ m: 'marketTaskIndicator', o: item.rule }"
        :key="index"
        :class="{ 'student-tab--active': tab === index + 1 }"
        @click="changeTab(index)"
        >{{ item.name }}</span
      >
    </div>
    <conversionList
      v-has="{ m: 'marketTaskIndicator', o: 'conversionList' }"
      v-show="tab == 1"
    ></conversionList>
    <indicatorList
      v-has="{ m: 'marketTaskIndicator', o: 'indicatorList' }"
      v-show="tab == 2"
    ></indicatorList>
  </div>
</template>
<script>
import conversionList from "./conversionList.vue";
import indicatorList from "./indicatorList.vue";
export default {
  data() {
    return {
      menuList: [
        {
          name: "市场周转化报表",
          rule: "conversionList"
        },
        {
          name: "市场任务指标",
          rule: "indicatorList"
        }
      ],
      tab: 1
    };
  },
  created() {
    for (let k = 0; k < this.menuList.length; k++) {
      if (this.$_has({ m: "marketTaskIndicator", o: this.menuList[k].rule })) {
        this.tab = k + 1;
        break;
      }
    }
  },
  methods: {
    changeTab(i) {
      this.tab = i + 1;
    }
  },
  components: {
    conversionList,
    indicatorList
  }
};
</script>
<style lang="less">
.student-analysis {
  .student-tab {
    margin-left: -10px;
    // border-top: 1px solid #e0e6ed;
    left: 0;
    height: 46px;
    background: #fff;
    padding: 0 16px;
    box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    width: calc(100% + 20px);
    box-sizing: border-box;
    span {
      font-family: @text-famliy_semibold;
      color: @text-color_second;
      font-size: @text-size_normal;
      display: inline-block;
      height: 44px;
      border-bottom: 2px solid transparent;
      line-height: 44px;
      cursor: pointer;
      font-weight: bold;
    }

    span + span {
      margin-left: 32px;
    }

    span.student-tab--active {
      color: @base-color;
      border-color: @base-color;
    }
  }
}
</style>
