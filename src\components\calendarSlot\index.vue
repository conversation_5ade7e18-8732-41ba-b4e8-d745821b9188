<template>
  <div class="calendar-index">
    <calendar-com
      ref="calendarCom"
      @toggle-show="calendarToggleShow"
      @change-board="calendarChangeBoard"
    >
      <template slot="dateCell" slot-scope="{ date, data }">
        <div
          :class="data.isSelected ? 'is-selected' : ''"
          class="calendar-cell-box"
          @click="calendarToggleClick(date, data)"
        >
          <div class="calendar-bg-box">
            <div
              v-if="!data.isSelected"
              :class="{
                'calendar-work-style': calendarObj[data.day] === '休',
                'calendar-no-work': calendarObj[data.day] === '工'
              }"
            >
              {{ Number(data.day.split("-").slice(2).join("-")) }}
            </div>
            <div
              v-else-if="data.isSelected"
              :class="{
                'calendar-work-style': calendarCurTxt === '休',
                'calendar-no-work': calendarCurTxt === '工'
              }"
            >
              {{ Number(data.day.split("-").slice(2).join("-")) }}
            </div>
          </div>
        </div>
      </template>
    </calendar-com>
    <div class="calendar-checked-data">
      <div class="checked-course">
        <em></em>
        <span
          >已选择排课记录：<span class="weight">{{ checked_schedule_num }}</span
          >条</span
        >
      </div>
      <div class="checked-date-list">
        <em></em>
        已选择目标日期：<span class="weight">{{
          checked_date_list.length
        }}</span
        >天
      </div>
      <div class="date-list-box">
        <span>{{ checked_date_list_sort.join("、") }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import CalendarCom from "./calendar.vue";
export default {
  components: {
    CalendarCom
  },
  data() {
    return {
      calendarDialogVisible: false,
      calendarObj: {},
      calendarCurTxt: "",
      calendarDateIsDisabled: false,
      checked_date_list: [],
      checked_date_list_sort: []
    };
  },
  props: {
    checked_schedule_num: {
      type: Number,
      default: 0
    },
    multiSelect: {
      type: Boolean,
      default: false
    }
  },
  computed: {},
  watch: {},
  methods: {
    showClick() {
      this.calendarDialogVisible = true;
    },
    calendarToggleClick(date, data) {
      // 不是多选
      if (!this.multiSelect) {
        this.calendarObj = {};
      }
      // 有值的时候，点击的时候，清空
      if (
        this.calendarObj[data.day] ||
        (this.calendarObj[data.day] && this.calendarObj[data.day] !== "")
      ) {
        delete this.calendarObj[data.day];
      } else {
        const week = this.moment(data.day).format("d");
        if (week === "0" || week === "6") {
          this.calendarObj[data.day] = "休";
        } else {
          this.calendarObj[data.day] = "工";
        }
      }
      this.checked_date_list = Object.keys(this.calendarObj);
      this.checked_date_list_sort = this.checked_date_list.sort(
        (a, b) => new Date(a) - new Date(b)
      );
      this.calendarCurTxt = this.calendarObj[data.day];
      this.$emit("change", this.checked_date_list_sort);
    },
    calendarToggleShow(val) {
      this.calendarDateIsDisabled = val;
    },
    calendarChangeBoard(val) {
      // this.calendarObj = {};
      // this.calendarCurTxt = "";
      console.log("calendarChangeBoard", val);
    },
    calendarDialogClose() {
      if (this.$refs.calendarCom.configBtnObj.editClicked) {
        this.calendarDialogVisible = true;
        // this.$message({message: '请先保存当前配置内容！', customClass: 'default-custom-msg'});
      } else {
        this.calendarDialogVisible = false;
        this.calendarObj = {};
        this.calendarCurTxt = "";
      }
    }
  }
};
</script>
