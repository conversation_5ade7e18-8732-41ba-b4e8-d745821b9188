<!--投放费用-->
<template>
  <div class="delivery__content">
    <el-dialog
      :visible="true"
      v-if="dialogVisible"
      width="1016px"
      :before-close="handleClose"
    >
      <div slot="title" class="dialog-title">
        <span>渠道费用投放记录-</span>
        <span class="special">{{ deliveryName }}</span>
      </div>
      <div class="tg-box--margin">
        <el-form ref="form" :model="form" :inline="true">
          <el-form-item label="投放时间">
            <el-date-picker
              v-model="form.putInDate"
              type="daterange"
              range-separator="-"
              start-placeholder="起始时间"
              end-placeholder="结束时间"
              popper-class="tg-date-picker tg-date--range"
              :pickerOptions="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="记录创建日期">
            <el-date-picker
              v-model="form.createdTime"
              type="daterange"
              range-separator="-"
              start-placeholder="起始时间"
              end-placeholder="结束时间"
              popper-class="tg-date-picker tg-date--range"
              :pickerOptions="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="投放金额范围" class="no-margin">
            <div
              class="tg-input--range el-input__inner"
              :class="{ 'border--active': borderFlag }"
            >
              <img src="../../assets/图片/icon_money.png" class="icon" />
              <el-input-number
                v-model="form.moneyRangeMin"
                placeholder="最小值"
                autocomplete="off"
                @focus="focus"
                @blur="blur"
                :controls="false"
                :min="0"
              />
              <span>-</span>
              <el-input-number
                v-model="form.moneyRangeMax"
                placeholder="最大值"
                autocomplete="off"
                @focus="focus"
                @blur="blur"
                :min="form.moneyRangeMin"
                :controls="false"
              />
            </div>
          </el-form-item>
          <el-form-item class="tg-box--margin">
            <el-button
              type="primary"
              @click="search()"
              class="tg-button--primary tg-button__icon"
            >
              <img
                src="../../assets/图片/icon_search.png"
                alt=""
                class="tg-button__icon--normal"
              />查询</el-button
            >
            <el-button
              type="plain"
              @click="reset()"
              class="tg-button--plain tg-button__icon"
            >
              <img
                src="../../assets/图片/icon_reset_ac.png"
                alt=""
                class="tg-button__icon--normal"
              />重置</el-button
            >
            <el-button
              type="primary"
              class="tg-button--primary tg-button__icon"
              @click="openDialog()"
              v-has="{ m: 'channelfee', o: 'create' }"
            >
              <img
                src="../../assets/图片/icon_add_white.png"
                alt=""
                class="tg-button__icon--normal"
              />新增</el-button
            >
          </el-form-item>
        </el-form>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            :data="deliveryData"
            v-loading="deliveryIsLoading"
            :height="410"
            class="tg-table tg-table--grey"
          >
            <el-table-column width="100" label="序号">
              <template slot-scope="scope">
                <span v-if="!deliveryIsLoading">
                  {{
                    Number(scope.$index) +
                    1 +
                    Number((currentPage - 1) * pageSize)
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="item.label"
              :prop="item.props"
              :width="item.width"
              v-for="(item, index) in deliveryTitle"
              :key="index"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span v-if="scope.column.property === 'putInTime'"
                  >{{ scope.row.timebegin | dataFormat(item) }}至{{
                    scope.row.timeend | dataFormat(item)
                  }}</span
                >
                <span v-else>{{
                  scope.row[scope.column.property] | dataFormat(item)
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <span
                  class="tg-text--error tg-button__del"
                  slot="reference"
                  @click="del(scope.row.id)"
                  v-has="{ m: 'channelfee', o: 'delete' }"
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="tg-pagination">
            <span class="el-pagination__total">共 {{ total }} 条</span>
            <el-pagination
              @size-change="sizeChange"
              @current-change="currentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="pageSize"
              layout="sizes, prev, pager, next, jumper"
              :total="total"
              background
              popper-class="tg-pagination--select"
            ></el-pagination>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" class="tg-button--plain"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="handleClose"
          class="tg-button--primary"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { picker_options } from "@/public/datePickerOptions";
export default {
  data() {
    return {
      form: {},
      borderFlag: false,
      pageSize: 10,
      currentPage: 1,
      deliveryTitle: [
        { props: "fee", label: "费用（元）", type: "input" },
        { props: "putInTime", label: "投放时间", type: "date", width: 200 },
        { props: "created_at", label: "创建时间", type: "datetime" },
        { props: "name", label: "备注", type: "input" }
      ], // 一，二级渠道投放费用表头
      deliveryIsLoading: false,
      deliveryName: "",
      deliveryData: [],
      total: 1,
      pickerOptions: picker_options
    };
  },
  filters: {
    dataFormat(value, item) {
      // let d, MM, y, date;
      // switch (item.type) {
      //   case "date":
      //     date = new Date(value);
      //     y = date.getFullYear();
      //     MM = date.getMonth() + 1;
      //     MM = MM < 10 ? "0" + MM : MM;
      //     d = date.getDate();
      //     d = d < 10 ? "0" + d : d;
      //     return y + "-" + MM + "-" + d;
      //   case "datetime":
      //     date = new Date(value);
      //     y = date.getFullYear();
      //     MM = date.getMonth() + 1;
      //     MM = MM < 10 ? "0" + MM : MM;
      //     d = date.getDate();
      //     d = d < 10 ? "0" + d : d;
      //     let h = date.getHours();
      //     h = h < 10 ? "0" + h : h;
      //     let m = date.getMinutes();
      //     m = m < 10 ? "0" + m : m;
      //     let s = date.getSeconds();
      //     s = s < 10 ? "0" + s : s;
      //     return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
      //   default:
      //     return value;
      // }
      function formatDate(date) {
        const y = date.getFullYear();
        const MM = date.getMonth() + 1;
        const d = date.getDate();
        const h = date.getHours();
        const m = date.getMinutes();
        const s = date.getSeconds();
        return `${y}-${MM < 10 ? "0" + MM : MM}-${d < 10 ? "0" + d : d} ${
          h < 10 ? "0" + h : h
        }:${m < 10 ? "0" + m : m}:${s < 10 ? "0" + s : s}`;
      }
      switch (item.type) {
        case "date":
          return formatDate(new Date(value));
        case "datetime":
          return formatDate(new Date(value));
        default:
          return value;
      }
    }
  },
  computed: {
    dialogVisible() {
      return this.$store.getters.doneGetDeliveryFlag;
    },
    fee_list() {
      return this.$store.getters.doneGetFeeList;
    },
    fee_success() {
      return this.$store.getters.doneGetFeeStatus;
    },
    deliveryId() {
      return this.$store.getters.doneGetDeliveryId;
    },
    deliveryParentOrSon() {
      return this.$store.getters.doneGetDeliveryParentOrSon;
    },
    channel_info() {
      return this.$store.getters.doneGetChannelInfo;
    },
    subchannel_info() {
      return this.$store.getters.doneGetSubChannelInfo;
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.currentPage = 1;
        this.getDeliveryList();
      }
    },
    fee_success(new_bool) {
      if (new_bool != null) {
        this.getDeliveryList();
      }
    },
    fee_list(data) {
      this.total = data.count;
      this.deliveryData = [...data.results];
      this.deliveryIsLoading = false;
    },
    currentPage() {
      this.getDeliveryList();
    },
    pageSize() {
      this.getDeliveryList();
    },
    channel_info(data) {
      this.form = data.reChannell;
      if (
        data.reChannell.channelsource === undefined ||
        data.reChannell.channelsource === null
      )
        this.$set(this.form, "channelsource", "");
      this.deliveryName = data.reChannell.name;
    },
    subchannel_info(data) {
      this.form = data.resubchannell;
      this.deliveryName = data.resubchannell.name;
    }
  },
  methods: {
    handleClose() {
      this.$store.commit("setDeliveryFlag", false);
      this.initData();
    },
    openDialog() {
      this.$store.commit("setOperate", 7);
      this.$store.commit("setDialogFlag", true);
    },
    currentChange(val) {
      this.currentPage = val;
    },
    sizeChange(val) {
      this.pageSize = val;
    },
    search() {
      this.getDeliveryList();
    },
    initData() {
      this.form = {};
    },
    focus() {
      this.borderFlag = true;
    },
    blur() {
      this.borderFlag = false;
    },
    // 重置
    reset() {
      this.form = {};
      this.getDeliveryList();
    },
    del(id) {
      this.$confirm("此操作将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.$store.commit("setFeeStatus", "");
        this.$store.dispatch("delChannelFee", { data: { id } });
        if (this.deliveryData.length === 1 && this.currentPage !== 1)
          this.currentPage = this.currentPage - 1;
      });
    },
    // 获取费用列表
    getDeliveryList() {
      const obj =
        this.deliveryParentOrSon === 0
          ? { channelid: this.deliveryId.id }
          : { subchannelid: this.deliveryId.subId };
      this.deliveryIsLoading = true;
      let params = {
        pageSize: this.pageSize,
        page: this.currentPage,
        ...obj
      };
      if (Object.keys(this.form).length !== 0) {
        let searchParams = {
          feemax:
            this.form.moneyRangeMax === undefined
              ? ""
              : Number(this.form.moneyRangeMax),
          feemin:
            this.form.moneyRangeMin === undefined
              ? ""
              : Number(this.form.moneyRangeMin)
        };
        if (this.form.putInDate)
          searchParams = {
            ...searchParams,
            timebegin: this.form.putInDate[0],
            timeend: this.form.putInDate[1]
          };
        if (this.form.createdTime)
          searchParams = {
            ...searchParams,
            createdbegin: this.form.createdTime[0],
            createdend: this.form.createdTime[1]
          };
        params = { ...params, ...searchParams };
      }
      this.$store.dispatch("getChannelFeeByChannelId", { params });
    }
  }
};
</script>
<style lang="less">
.tg-pagination--select {
  margin-top: -1px !important;
  .el-select-dropdown {
    border: solid 1px #2d80ed !important;
  }
  .el-input.is-focus::after {
    border: 0 !important;
  }
  .el-select-dropdown {
    border: 1px solid #2d80ed !important;
    margin-top: -1px !important;
  }
  .el-select-dropdown__item.selected {
    background-color: #ebf4ff;
    font-weight: normal;
  }
  .el-select-dropdown__item.hover {
    background-color: #ebf4ff;
    font-weight: normal;
  }
  .el-select-dropdown__item {
    padding: 0;
    margin: 0 10px;
    border-radius: 4px;
    text-align: center;
  }
  .popper__arrow {
    display: none !important;
  }
}
</style>

<style lang="less" scoped>
@baseColor: #2f7fff;
@errColor: #fd5461;
.tg-text--error {
  color: @errColor;
}
.special {
  color: @base-color;
}
input {
  appearance: none;
  border: none;
  outline: 0;
  padding: 0;
  text-align: center;
  display: inline-block;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none !important;
}
input[type="number"] {
  -moz-appearance: textfield;
}
.delivery__content {
  .tg-input--range {
    border: 1px solid #d3dce6;
    width: 216px;
    display: flex;
    flex-direction: row;
    align-items: center;
    .icon {
      width: 12px;
      height: 12px;
    }
    ::v-deep .el-input {
      width: 88px;
      .el-input__inner {
        position: absolute;
      }
    }
    ::v-deep .el-input-number.is-without-controls .el-input__inner {
      padding-left: 12px;
      padding-right: 12px;
    }
    ::v-deep
      .el-input-number.is-without-controls:nth-child(2)
      .el-input__inner {
      padding-left: 0;
    }
    ::v-deep .el-input__inner {
      border: none;
      background: transparent;
      text-align: center;
      font-size: 14px;
    }
    ::v-deep .el-input-number {
      height: 32px;
      line-height: 32px;
    }
  }
  ::v-deep .el-range-editor.el-input__inner {
    width: 240px;
  }
  .tg-button__del {
    cursor: pointer;
  }
  .border--active {
    border: 1px solid @base-color;
  }
  ::v-deep .el-form-item__label,
  ::v-deep .el-form-item__content {
    line-height: 32px;
  }
  ::v-deep .el-form {
    width: 982px;
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px;
    height: 573px;
  }
  ::v-deep .el-form--inline div.el-form-item {
    margin-right: 16px;
  }
  ::v-deep .el-form--inline div.el-form-item.no-margin {
    margin-right: 0;
  }
  ::v-deep .tg-table__box {
    margin: 16px 0 0 0;
  }
  ::v-deep .tg-table--grey {
    border-bottom: 1px solid #e9f0f7;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    &:before,
    &:after {
      background: #f5f8fc;
      content: "";
      height: 48px;
      width: 16px;
      position: absolute;
      left: 0;
      top: 0;
    }
    &:before {
      right: 0;
      bottom: unset;
      left: unset;
    }
    th {
      background: #f5f8fc;
    }
    .el-table__body tr.current-row > td {
      background-color: #ebf4ff;
    }
  }
}
</style>
