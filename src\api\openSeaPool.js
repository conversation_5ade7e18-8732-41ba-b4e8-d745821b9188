import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 全国
export const openSeaList = (data) => {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/market-service/open-sea/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 全国
export const openSeaAdvisorList = (data) => {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/market-service/open-sea/advisor-list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
// 全国
export const groupMemberListLeader = (data) => {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(
      `/api/organization-service/employee-group/group-member-list/leader?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

export const openSeaAdvisorAdd = (data) => {
  return axios
    .post(`/api/market-service/open-sea/advisor-add`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      console.log(error);
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
};
