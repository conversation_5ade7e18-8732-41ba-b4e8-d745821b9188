import { fetchGet } from "../fetch";
import qs from "qs";
// 退学学员列表
function getDropStudentLogs(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/teacher/drop-student-logs?${new_data}`);
}
// 学员列表
function getStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/student-service/teacher/students?${new_data}`);
}
// 试听学员列表
function getAuditionStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/school-service/teacher/audition-students?${new_data}`);
}
// 试听转化率
function getAuditionTransformRate(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/school-service/teacher/audition-transform-rate?${new_data}`
  );
}
// 教练任务池 业绩汇总列表 课程维度
function getCoachTaskPool(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/deduct/coach-task-pool?${new_data}`
  );
}
// 教练任务池 业绩汇总列表 课程维度 合计
function getCoachTaskPoolTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/deduct/coach-task-pool-total?${new_data}`
  );
}
// 任课老师出勤率
function getTeacherPresentRatio(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/deduct/teacher-present-ratio?${new_data}`
  );
}
// 任课老师出勤率 合计
function getTeacherPresentRatioTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/deduct/teacher-present-ratio-total?${new_data}`
  );
}
//  转化明细表
function getCoachTaskPoolTransformStudent(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/teacher/transform-students?${new_data}`
  );
}

export default {
  getDropStudentLogs,
  getStudentList,
  getAuditionStudentList,
  getAuditionTransformRate,
  getCoachTaskPool,
  getCoachTaskPoolTotal,
  getTeacherPresentRatio,
  getTeacherPresentRatioTotal,
  getCoachTaskPoolTransformStudent
};
