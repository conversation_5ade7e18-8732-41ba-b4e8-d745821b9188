// 补课
import axios from "../http";
import Vue from "vue";
import qs from "qs";

function transferSchoolList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/customer/transfer-school-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async transferSchoolList(data) {
    return transferSchoolList(data);
  }
};
