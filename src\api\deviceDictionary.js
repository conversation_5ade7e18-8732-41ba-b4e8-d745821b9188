import axios from "../http";
import Vue from "vue";
import qs from "qs";
const fetch = require("../fetch");
// 更新设备信息
function deviceUpdate(data) {
  return fetch.fetchPatch(
    `/api/student-service/device/update?id=${data.id}`,
    data
  );
}
// 获取设备列表
function deviceList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/student-service/device/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除设备
function deviceDelete(data) {
  return fetch.fetchDel(`/api/student-service/device/delete`, {
    params: data
  });
}
// 获取设备信息
function deviceInfo(data) {
  return axios
    .get(`/api/student-service/device/info`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取设备类别
function deviceCategoryList(data) {
  return axios
    .get(`/api/student-service/device-category/list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新设备类别
function deviceCategoryUpdate(data) {
  return fetch.fetchPatch(
    `/api/student-service/device-category/update?id=${data.id}`,
    data
  );
}
// 获取类别信息
function deviceCategoryInfo(data) {
  return axios
    .get(`/api/student-service/device-category/info`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除设备类别
function deviceCategoryDelete(data) {
  return fetch.fetchDel(`/api/student-service/device-category/delete`, {
    params: data
  });
}
// 新增类别
function deviceCategoryAdd(data) {
  return fetch.fetchPost(`/api/student-service/device-category/create`, data);
}
export default {
  async DeviceList(data) {
    return deviceList(data);
  },
  async DeviceUpdate(data) {
    return deviceUpdate(data);
  },
  async DeviceDelete(data) {
    return deviceDelete(data);
  },
  async DeviceInfo(data) {
    return deviceInfo(data);
  },
  async DeviceCategoryList(data) {
    return deviceCategoryList(data);
  },
  async DeviceCategoryUpdate(data) {
    return deviceCategoryUpdate(data);
  },
  async DeviceCategoryInfo(data) {
    return deviceCategoryInfo(data);
  },
  async DeviceCategoryDelete(data) {
    return deviceCategoryDelete(data);
  },
  async DeviceCategoryAdd(data) {
    return deviceCategoryAdd(data);
  }
};
