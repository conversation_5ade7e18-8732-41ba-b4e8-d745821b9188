import axios from "../http";
import Vue from "vue";
import qs from "qs";
// import { Notification } from "element-ui";

// 创建员工
function getCreateEmployee(data) {
  return axios
    .post(`/api/organization-service/employee/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除员工
function deleteEmployee(data) {
  return axios
    .delete(`/api/organization-service/employee/delete?employee_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 单个员工信息
function getEmployeeInfo(data) {
  return axios
    .get(`/api/organization-service/employee/info?employee_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function getEmployeeList(data) {
  return axios
    .get(`/api/organization-service/employee/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function updateEmployee(data) {
  return axios
    .patch(
      `/api/organization-service/employee/update?employee_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 员工禁用启用
function employeeOpenClose(data) {
  return axios
    .get(
      `/api/organization-service/employee/open-or-close?employee_id=${data.id}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 启用账号
function updateEmployeeStatus(data) {
  return axios
    .put(`/api/v1/Employeee/updateEmployeeStatus`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导出员工
function getEmployeeExport(data) {
  return axios
    .get(`/api/organization-service/employee/manage-export`, {
      params: { ...data, exportData: 1 }
    })

    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导入员工
function getEmployeeImport(data) {
  return axios
    .post(`/api/organization-service/employee/import`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 员工的角色信息
function getEmployeeRoleInfo(data) {
  return axios
    .get(`/api/permission-service/employee/info?employee_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新员工角色信息
function updateEmployeeRole(data) {
  return axios
    .post(
      `/api/permission-service/employee/update?employee_id=${data.id}`,
      data.data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取员工所有权限
function getEmployeeAllPermission(data) {
  return axios
    .get(`/api/permission-service/employee/all-permission`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return Promise.reject(error);
    })
    .finally();
}
// 获取员工树
function getStaffTreeList(data) {
  return axios
    .get(`/api/organization-service/employee/overview`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取员工树-只包含主岗
function getStaffFullTreeList(data) {
  return axios
    .get(`/api/organization-service/employee/overview-full`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 获取员工树-只包含主岗
function getEmployeePreview(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/organization-service/employee/preview?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}

// 获取审批红点
function approveRedCircle(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/workstream-service/approve/statistics?${new_data}`)
    .then((res) => {
      return res;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 同步企业微信
function syncWechatWork(data) {
  return axios
    .get(`/api/organization-service/employee/sync-corp-wechat`, {
      params: data
    })
    .then((res) => {
      return res.data;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 获取员工岗位-树形数据
function getEmployeePostTree(data) {
  return axios
    .get(`/api/organization-service/employee/overview-post`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
// 解绑openid
function unbindEmployee(data) {
  return axios
    .post(
      `/api/organization-service/employee/unbindEmployee?employee_id=${data.employee_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取员工管理列表
function getEmployeeManageList(data) {
  return axios
    .get(`/api/organization-service/employee/manage-list`, { params: data })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.message);
    })
    .finally();
}
export default {
  async GetCreateEmployee(data) {
    return getCreateEmployee(data);
  },
  async DeleteEmployee(data) {
    return deleteEmployee(data);
  },
  async GetEmployeeInfo(data) {
    return getEmployeeInfo(data);
  },
  async GetEmployeeList(data) {
    return getEmployeeList(data);
  },
  async GetEmployeeManageList(data) {
    return getEmployeeManageList(data);
  },
  async UpdateEmployee(data) {
    return updateEmployee(data);
  },
  async UpdateEmployeeStatus(data) {
    return updateEmployeeStatus(data);
  },
  async GetEmployeeExport(data) {
    return getEmployeeExport(data);
  },
  async GetEmployeeImport(data) {
    return getEmployeeImport(data);
  },
  async EmployeeOpenClose(data) {
    return employeeOpenClose(data);
  },
  async getEmployeeRoleInfo(data) {
    return getEmployeeRoleInfo(data);
  },
  async updateEmployeeRole(data) {
    return updateEmployeeRole(data);
  },
  async getEmployeeAllPermission(data) {
    return getEmployeeAllPermission(data);
  },
  async getStaffTreeList(data) {
    return getStaffTreeList(data);
  },
  async approveRedCircle(data) {
    return approveRedCircle(data);
  },
  async syncWechatWork(data) {
    return syncWechatWork(data);
  },
  async getStaffFullTreeList(data) {
    return getStaffFullTreeList(data);
  },
  async getEmployeePreview(data) {
    return getEmployeePreview(data);
  },
  async getEmployeePostTree(data) {
    return getEmployeePostTree(data);
  },
  async unbindEmployee(data) {
    return unbindEmployee(data);
  }
};
