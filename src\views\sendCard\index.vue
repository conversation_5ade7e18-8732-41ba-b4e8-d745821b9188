<template>
  <div class="front">
    <div class="student-tab">
      <template v-for="(item, index) in menuList">
        <span
          :key="index"
          :class="{ 'student-tab--active': tab === index }"
          @click="changeTab(index)"
          v-has="{ m: 'front', o: item.control }"
          >{{ item.name }}</span
        ></template
      >
    </div>
    <clubCardManage v-show="tab === 0"></clubCardManage>
    <refillCardManage
      v-if="$_has({ m: 'front', o: 'statusList' })"
      v-show="tab === 1"
      :tabindex="tab"
    ></refillCardManage>
    <entityCardRecord
      v-if="$_has({ m: 'front', o: 'source_list' })"
      v-show="tab === 2"
      :tabindex="tab"
    ></entityCardRecord>
    <virtualCardRecord
      v-if="$_has({ m: 'front', o: 'transfer_rate_list' })"
      v-show="tab === 3"
      :tabindex="tab"
    ></virtualCardRecord>
  </div>
</template>

<script>
import clubCardManage from "./clubCardManage.vue";
import refillCardManage from "./refillCardManage.vue";
import entityCardRecord from "./entityCardRecord.vue";
import virtualCardRecord from "./virtualCardRecord.vue";

export default {
  data() {
    return {
      menuList: [
        {
          name: "会员卡管理",
          control: ""
        },
        {
          name: "充值卡管理",
          control: ""
        },
        {
          name: "实体卡开卡记录",
          control: ""
        },
        {
          name: "虚拟卡开卡记录",
          control: ""
        }
      ],
      tab: 0
    };
  },
  components: {
    clubCardManage,
    refillCardManage,
    entityCardRecord,
    virtualCardRecord
  },
  created() {
    // for (let k = 0; k < this.menuList.length; k++) {
    //   if (this.$_has({ m: "front", o: this.menuList[k].control })) {
    //     this.tab = k;
    //     break;
    //   }
    // }
  },
  methods: {
    changeTab(i) {
      this.tab = i;
    }
  }
};
</script>

<style lang="less" scoped>
.front {
  height: 100%;

  .student-tab {
    width: 100%;
    height: 46px;
    background: #fff;
    border-radius: 4px;
    padding: 0 16px;
    // border-top: 1px solid #e0e6ed;
    margin-bottom: 16px;
    margin-left: -10px;
    box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    span {
      font-family: @text-famliy_semibold;
      color: @text-color_second;
      font-size: @text-size_normal;
      display: inline-block;
      height: 44px;
      border-bottom: 2px solid transparent;
      line-height: 44px;
      cursor: pointer;
      font-weight: bold;
    }
    span + span {
      margin-left: 32px;
    }
    span.student-tab--active {
      color: #2d80ed;
      border-color: #2d80ed;
    }
  }
}
</style>
