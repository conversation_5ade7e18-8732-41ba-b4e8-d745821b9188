import qs from "qs";
const fetch = require("../fetch");

// 销售统计报表列表
function getSaleReportList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/order/sale-report?${new_data}`
  );
}
// 销售统计报表列表导出
function saleReportExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/order/sale-report-export?${new_data}`
  );
}

export default {
  getSaleReportList,
  saleReportExport
};
