<template>
  <div class="connect-record-statistics">
    <tg-search
      class="connect-search"
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="2"
      :isExport="isExport"
      @reset="reset"
      @educe="exportExcel"
      @search="search"
      :loadingState="exportLoading"
      :defaultSpread="false"
      ref="search"
    ></tg-search>
    <div class="tg-table__box statistics-table">
      <el-table
        ref="table"
        :data="normalDataList"
        tooltip-effect="dark"
        class="tg-table indicator-table"
        v-loading="false"
        :height="height + 'px'"
        border
        :span-method="objectSpanMethod"
        :row-class-name="tableRowClassName"
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column
          align="center"
          prop="area_name"
          label="区域"
          min-width="100"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.area_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="employee_name"
          label="姓名"
          min-width="100"
        >
          <template slot="header">
            <span>姓名</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>以员工为单位，【员工信息】中主岗为市场专员</p>
                <p>1.展示离职信息</p>
                <p>2.转岗后也计入</p>
              </div>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.employee_name }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="valid_num"
          label="预算有效信息"
          min-width="120"
        >
          <template slot="header">
            <span>预算有效信息</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">市场专员任务池提取数据【目标有效】</div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.valid_num }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="valid_amount"
          label="有效完成"
          min-width="100"
        >
          <template slot="header">
            <span>有效完成</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>渠道：一级渠道：【线下-市场地面推广】；</p>
                <p>生成日期：等于筛选日期；</p>
                <p>意向客户有效性字段标记有效；</p>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.valid_amount }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="audition_num"
          label="预算试听人数"
          min-width="100"
        >
          <template slot="header">
            <span>预算试听人数</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">市场专员任务池提取数据【目标试听】</div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.audition_num }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="audition_amount"
          label="试听完成"
          min-width="100"
        >
          <template slot="header">
            <span>试听完成</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>渠道：一级渠道：【线下-市场地面推广】；</p>
                <p>试听日期：等于筛选日期；</p>
                <p>试听管理中试听状态【已报名；试听中已上课】</p>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.audition_amount }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="new_num"
          label="预算正课新招"
          min-width="120"
        >
          <template slot="header">
            <span>预算正课新招</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">市场专员任务池提取数据【目标新招】</div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.new_num }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="new_amount"
          label="新招完成"
          min-width="120"
        >
          <template slot="header">
            <span>新招完成</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">
                <p>渠道：一级渠道：【线下-市场地面推广】；</p>
                <p>收费日期：等于筛选日期；</p>
                <p>收据类型：收费，收费类型：新增；</p>
              </div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.new_amount }}</span>
          </template>
        </el-table-column>

        <el-table-column
          align="center"
          prop="new_amount_rate_str"
          label="新招完成率"
          min-width="120"
        >
          <template slot="header">
            <span>新招完成率</span>
            <el-tooltip
              class="item"
              effect="dark"
              placement="top"
              popper-class="custom-tooltip"
            >
              <div slot="content">新招完成/预算正课新招人数</div>
              <i
                class="el-icon-question"
                style="margin-left: 4px; color: #409eff; cursor: pointer"
              ></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.new_amount_rate_str || "--" }}</span>
          </template>
        </el-table-column>

        <template slot="empty">
          <div style="margin-top: 2%">
            <loading v-if="loading"></loading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
    </div>
  </div>
</template>
<script>
import quickTime from "@/public/quickTime";
import reportApi from "@/api/report";
import loading from "../loading";
export default {
  data() {
    return {
      exportLoading: false,
      searchForm: {
        date_range: [],
        employee_ids: []
      },
      isExport: true,
      search_title: [
        {
          props: "date_range",
          label: "时间范围",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "employee_ids",
          label: "市场专员",
          type: "mutipleSelect_filterable",
          show: true,
          selectOptions: [],
          clearable: true
        }
      ],

      page: 1,
      list: [],
      dataList: [],
      normalDataList: [],
      summaryData: null,
      spanMap: {},
      staffConnectVisible: false,
      connectSearch: {},
      loading: false,
      height: 0,
      rawData: null
    };
  },
  components: {
    loading
  },
  watch: {
    school_id: {
      handler(val) {
        if (this.$_has({ m: "marketTaskIndicator", o: "indicatorList" })) {
          this.searchForm.employee_id = [];
          this.getIndicatorEmployee();
          this.search();
        }
      },
      deep: true
    }
  },
  created() {
    this.height = window.innerHeight - 230;
    this.isExport = this.$_has({
      m: "marketTaskIndicator",
      o: "indicatorListExport"
    });
  },
  mounted() {
    if (this.$_has({ m: "marketTaskIndicator", o: "indicatorList" })) {
      this.setSearchDefault();
      this.search();
      this.getIndicatorEmployee();
    }
  },

  updated() {
    // 确保合计行在DOM更新后正确显示
    this.$nextTick(() => {
      if (this.$refs.table) {
        this.$refs.table.doLayout();
      }
    });
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  methods: {
    search() {
      this.page = 1;
      const q = this.getSearch();
      if (
        this.searchForm.date_range === null ||
        this.searchForm.date_range.length === 0
      ) {
        this.$message.error("请选择日期");
        return;
      }
      this.getStatisticsList(q);
    },

    async getIndicatorEmployee() {
      const res = await reportApi.getIndicatorEmployee({
        department_id: this.school_id
      });
      this.search_title[1].selectOptions = res.data.data.map((item) => ({
        id: item.employee_id,
        name: item.employee_name
      }));
    },

    async getStatisticsList(search) {
      this.loading = true;
      const res = await reportApi.getIndicatorList(search);
      this.rawData = res.data.data;
      this.processTableData();
      this.loading = false;
    },

    // 处理表格数据，按照新的数据格式
    processTableData() {
      if (!this.rawData || !this.rawData.data) {
        this.dataList = [];
        this.normalDataList = [];
        this.summaryData = null;
        return;
      }

      const processedData = [];
      const areaData = this.rawData.data;

      // 遍历每个区域的数据
      Object.keys(areaData).forEach((areaName) => {
        const employees = areaData[areaName];

        employees.forEach((employee) => {
          processedData.push({
            ...employee,
            rowType: "data"
          });
        });
      });

      this.dataList = processedData;
      this.normalDataList = processedData; // 只包含普通数据行

      // 存储合计数据
      if (this.rawData.totle) {
        this.summaryData = this.rawData.totle;
      }

      this.calculateSpanMap();
    },

    // 计算合并单元格的映射
    calculateSpanMap() {
      this.spanMap = {};
      const areaGroupedData = {};

      // 按区域分组计算
      this.dataList.forEach((item, index) => {
        if (item.rowType === "data") {
          const areaName = item.area_name;

          // 区域分组
          if (!areaGroupedData[areaName]) {
            areaGroupedData[areaName] = [];
          }
          areaGroupedData[areaName].push(index);
        }
      });

      // 设置区域列的合并（第0列）
      Object.values(areaGroupedData).forEach((indexes) => {
        if (indexes.length > 0) {
          // 第一行显示区域名称，后续行隐藏
          this.spanMap[`${indexes[0]}_0`] = [indexes.length, 1];
          for (let i = 1; i < indexes.length; i++) {
            this.spanMap[`${indexes[i]}_0`] = [0, 0];
          }
        }
      });
    },

    // 合并单元格的方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const key = `${rowIndex}_${columnIndex}`;

      // 普通数据行的区域列合并
      if (columnIndex === 0) {
        const span = this.spanMap[key];
        if (span) {
          return span;
        }
      }

      return [1, 1];
    },

    // 表格行类名
    tableRowClassName({ row, rowIndex }) {
      return "";
    },

    // Element Table 内置合计方法
    getSummaries(param) {
      const { columns } = param;
      const sums = [];

      if (!this.summaryData) {
        // 如果没有合计数据，返回空合计行
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计";
          } else if (index === 1) {
            sums[index] = ""; // 第二列为空，用于合并效果
          } else {
            sums[index] = "0";
          }
        });
        return sums;
      }

      columns.forEach((column, index) => {
        const property = column.property;

        if (index === 0) {
          sums[index] = "合计";
        } else if (index === 1) {
          sums[index] = ""; // 第二列为空，用于合并效果
        } else {
          // 根据列的property属性来匹配数据
          switch (property) {
            case "valid_num":
              sums[index] = this.summaryData.valid_num;
              break;
            case "valid_amount":
              sums[index] = this.summaryData.valid_amount;
              break;
            case "audition_num":
              sums[index] = this.summaryData.audition_num;
              break;
            case "audition_amount":
              sums[index] = this.summaryData.audition_amount;
              break;
            case "new_num":
              sums[index] = this.summaryData.new_num;
              break;
            case "new_amount":
              sums[index] = this.summaryData.new_amount;
              break;
            case "new_amount_rate_str":
              sums[index] = this.summaryData.new_amount_rate_str || "--";
              break;
            default:
              sums[index] = "--";
          }
        }
      });

      return sums;
    },

    async exportExcel() {
      // 前端通过html导出表格
      // 创建不可见的虚拟表格
      this.exportLoading = true;
      const table = document.createElement("table");
      table.id = "virtualTable";
      table.style.display = "none";
      const head_html = $(".indicator-table .el-table__header-wrapper>table")
        .children("thead")
        .html();
      const body_html = $(".indicator-table .el-table__body-wrapper>table")
        .children("tbody")
        .html();
      table.innerHTML = head_html + body_html;
      // 将表格转换为工作表，使用 { raw: true } 选项来保留原始值
      const worksheet = XLSX.utils.table_to_sheet(table, { raw: true });
      // 设置统一的列宽
      const columnWidth = { wpx: 120 }; // 120像素宽度，每列相同
      worksheet["!cols"] = Array(Object.keys(worksheet).length).fill(
        columnWidth
      );

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, `市场任务指标`);
      XLSX.writeFile(workbook, `市场任务指标.xlsx`);
      this.exportLoading = false;
      this.$message.success("导出成功");
      table.remove();
    },

    reset() {
      this.searchForm = {
        date_range: [],
        employee_ids: []
      };
      this.setSearchDefault();
      const q = this.getSearch();
      this.getStatisticsList(q);
    },

    currentChange(val) {
      this.page = val;
      const q = this.getSearch();
      this.getStatisticsList(q);
    },

    getSearch() {
      const newForm = JSON.parse(JSON.stringify(this.searchForm));
      const query = {
        page: this.page,
        department_id: this.school_id,
        ...newForm
      };

      if (this.searchForm.date_range && this.searchForm.date_range.length) {
        const [start, end] = query.date_range;
        query.start_time = start || "";
        query.end_time = end || "";
        delete query.date_range;
      }
      return query;
    },

    setSearchDefault() {
      const week = quickTime.GetDate("month");
      this.$set(this.searchForm, "date_range", week);
    }
  }
};
</script>
<style lang="less" scoped>
::v-deep .el-input__inner {
  height: 32px !important;
  line-height: 32px !important;
}
::v-deep .el-input.is-focus::after {
  border: none !important;
}
::v-deep .el-select-dropdown {
  left: 0 !important;
}
.connect-record-statistics {
  margin-top: 16px;
}
.connect-search {
  margin: 0 6px;
  width: calc(100% - 12px);
}
::v-deep .statistics-table {
  width: calc(100% - 12px);
  border: 1px solid @base-color;
  .el-table th.is-leaf {
    border: none;
  }
  .el-table td:last-child {
    border-right: none;
  }
  .el-table {
    padding: 0;
    border: none;
  }
  th {
    background: @light-color;
  }
  .el-table th:first-child > .cell {
    padding-left: 26px;
  }
  .el-table td:first-child > .cell {
    padding-left: 26px;
  }
  .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      // position: absolute;
      top: 50%;
      left: 50%;
      height: 80px;
    }
    .loading-container {
      position: absolute;
      top: 15%;
      left: 1%;
      width: 100%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
}

// Element Table 合计行样式
::v-deep .el-table__footer-wrapper {
  .el-table__footer {
    td {
      background-color: #e6f7ff !important;
      font-weight: bold !important;
      color: #1890ff !important;
    }
  }
}

// Tooltip 自定义样式
::v-deep .custom-tooltip {
  max-width: 400px;

  .el-tooltip__popper {
    max-width: 400px !important;

    .el-tooltip__popper[x-placement^="top"] {
      margin-bottom: 8px;
    }
  }
}

// 表头问号图标样式
::v-deep .el-table th .cell {
  display: flex;
  align-items: center;
  justify-content: center;

  .el-icon-question {
    font-size: 14px;
    margin-left: 4px;
    color: #409eff;
    cursor: pointer;

    &:hover {
      color: #66b1ff;
    }
  }
}
</style>
