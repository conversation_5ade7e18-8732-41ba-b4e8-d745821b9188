import axios from "../http";
import Vue from "vue";

// 获取岗位列表
function getPostList(data) {
  return axios
    .get(`/api/organization-service/office-post/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 岗位信息
function getPostInfo(data) {
  return axios
    .get(`/api/organization-service/office-post/info?office_post_id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建岗位
function getPostCreate(data) {
  return axios
    .post(`/api/organization-service/office-post/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新岗位
function postUpdate(data) {
  return axios
    .patch(
      `/api/organization-service/office-post/update?office_post_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除岗位
function getPostDelete(data) {
  return axios
    .delete(
      `/api/organization-service/office-post/delete?office_post_id=${data.id}`
    )
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 获取岗位列表
  async GetPostList(data) {
    return getPostList(data);
  },
  // 岗位信息
  async GetPostInfo(data) {
    return getPostInfo(data);
  },
  // 创建岗位
  async GetPostCreate(data) {
    return getPostCreate(data);
  },
  // 更新岗位
  async PostUpdate(data) {
    return postUpdate(data);
  },
  // 删除岗位
  async GetPostDelete(data) {
    return getPostDelete(data);
  }
};
