import qs from "qs";
const fetch = require("../fetch");

// 续费导出
export function exportRenewalExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/taskpool-service/admin/renewal/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 补课导出
export function exportMakeupExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/taskpool-service/admin/makeup/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 续费列表
export function getRenewalList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(`/api/taskpool-service/admin/renewal/list?${new_data}`);
}
// 补课列表
export function getmakeupList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(`/api/taskpool-service/admin/makeup/list?${new_data}`);
}
// 任务汇总列表
export function getSummary(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/taskpool-service/admin/common/summary?${new_data}`
  );
}
// 数据概览
export function getOverviewList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/taskpool-service/admin/overview/list?${new_data}`
  );
}
// 重要跟进对象
export function getRechargeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/list?${new_data}`
  );
}
// 重要跟进对象导出
export function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 重要跟进对象导出
export function exportAllExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/export-all?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 续费完成率（教务）导出
function exportRechargeCompletionRate(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/education-export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 设置优先级
function updateStars(data) {
  return fetch.fetchPost(
    `/api/taskpool-service/admin/recharge/update-stars?id=${data.id}`,
    data
  );
}
// 修改教务
function setEducation(data) {
  return fetch.fetchPost(
    `/api/taskpool-service/admin/recharge/set-education`,
    data
  );
}
// 修改学管师
function setSchoolManager(data) {
  return fetch.fetchPost(
    `/api/taskpool-service/admin/recharge/set-school-manager`,
    data
  );
}
// 简要信息
export function getSummaryObject(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/summary?${new_data}`
  );
}
// 剔除任务
function rechargeRemove(data) {
  return fetch.fetchPost(`/api/taskpool-service/admin/recharge/remove`, data);
}

// 剔除任务列表
export function rechargeRemoveList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/remove-list?${new_data}`
  );
}
// 剔除任务列表导出
export function rechargeRemoveListExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/taskpool-service/admin/recharge/remove-list-export?${new_data}`
  );
}

export default {
  getRenewalList,
  getmakeupList,
  exportRenewalExcel,
  exportMakeupExcel,
  getSummary,
  getOverviewList,
  exportExcel,
  exportAllExcel,
  updateStars,
  setEducation,
  setSchoolManager,
  getSummaryObject,
  rechargeRemove,
  rechargeRemoveList,
  rechargeRemoveListExport,
  getRechargeList,
  exportRechargeCompletionRate
};
