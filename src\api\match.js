import { fetchGet, fetchPost, fetchDel } from "../fetch";
import qs from "qs";
// 创建赛事类型
function createType(data) {
  return fetchPost("/api/course-service/match-category/create", data);
}
// 获取类型信息
function getTypeInfo(data) {
  return fetchGet(
    `/api/course-service/match-category/info?match_category_id=${data}`
  );
}
// 获取类型列表
function getTypeList(data) {
  return fetchGet("/api/course-service/match-category/list", data);
}
// 修改类型
function updateType(data, msg) {
  return fetchPost(
    `/api/course-service/match-category/update?match_category_id=${data.id}`,
    data,
    msg
  );
}
// 删除类型
function delType(data) {
  return fetchDel(`/api/course-service/match-category/delete`, {
    params: data
  });
}
// 批量删除赛事
function batchDelMatch(data) {
  return fetchDel("/api/course-service/match/batch-delete", { data });
}
// 批量修改赛事
function batchUpdateMatch(data, msg) {
  return fetchPost("/api/course-service/match/batch-update", data, msg);
}
// 添加赛事
function createMatch(data) {
  return fetchPost("/api/course-service/match/create", data);
}
// 删除赛事
function delMatch(data) {
  return fetchDel("/api/course-service/match/delete", { params: data });
}
// 赛事导出
function exportMatch(data) {
  return fetchGet("/api/course-service/match/export", {
    params: { ...data, exportData: 1 }
  });
}
// 赛事详情
function getMatchInfo(data) {
  return fetchGet("/api/course-service/match/info", { params: data });
}
// 赛事列表
function getMatchList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/match/list?${new_data}`);
}
// 更新赛事
function updateMatch(data, msg) {
  return fetchPost(
    `/api/course-service/match/update?match_id=${data.id}`,
    data,
    msg
  );
}

// 绑定赛事
function createCourseMatch(data) {
  return fetchPost(`/api/course-service/course-match/create`, data);
}
// 删除绑定赛事
function delCourseMatch(data) {
  return fetchDel(`/api/course-service/course-match/delete`, { data });
}
// 获取绑定赛事列表
function getCourseMatchList(data) {
  return fetchGet(`/api/course-service/course-match/list`, { params: data });
}
// 修改绑定赛事
function updateCourseMatch(data, msg) {
  return fetchPost(`/api/course-service/course-match/update`, data, msg);
}

export default {
  async createType(data) {
    return createType(data);
  },
  async getTypeInfo(data) {
    return getTypeInfo(data);
  },
  async getTypeList(data) {
    return getTypeList(data);
  },
  async updateType(data, msg) {
    return updateType(data, msg);
  },
  async delType(data) {
    return delType(data);
  },
  async batchDelMatch(data) {
    return batchDelMatch(data);
  },
  async batchUpdateMatch(data, msg) {
    return batchUpdateMatch(data, msg);
  },
  async createMatch(data) {
    return createMatch(data);
  },
  async delMatch(data) {
    return delMatch(data);
  },
  async exportMatch(data) {
    return exportMatch(data);
  },
  async getMatchInfo(data) {
    return getMatchInfo(data);
  },
  async getMatchList(data) {
    return getMatchList(data);
  },
  async updateMatch(data, msg) {
    return updateMatch(data, msg);
  },
  async createCourseMatch(data) {
    return createCourseMatch(data);
  },
  async delCourseMatch(data) {
    return delCourseMatch(data);
  },
  async getCourseMatchList(data) {
    return getCourseMatchList(data);
  },
  async updateCourseMatch(data, msg) {
    return updateCourseMatch(data, msg);
  }
};
