<template>
  <div class="vessel">
    <leftMenu @curTab="curTab" ref="left_menu"></leftMenu>
    <div class="rightVessel container">
      <tgSearch
        ref="tgSearch"
        :searchTitle.sync="searchTitle"
        :form.sync="search"
        :showNum="2"
        :isExport="isExport"
        @reset="reset"
        @search="searchVal"
        @educe="educe"
      ></tgSearch>
      <commonTab
        :tabList="tabList"
        :active="0"
        ref="commonTab"
        @changeTabs="changeTabs"
      ></commonTab>
      <div class="tg-table__box tableHeight">
        <div class="tg-box--border"></div>
        <el-table
          ref="tableData"
          :data="tableData"
          tooltip-effect="dark"
          class="tg-table"
          :cell-style="{ borderRightColor: '#e0e6ed75' }"
          :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
          border
        >
          <el-table-column
            v-for="(item, index) in currentTableHead"
            :key="index"
            :prop="item.prop"
            :label="item.label"
            :min-width="item.width"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="scope.column.property === 'updated_at'">
                <span
                  v-if="
                    scope.row.status === 'complete' ||
                    scope.row.status === 'reject' ||
                    scope.row.status === 'revoke'
                  "
                  >{{ scope.row.updated_at }}</span
                >
                <span v-else></span>
              </span>

              <span v-else-if="scope.column.property === 'status_ch'">
                <el-tag
                  v-if="
                    scope.row.status === 'pending' ||
                    scope.row.status === 'reviewing'
                  "
                  size="small"
                  type="warning"
                >
                  {{ scope.row.status_ch }}
                </el-tag>
                <span v-else>{{ scope.row.status_ch }}</span>
              </span>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
          <el-table-column
            :label="currentTableOption.label"
            :min-width="currentTableOption.width"
          >
            <!-- fixed="right" -->
            <template slot-scope="scope">
              <span
                style="color: #157df0"
                @click="handle('check', scope)"
                class="tableBtn"
                >查看</span
              >
              <span
                v-if="
                  (scope.row.status === 'reviewing' ||
                    scope.row.status === 'pending') &&
                  currentleftMenu === 1
                "
                style="color: #fd6865"
                @click="handle('rejected', scope)"
                class="tableBtn"
                >驳回</span
              >

              <span
                v-if="
                  (scope.row.status === 'reviewing' ||
                    scope.row.status === 'pending') &&
                  currentleftMenu === 1
                "
                style="color: #2d80ed"
                @click="handle('agree', scope)"
                class="tableBtn"
                >同意</span
              >
              <span
                v-if="
                  userInfo.employee_id == scope.row.employee_id &&
                  scope.row.status === 'pending' &&
                  currentleftMenu === 2
                "
                style="color: #fd6865"
                @click="handle('undo', scope)"
                class="tableBtn"
                >撤销</span
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div style="margin-top: 15%">
              <TgLoading v-if="loading"></TgLoading>
              <div class="empty-container" v-else>暂无数据～</div>
            </div>
          </template>
        </el-table>
        <!-- 分页 -->
        <div class="tg-pagination">
          <span class="el-pagination__total">共 {{ total }} 条</span>
          <el-pagination
            background
            layout="prev, pager, next,jumper"
            :total="total"
            :page-size.sync="pageSize"
            :current-page.sync="page"
            @current-change="currentChange"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <workflow-info
      v-if="workflow_visible"
      @close="getList"
      :workflow_id="workflow_id"
    ></workflow-info>
    <!-- <offer-new
      type="edit"
      :is_workflow="true"
      v-if="edit_visible"
      :id="basic_info.modify_data.id"
      :addDepartmentId="basic_info.modify_data.department_id"
      :addDepartmentName="basic_info.modify_data.department_name"
      @close="edit_visible = false"
    ></offer-new> -->
  </div>
</template>

<script>
import leftMenu from "./components/leftMenu.vue";
import tgSearch from "@/components/search/search.vue";
import commonTab from "./components/commonTab.vue";
import userCenterApi from "@/api/userCenter";
import timeFormat from "@/public/timeFormat";
// import OfferNew from "@/components/offerManagement/offerNew.vue";
import {
  search_List,
  userCenter_Tab_List,
  table_Head_List
} from "./common/searchList";
import { downLoadFile } from "@/public/downLoadFile";
import WorkflowInfo from "@/components/workflow/workflowInfo.vue";
// import workflowApi from "@/api/workflowService.js";

export default {
  beforeRouteLeave(to, from, next) {
    window.sessionStorage.setItem("userCenterTab", 0);
    window.sessionStorage.setItem("userCenterTableTab", 0);
    next();
  },
  data() {
    return {
      searchTitle: [],
      search: {},
      tabList: [],
      total: 0,
      pageSize: 10,
      page: 1,
      loading: false,
      tableData: [],
      currentleftMenu: 1,
      currentTableMenu: "",
      currentTableHead: [],
      currentTableOption: "",
      workflow_visible: false,
      workflow_id: "",
      edit_visible: false,
      isExport: false
    };
  },

  computed: {
    userInfo() {
      return this.$store.getters.doneGetLoginInfo;
    },
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    school_id(val) {
      if (val?.length) {
        this.searchStaff();
      }

      // 获取是否有流程审批消息
      // this.$store.dispatch("getApproveStatistics", {
      //   department_id: val
      // });
    }
  },
  created() {
    if (this.$_has({ m: "workflowService", o: "approve_export" })) {
      this.isExport = true;
    }
    this.tabList = userCenter_Tab_List[1];
  },
  methods: {
    // 重置
    reset() {
      for (const k in this.search) {
        this.search[k] = "";
      }
      this.page = 1;
      this.search.list_type = this.currentleftMenu + 1;
      this.searchStaff();
    },
    searchVal() {
      this.page = 1;
      this.searchStaff();
    },
    // 查询
    searchStaff() {
      this.loading = true;
      this.tableData = [];
      const parameter = {
        ...this.search,
        page: this.page,
        page_size: this.pageSize,
        status: this.currentTableMenu || "",
        department_id: this.school_id
      };
      // 左侧页签是待处理的审批
      if (parameter.list_type === 2) {
        parameter.status = "";
      }
      parameter.start_time = parameter.xzrq ? parameter.xzrq[0] : "";
      parameter.end_time = parameter.xzrq ? parameter.xzrq[1] : "";
      delete parameter.xzrq;
      delete parameter.applicantId_name;
      const map = {
        pending: "待审批",
        reviewing: "审批中",
        complete: "审批通过",
        reject: "已驳回",
        revoke: "已撤销"
      };
      userCenterApi
        .approveRecord(parameter)
        .then((res) => {
          const { code, data } = res.data;
          if (code === 0) {
            this.tableData = data.results
              ? data.results.map((item) => {
                  return {
                    ...item,
                    status_ch: map[item.status],
                    created_at: timeFormat.GetTime(item.created_at),
                    updated_at: timeFormat.GetTime(item.updated_at)
                  };
                })
              : [];
            this.total = data.count || 0;
          } else {
            this.$message.error(res.data.message);
          }
          this.loading = false;
        })
        .catch(() => {
          this.loading = false;
        });
    },
    // 导出
    educe() {
      const data = { ...this.search };
      data.start_time = data.xzrq ? data.xzrq[0] : "";
      data.end_time = data.xzrq ? data.xzrq[1] : "";
      delete data.xzrq;
      userCenterApi
        .exportExcel({
          ...data,
          status_type: "intention",
          department_id: this.school_id
        })
        .then((res) => {
          downLoadFile(res, "审批记录");
        });
    },
    // 左侧菜单栏高亮项
    curTab(index) {
      this.page = 1;
      // 动态改变搜索条件
      this.searchTitle = search_List[index];
      this.getTypeList();
      // 动态改变tab展示
      this.tabList.splice(0, this.tabList.splice);
      this.tabList = userCenter_Tab_List[index];

      // 动态改变table表头
      this.currentTableHead = table_Head_List[index][0];
      // 动态改变table操作项
      this.currentTableOption = table_Head_List[index][1];
      this.search = {};
      for (const k in this.searchTitle) {
        this.$set(this.search, this.searchTitle[k].props, "");
      }
      this.search.waiting_review = false;
      if (index === 1) {
        this.search.waiting_review = true;
      }
      this.search.list_type = index + 1;
      this.currentleftMenu = index;
      this.$nextTick(() => {
        this.$refs.tgSearch.showAllSearch(false);
      });
      // this.searchStaff();
    },
    // 切换表单头部tabs
    changeTabs(id) {
      this.currentTableMenu = id;
      this.searchStaff();
    },
    // 分页器切换
    currentChange(page) {
      this.page = page;
      this.searchStaff();
    },
    // 执行对应按钮事件
    handle(key, row) {
      this[key](row);
    },
    // 查看
    check(scope) {
      // console.log(scope);
      this.workflow_visible = true;
      this.workflow_id = scope.row.id;
      this.$store.commit("setListOpenDialog", "");
    },
    // 驳回
    rejected(scope) {
      this.workflow_visible = true;
      this.workflow_id = scope.row.id;
      this.$store.commit("setListOpenDialog", "reject");
    },
    // 同意
    agree(scope) {
      this.workflow_visible = true;
      this.workflow_id = scope.row.id;
      this.$store.commit("setListOpenDialog", "agree");
    },
    // 撤销
    undo(scope) {
      this.workflow_visible = true;
      this.workflow_id = scope.row.id;
      this.$store.commit("setListOpenDialog", "cancel");
    },
    // 修改
    // async fix(scope) {
    //   const { data } = await userCenterApi.getLastUpdateRecord({
    //     approval_id: scope.row.id
    //   });
    //   this.basic_info = data;
    //   const { modify_data } = this.basic_info;
    //   let name = "";
    //   if (this.workflow_type === "open_class") {
    //     name = modify_data.name;
    //   }
    //   this.$store.commit("setRecordName", name);
    //   this.edit_visible = true;
    // },
    getList() {
      this.workflow_visible = false;
      this.searchStaff();
      // this.$refs.left_menu.getMsgNum();
    },
    async getTypeList() {
      this.searchTitle[1].selectOptions = [
        {
          id: "refund",
          name: "退费审批"
        },
        {
          id: "open_class",
          name: "开班申请"
        },
        {
          id: "transfer",
          name: "转校转费"
        },
        {
          id: "customer_transfer",
          name: "意向客户转校审批"
        }
      ];
    }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.$refs.tableData.doLayout();
    });
  },
  components: {
    leftMenu,
    tgSearch,
    commonTab,
    WorkflowInfo
    // OfferNew
  }
};
</script>

<style lang="less" scoped>
.vessel {
  height: 100%;
  // overflow: hidden;
  padding: 16px 0 6px 6px;
  display: flex;
  box-sizing: border-box;
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }

  .tg-search {
    margin-bottom: 16px;
  }

  .tg-table__box {
    margin-right: 0;
    margin-left: 0;
  }

  .rightVessel {
    padding-left: 10px;
    // flex: 1;
    width: calc(100% - 260px);
    position: relative;
    margin-right: 16px;

    .tableHeight {
      // position: absolute;
      // width: calc(100% - 22px);
      width: 100%;

      .tableBtn {
        // width: 35px;
        margin: 0 5px;
        font-size: 14px;
        display: inline-block;
        // border-right: 1px solid #cbcfda;
        cursor: pointer;

        &:last-child {
          border-right: none;
        }
      }
    }
  }
  ::v-deep .el-table__body-wrapper {
    height: calc(100% - 46px);
    .empty-container {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    .loading-container {
      position: absolute;
      top: 30%;
      left: 1%;
      background: transparent;
      .box {
        height: 100%;
      }
    }
  }
}
</style>
