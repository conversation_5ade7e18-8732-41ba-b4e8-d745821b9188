import axios from "../http";
import Vue from "vue";
const url_path = "/api/system-service/finance-range";
// 获取财务期间列表
function list(data) {
  return axios
    .get(`${url_path}/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取财务期间
function info(data) {
  return axios
    .get(`${url_path}/info?finance_range_id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 新增财务期间
function create(data) {
  return axios
    .post(`${url_path}/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新财务期间
function update(data) {
  return axios
    .patch(`${url_path}/update?finance_range_id=${data.id}`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除财务期间
function remove(data) {
  return axios
    .delete(`${url_path}/delete?finance_range_id=${data.id}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 获取财务期间列表
  list,
  // 获取财务期间
  info,
  // 新增财务期间
  create,
  // 更新财务期间
  update,
  // 删除教室
  remove
};
