<template>
  <div class="refillCardManage">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="3"
      @reset="reset"
      @search="searchVal"
      :searchLoadingState="searchLoading"
      class="search"
      :isExport="isExport"
      @educe="exportExcel"
      :loadingState="exportLoading"
    ></tg-search>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchLoading: false,
      isExport: true,
      exportLoading: false,
      searchForm: {
        card_number: "",
        card_type: undefined,
        validity_type: undefined,
        per: "",
        status_type: undefined,
        department_id: ""
      },
      search_title: [
        {
          label: "卡号",
          props: "card_number",
          type: "input",
          show: true
        },
        {
          props: "card_type",
          label: "卡类型",
          type: "select",
          show: true,
          selectOptions: [
            { name: "全部", id: undefined },
            { name: "会员卡", id: "1" },
            { name: "充值卡", id: "2" }
          ]
        },
        {
          props: "validity_type",
          label: "有效期类型",
          type: "select",
          show: true,
          selectOptions: [
            { name: "全部", id: undefined },
            { name: "月卡", id: "1" },
            { name: "半年卡", id: "2" },
            { name: "年卡", id: "3" },
            { name: "双周卡", id: "4" },
            { name: "季卡", id: "5" },
            { name: "双月卡", id: "6" },
            { name: "周卡", id: "15" }
          ]
        },
        {
          label: "使用人",
          props: "per",
          type: "input",
          placeholder: "请输入学员姓名/学号/手机号",
          show: true
        },
        {
          props: "status_type",
          label: "客户状态",
          type: "select",
          show: false,
          selectOptions: [
            {
              id: undefined,
              name: "全部"
            },
            {
              name: "意向客户",
              id: "intention"
            },
            {
              name: "临时学员",
              id: "temp"
            },
            {
              name: "试听学员",
              id: "tryListen"
            },
            {
              name: "在读学员",
              id: "student"
            },
            {
              name: "休学学员",
              id: "out"
            },
            {
              name: "流失学员",
              id: "churn"
            }
          ]
        },
        {
          props: "department_id",
          label: "使用校区",
          type: "school",
          show: false,
          selectOptions: [],
          school_choose_type: "radio",
          use_store_options: true
        }
      ]
    };
  },
  components: {},
  created() {},
  methods: {
    reset() {},
    searchVal() {},
    exportExcel() {}
  }
};
</script>

<style lang="less" scoped></style>
