### 版本历史

```javascript
v1.7.815 更新内容：

1、教务任务池新增“导出全部任务”按钮
2、TW 管理后台创建正价课排课期次更改为手动输入组件
3、库存变动表小箭头排序优化
4、天弈学生端直播间昵称同步
5、直播-回放管理，操作按钮权限拆分
6、收据管理，转费转入对应收据的渠道展示不对的 bug 修复
7、收据管理，渠道筛选，已知 bug 修复 8、前端获客-介绍人统计，已知bug修复
9.审批流审批通过后调用真实业务接口增加重试机制，调用失败会额外重试两次。
10、新增网校前端数据看板导出 11、【直营校月报】表头更新
```

```javascript
v1.7.816 更新内容：

1、进出库管理，库存调整已知bug修复
2、网校职级绑定配置，并应用于客户管理和学员信息管理，
3、天弈学生端增加查看讲解视频功能
4、天弈管理后台增加上传课后习题讲解视频入口
5、直播间白名单（已发版
6、寒假集训选期H5（已发版
7、库存变动表，库存异常的bug修复
8、权限组管理，子级取消不影响父级的bug修复
```

```javascript
v1.7.817 更新内容：

1、教练任务池
2、TW自动入班逻辑优化
3、阿波罗看板优化
4、退费来源取值判断（网校
5、天工管理后台收据/学员信息增加快递信息维护
6、网校APP增加我的快递
7、“学员信息管理”-学员类别新增筛选项【不限】
8、收完费直接可以打印收据
9、网校客户管理优化
```

```javascript
v1.7.818 更新内容：

1、直播管理-列表页增加选择列
2、直播管理-列表-学生数详情增加导出
3、教务任务池，批量修改学管师/教务
4、解决订单创建失败时优惠券没有释放的问题
5、教练任务池优化在休退加一个【学员类别】的字段并支持筛选
6、解决退学二级原因为空的问题
```

```javascript
v1.7.819 更新内容：
1、教练任务池，累计课消时长转换
2、增加批量数据处理页面
3、转费明细，金额显示优化（显示绝对值）
```

```javascript
v1.7.820 更新内容：
1、运营数据看板 【新增】、【续费】统计逻辑调整
2、收据管理，退费来源筛选问题修复
3、收费汇总表展示类型调整
```

```javascript
v1.7.822 更新内容：
1、网校增加元萝卜标记展示
```

```javascript
v1.7.823 更新内容：
1、退费列表增加元萝卜退费权限
2、收据列表增加元萝卜收据作废权限
3、图片管理增加webp格式支持
4、结转列表增加元萝卜结转权限
5、退费详情物品展示优化
```

```javascript
v1.7.824 更新内容：
1、收据管理，物流信息查询优化
2、班级列表新接口
```

```javascript
v1.7.825 更新内容：
1、模板管理，创建基础调查问卷时，可以调用模板库内容
2、问卷管理，统计页新增了校区筛选
3、问卷管理，修复了统计页权限的问题
4、赛事管理，
5、banner管理
6、直营校月报，人数相关的数据，取整展示
7、学员出班后，对应的班级结业，学员不应再有结业出班记录的优化
8、课表与点名，支持试听后推送问卷
```

```javascript
v1.7.826 更新内容：
1、学员信息右侧详情组件中，退学操作时，退费原因添加校区id
```

```javascript
v1.7.827 更新内容：
1、更新: 在课程详情组件中修正课程时长汇总计算，将时长从秒转换为小时
```

```javascript
v1.7.828 更新内容：
1、问卷调查天剑校区参数
```

```javascript
v1.7.829 更新内容：
1、收据管理，快递单号的导入
2、修复已知问题：优惠券编辑异常情况、收费单据打印异常情况
```

```javascript
v1.7.830 更新内容：
1、收据管理，快递单号的导入更改为手机号
```

```javascript
v1.7.831 更新内容：
1、网校转介绍
2、新增租赁管理
3、学员信息添加元萝卜tab
4、物品新增租赁类型
5、收费管理新增物品租赁逻辑，新增收获方式，销售方式字段
6、收据管理新增收获方式，销售方式字段
```

```javascript
v1.7.832 更新内容：
1、直播管理新增字段 班型、实际出勤学员人数、未出勤并观看回放（30min以上）人数 、实际直播课程时长
2、租赁管理位置移动到报表中心
```

```javascript
v1.7.833 更新内容：
1、业绩归属人网校销售作废
```

```javascript
v1.7.834 更新内容：
1、收费类型配置不展示菜单的问题
2、试听管理，翻页清空勾选状态
```

```javascript
v1.7.835 更新内容：
1、收费类型配置不展示菜单的问题
3、新增班级时，自动新增班级序号优化
```

```javascript
v1.7.836 更新内容
1、进出库管理添加盘点功能
2、库存变动表添加盘点字段
3、排课管理移动排课，复制排课，去除上课时间筛选限制 （去除仅能选择3个月时间范围的限制）
4、意向列表的渠道筛选去除中文传参，以优化API请求参数，提升数据处理的准确性。
5、试听管理，试听列表-试听记录，导出数据异常，
6、直营校选择不同的收费类型在录入时无法生成订单
7、教务任务池，调整位置到表格上面一行
```

```javascript
v1.7.837 更新内容：
1、学员信息管理批量修改信息教务、课程顾问传值问题
```

```javascript
v1.7.838 更新内容：
1、直播管理课堂数据 导出
2、盘点仓库 输入搜索 重新构建
```

```javascript
v1.7.839 更新内容：
1.转介绍管理相关需求（不展示“推荐人课程顾问”、“推荐人教务”、“被推荐人班主任”、“被推荐人教务”字段）
2.推荐人班主任”和被“推荐人课程顾问”字段支持编辑，但是编辑后不影响学员信息管理课程顾问和班主任
3.转介绍管理-被推荐人侧增加列表列【学员是否报名正价课】，取消报名状态不变
```

```javascript
v1.7.840 更新内容：
1.直播管理外层添加批量导出功能
2.审批流增加金额分支判断（退费模版添加，增加金额范围的条件判断）
3.教务任务池，导出续费完成率（教务）
```

```javascript
v1.7.841 更新内容：
1.操作日志添加筛选“内容”，类型 添加不限
2.新增人力拆表功能
3、收据管理收款账户新增日期筛选
```

```javascript
v1.7.842 更新内容：
1.快递导入手机号更改为收据号
2.银行名称过长添加提示
3.直营校退费对接合思
```

```javascript
v1.7.843 更新内容：
1.学员信息管理上方tab当前“元萝卜”字段更名为，元萝卜租赁
2.学员信息管理上方tab增加页面“物品”，展示用户购买的所有物品。字段为“物品名称、物品金额、物品实交金额、购买时间”其中物品实交金额：若用户下单时选择优惠方式，此处展示按比例分摊后，物品的实交金额；（此条包含直营校学员，展示所有下单购买的物品，不仅仅只展示元萝卜！，租赁物品无需在此处展示）
3.退费申请-退费方式-退费项目合计：当前为通过上方选择退费数量自动计算出退费项目合计，目前需要对网校且为元萝卜订单用户做区分，若用户购买物品中包含“元萝卜物品”，即线上环境物品代码“QL1089-QL1094”,则退费项目合计拆分为（退费课程金额+退费物品金额），且均为输入框，金额为上方通过退费数量自动算出相应金额，但此处修改操作需做权限，且修改金额最大不能超过用户实际购买课程、物品的金额；
4.退费申请详情-退费项目合计此处无需拆分，但需要增加小问号图标，鼠标触碰后需要展示形式为（退课程金额xx.xx 退物品金额xx.xx）
5.租赁管理添加分摊状态、冲销方式
6.费用结转显示父子结构、元萝卜不允许与其他项目一起结转
7.租赁物品可退费不可结转
8.在收据管理中，课程名称后增加列（课程金额）物品名称后增加列（物品金额）；
9.在收据管理中，应退金额后增加两列（应退课程金额、应退物品金额）；
10.在收据管理中，实退金额后增加两列（实退课程金额、实退物品金额）；
```

```javascript
v1.7.844 更新内容：
1、发票开票，新增审批流逻辑
```

```javascript
v1.7.845 更新内容：
1、退费审批合思主体展示
```

```javascript
v1.7.846 更新内容：
1、租赁管理针对网校新增筛选、表格字段
```

```javascript
v1.7.847 更新内容：
1、前端获课 重写转化率报表
2、市场专员完成度报表
3、课程顾问任务池实际试听人次逻辑改为实际试听人头数
```

```javascript
v1.7.848 更新内容：
1、网校对账功能前期功能
```
