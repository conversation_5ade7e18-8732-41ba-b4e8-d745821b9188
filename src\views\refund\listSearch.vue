<template>
  <div>
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="3"
      @reset="reset"
      @search="searchVal"
    ></tg-search>
    <el-row class="tg-box--margin">
      <el-button
        type="primary"
        :disabled="!tableInfo.list.length"
        @click="exportTable"
        :loading="exportLoading"
        v-has="{ m: 'refund', o: 'export' }"
        class="tg-button--primary tg-button__icon"
      >
        <img
          src="@/assets/图片/export.png"
          alt
          class="tg-button__icon--large"
        />导出
      </el-button>
    </el-row>
    <div style="margin-left: 0px" class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :data="tableInfo.list"
        v-loading="tableInfo.loading"
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
        border
        @sort-change="sortChange"
      >
        <template v-for="(item, index) in table_title">
          <el-table-column
            v-if="item.show"
            :key="index"
            :fixed="item.fixed"
            :prop="item.props"
            :sortable="item.sort"
            :label="item.label"
            :min-width="item.width"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="item.props === 'status'">
                {{ refundStatusFilter(scope.row.status) }}
              </span>
              <div v-else-if="item.props === 'student_name'" class="copy_name">
                <el-button
                  class="tg-text--blue tg-table__name--ellipsis"
                  type="text"
                  @click="toDetail(scope.row)"
                  >{{ scope.row.student_name }}</el-button
                >
                <div v-copy="scope.row.student_name"></div>
              </div>
              <template v-else-if="item.props === 'mobile'">
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.mobile
                  }"
                ></mobileHyposensitization>
              </template>
              <!-- <el-button
                v-else-if="item.props === 'student_name'"
                class="tg-text--blue tg-table__name--ellipsis"
                type="text"
                @click="toDetail(scope.row)"
                >{{ scope.row.student_name }}</el-button
              > -->
              <span v-else-if="item.props == 'apply_time'">
                {{ getTime(scope.row.apply_time) }}
              </span>
              <span v-else-if="item.props == 'audit_time'">
                {{ getTime(scope.row.audit_time) }}
              </span>
              <span v-else-if="item.props == 'order_yop_id'">
                {{ scope.row.order_yop_id.join() }}
              </span>
              <span v-else-if="item.props == 'refundable_price'">
                {{ Number(scope.row.refundable_price).toFixed(2) }}
              </span>
              <span v-else-if="item.props == 'refund_price'">
                {{ Number(scope.row.refund_price).toFixed(2) }}
              </span>
              <span v-else-if="item.props == 'refund_channel'">
                {{ refundChannels[scope.row.refund_channel] }}
              </span>

              <span v-else-if="item.props == 'pay_time'">
                {{ getDate(scope.row.pay_time) }}
              </span>
              <span v-else-if="item.props === 'reason'">
                {{ scope.row.status === "pay_cancel" ? scope.row.reason : "" }}
              </span>
              <span v-else-if="item.props == 'performance_name'">
                {{ scope.row?.performance_name?.join("、") }}
              </span>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <!-- <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button
              @click.native="deleRow(scope.row)"
              size="small"
              v-if="scope.row.status == 'audited'"
              type="text"
              >删除</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ tableInfo.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev, pager, next,jumper"
          :total="tableInfo.total"
          :page-size="tableInfo.size"
          :current-page="tableInfo.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <apply-detail
      v-if="apply_detail_visible"
      :info="detailInfo"
      @close="apply_detail_visible = false"
      @refresh="refundHandle"
    ></apply-detail>
  </div>
</template>

<script>
import refundApi from "@/api/refund";
import timeFormat from "@/public/timeFormat";
import ApplyDetail from "./applyDetail.vue";
import { downLoadFile } from "@/public/downLoadFile";

export default {
  name: "RefundListSearch",
  components: {
    ApplyDetail
  },
  data() {
    return {
      exportLoading: false,
      apply_detail_visible: false,
      refundChannels: {
        back: "原路退回",
        cash: "虚拟退款",
        transfer: "银行转账"
      },
      search_title: [
        {
          props: "target",
          label: "学员信息",
          type: "input",
          show: true,
          placeholder: "请输入学员姓名/学号/手机号"
        },
        {
          props: "operator_id",
          label: "申请人",
          type: "mark_staff",
          show: true,
          is_leave: true,
          placeholder: "请选择申请人",
          selectOptions: []
        },
        {
          props: "serial_number",
          label: "审批单编号",
          type: "input",
          show: true
        },
        {
          props: "order_yop_id",
          label: "第三方交易单号",
          type: "input",
          show: true
        },
        {
          props: "applyDate",
          label: "申请日期",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "payDate",
          label: "付款日期",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        },
        {
          props: "refundStatus",
          label: "退费进度",
          type: "select",
          show: false,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: "unaudited",
              name: "待审核"
            },
            // {
            //   id: "audit_fail",
            //   name: "审批失败"
            // },
            // {
            //   id: "auditing",
            //   name: "审核中"
            // },
            {
              id: "unpaid",
              name: "待付款"
            },
            {
              id: "paid",
              name: "已付款"
            },
            {
              id: "cancel",
              name: "审批驳回"
            },
            {
              id: "pay_cancel",
              name: "付款驳回"
            },
            {
              id: "discard",
              name: "已作废"
            },
            {
              id: "revocation",
              name: "审批撤销"
            }
          ]
        },
        {
          props: "refund_channel",
          label: "退费方式",
          type: "select",
          show: false,
          selectOptions: [
            {
              id: "",
              name: "不限"
            },
            {
              id: "transfer",
              name: "银行转账"
            },
            {
              id: "cash",
              name: "虚拟退款"
            },
            {
              id: "back",
              name: "原路退回"
            }
          ]
        }
      ],
      searchForm: {
        applyDate: [],
        payDate: [],
        target: "",
        refundStatus: "",
        refund_channel: "",
        sort: "",
        operator_id: "",
        serial_number: "",
        order_yop_id: ""
      },
      tableInfo: {
        list: [],
        loading: false,
        total: 0,
        size: 10,
        page: 1
      },
      table_title: [
        {
          props: "department_name",
          label: "所属校区",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "hose_entity",
          label: "合思主体",
          show: true,
          width: 160
        },
        {
          props: "student_name",
          label: "学员姓名",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "mobile",
          label: "手机号",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "student_number",
          label: "学号",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "serial_number",
          label: "审批单编号",
          show: true,
          width: 120
        },
        {
          props: "order_yop_id",
          label: "第三方交易单号",
          show: true,
          width: 120
        },
        {
          props: "apply_time",
          label: "申请时间",
          show: true,
          width: 180,
          sort: "custom"
        },
        {
          props: "operator",
          label: "申请人",
          show: true,
          width: 100,
          sort: "custom"
        },
        {
          props: "audit_time",
          label: "审批通过时间",
          show: true,
          width: 180,
          sort: "custom"
        },
        {
          props: "pay_person",
          label: "付款人",
          show: true,
          width: 140,
          sort: "custom"
        },

        {
          props: "pay_time",
          label: "付款日期",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "refundable_price",
          label: "应退金额",
          show: true,
          width: 140,
          sort: "custom"
        },
        {
          props: "refund_price",
          label: "实退金额",
          show: true,
          width: 160,
          sort: "custom"
        },
        {
          props: "refund_channel",
          label: "退费方式",
          show: true,
          width: 110,
          sort: "custom"
        },
        {
          props: "status",
          label: "退费进度",
          show: true,
          width: 110,
          sort: "custom"
        },
        {
          props: "reason",
          label: "退费驳回原因",
          show: true,
          width: 150
        },
        {
          props: "tw_remark",
          label: "退费来源",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "performance_name",
          label: "业绩归属人",
          show: true,
          width: 150,
          sort: "custom"
        },
        {
          props: "class_num",
          label: "上课次数",
          show: true,
          width: 150,
          sort: "custom"
        }
      ],
      settlement_order_visible: false,
      detail_visible: false,
      refundOrderId: "",
      detailInfo: null
    };
  },
  computed: {
    schoolIds() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  watch: {
    table_title: {
      handler() {
        this.$nextTick(() => {
          this.$refs.table.doLayout();
        });
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    if (this.$_has({ m: "refund", o: "refundList" })) {
      this.search();
    }
  },
  methods: {
    refundStatusFilter(val) {
      const obj = this.search_title.find(
        (item) => item.props === "refundStatus"
      );
      if (obj) {
        return obj.selectOptions.find((item) => item.id === val)?.name;
      }
      return "";
    },
    refundHandle() {
      this.apply_detail_visible = false;
      this.search();
    },
    getTime(time) {
      return timeFormat.GetTime(time);
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    },
    detailClose() {
      this.detail_visible = false;
    },
    settClose() {
      this.settlement_order_visible = false;
    },
    // 查看详情
    toDetail(row) {
      this.refundOrderId = row.refund_id;
      refundApi
        .getRefundApplyDetail({
          order_id: row.refund_id,
          with_del: false
        })
        .then((res) => {
          if (res.data.data) {
            this.detailInfo = res.data.data;
            this.detailInfo.status = row.status || "";
            this.apply_detail_visible = true;
          } else {
            this.$message.info("查询信息为空！");
          }
        });
    },
    // 重置查询条件
    reset() {
      this.searchForm = {
        applyDate: [],
        payDate: [],
        target: "",
        refundStatus: "",
        refund_channel: "",
        sort: "",
        operator_id: "",
        serial_number: "",
        order_yop_id: ""
      };
      this.$refs.table.clearSort();
      this.tableInfo.page = 1;
      this.search();
    },
    sortChange(val) {
      const { prop, order } = val;
      let _oreder = "";
      if (order === "ascending") {
        _oreder = "asc";
      } else if (order === "descending") {
        _oreder = "desc";
      }
      this.searchForm.sort = `${prop} ${_oreder}`;
      this.searchVal();
    },
    searchVal() {
      this.tableInfo.page = 1;
      this.search();
    },
    sizeChange(val) {
      this.tableInfo.size = val;
      this.tableInfo.page = 1;
      this.search();
    },
    // 查询信息
    search() {
      const { page, size } = this.tableInfo;
      const {
        applyDate,
        sort,
        payDate,
        target,
        refundStatus,
        refund_channel,
        operator_id,
        serial_number,
        order_yop_id
      } = this.searchForm;
      const query = {
        page,
        page_size: size,
        search_begin: applyDate?.length > 0 ? applyDate[0] : "",
        search_end: applyDate?.length > 0 ? applyDate[1] : "",
        pay_time_begin: payDate?.length > 0 ? payDate[0] : "",
        pay_time_end: payDate?.length > 0 ? payDate[1] : "",
        department_id: this.schoolIds,
        student_info: target,
        status: refundStatus,
        refund_channel,
        sort,
        operator_id,
        serial_number,
        order_yop_id
      };
      this.tableInfo.loading = true;
      refundApi.getRefundApplyList(query).then((res) => {
        const { data } = res.data;
        if (res.data.code === 0) {
          this.tableInfo.list = data.results || [];
          this.tableInfo.total = data.count;
          this.tableInfo.loading = false;
        } else {
          this.tableInfo.loading = false;
        }
      });
    },
    // 删除订单
    deleRow() {
      this.$confirm("此操作将删除退费申请，确定要进行删除吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // refundApi
          //   .deleteOrder({
          //     orderId: row.orderId,
          //   })
          //   .then((res) => {
          //     if (res.data.code === 0) {
          //       this.$message.success("删除成功！");
          //       sleep(300).then(() => {
          //         this.search();
          //       });
          //     } else {
          //       this.$message.error("删除失败！");
          //     }
          //   });
        })
        .catch(() => {});
    },
    // 导出
    exportTable() {
      this.exportLoading = true;
      const {
        applyDate,
        sort,
        payDate,
        target,
        refundStatus,
        refund_channel,
        operator_id,
        serial_number,
        order_yop_id
      } = this.searchForm;
      const query = {
        search_begin: applyDate.length > 0 ? applyDate[0] : "",
        search_end: applyDate.length > 0 ? applyDate[1] : "",
        pay_time_begin: payDate.length > 0 ? payDate[0] : "",
        pay_time_end: payDate.length > 0 ? payDate[1] : "",
        student_info: target,
        status: refundStatus,
        refund_channel,
        department_id: this.schoolIds,
        sort,
        operator_id,
        serial_number,
        order_yop_id
      };
      refundApi.refundExport(query).then((res) => {
        downLoadFile(res, `退费申请`);
        this.exportLoading = false;
      });
    },
    // 改变页数
    currentChange(page) {
      this.tableInfo.page = page;
      this.search();
    }
  }
};
</script>

<style lang="less" scoped>
.t-a-c {
  text-align: center !important;
}
::v-deep .el-table td:last-child {
  border-right-color: transparent !important;
}
::v-deep .el-table__header th:last-child {
  border-right-color: transparent !important;
}
/deep/ .tg-search {
  div.tg-search__box:first-child .el-input {
    width: 280px;
  }
}

/deep/ .el-button--text {
  color: #157df0;
}
::v-deep.tg-button__icon > span {
  display: flex;
}
.tg-button__icon--normal {
  width: 10px;
  height: 11px;
  margin-right: 9px;
}
.tg-button__icon--large {
  width: 14px;
  height: 14px;
  margin-right: 8px;
}
</style>
