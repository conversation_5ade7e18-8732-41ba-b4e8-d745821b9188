import axios from "../http";
import Vue from "vue";
import qs from "qs";
// 获取岗位列表
export function getPostList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/questionnaire-service/admin/survey/getList?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建岗位
export function createSurvey(data) {
  return axios
    .post(`/api/questionnaire-service/admin/survey/createSurvey`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function getTypeList() {
  return axios
    .get(`/api/questionnaire-service/admin/survey/typeList`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function getSurvey(data) {
  return axios
    .get(`/api/questionnaire-service/admin/survey/getSurvey`, { params: data })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function updateMeta(data) {
  return axios
    .post(`/api/questionnaire-service/admin/survey/updateMeta`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function updateConf(data) {
  return axios
    .post(`/api/questionnaire-service/admin/survey/updateConf`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function deleteSurvey(data) {
  return axios
    .post(`/api/questionnaire-service/admin/survey/deleteSurvey`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function getBannerData() {
  return axios
    .get(`/api/questionnaire-service/admin/survey/getBannerData`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function updateEnabledStatus(data) {
  return axios
    .post(`/api/questionnaire-service/admin/survey/updateEnabledStatus`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function updatePostStatus(data) {
  return axios
    .post(`/api/questionnaire-service/admin/survey/updatePostStatus`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function metaInfo(data) {
  return axios
    .get(`/api/questionnaire-service/admin/survey/metaInfo`, { params: data })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function getSchema(data) {
  return axios
    .get(`/web/questionnaire-web-service/surveyWeb/getSchema`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function getWebSchema(data) {
  return axios
    .get(`/api/questionnaire-service/admin/survey/getSchema`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function createResponse(data) {
  return axios
    .post(`/web/questionnaire-web-service/surveyWeb/createResponse`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function getSurveyStudent(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/questionnaire-service/admin/survey/getSurveyStudent?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function getSurveyAnswerList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/questionnaire-service/admin/survey/answerList?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export function getSurveyStatistics(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/questionnaire-service/admin/survey/statistics?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export function getSurveyInfo(data) {
  return axios
    .get(`/web/questionnaire-web-service/public/getSurveyInfo`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
