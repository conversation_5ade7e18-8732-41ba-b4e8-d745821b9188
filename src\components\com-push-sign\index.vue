<!-- 弹窗推送电子签合同 -->
<template>
  <div class="comPushSign">
    <el-dialog
      title="推送电子签合同"
      :visible.sync="dialogVisible"
      v-if="dialogVisible"
      width="35%"
      :before-close="handleClose"
    >
      <!-- 收集姓名手机号 -->
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-width="150px"
        class="demo-ruleForm"
      >
        <el-form-item label="签署人姓名" prop="user_name">
          <el-input
            v-model="form.user_name"
            placeholder="请输入待实名真实姓名"
          ></el-input>
        </el-form-item>
        <el-form-item label="签署人手机号" prop="user_mobile">
          <el-input
            v-model="form.user_mobile"
            placeholder="请输入签署人手机号"
          ></el-input>
        </el-form-item>
        <el-form-item label="是否需要解除原合同">
          <el-radio-group v-model="form.check_type">
            <el-radio label="yes">是</el-radio>
            <el-radio label="no">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          :prop="form.check_type == 'yes' ? 'origin_contract_no' : ''"
          label="请选择需作废协议"
          v-if="form.check_type == 'yes'"
        >
          <el-button type="primary" size="small" @click="getOriginContractList"
            >选择需作废协议</el-button
          >
          {{ form.origin_contract_no }}
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          class="tg-button--plain"
          type="plain"
          :loading="pushSaveLoading"
          v-throttle="() => pushSigns('save')"
          >保存</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="() => pushSigns('push')"
          :loading="pushLoading"
          >推送</el-button
        >
      </span>
    </el-dialog>
    <comp-origin-list
      v-if="showOriginContractList"
      ref="compOriginList"
      :originContractList="originContractList"
      :originContract="form.origin_contract_no"
      @originCancellation="originCancellation"
      @originClose="originClose"
    ></comp-origin-list>
  </div>
</template>

<script>
import electronicApi from "@/api/electronic";
import refundApi from "@/api/refund";
import compOriginList from "./dialog/comp-origin-list.vue";
export default {
  name: "comPushSign",
  props: {
    student_id: String
  },
  components: {
    compOriginList
  },
  data() {
    return {
      form: {
        user_mobile: "",
        user_name: "",
        template_id: "",
        receipt_no: "",
        is_save: true,
        check_type: "",
        origin_contract_no: ""
      },
      rules: {
        user_name: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          { max: 5, message: "最多可输入5个字", trigger: "blur" },
          {
            validator: function (rule, value, callback) {
              if (/^[\u4e00-\u9fa5]+$/.test(value) === false) {
                callback(new Error("请输入文字"));
              } else {
                // 校验通过
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        user_mobile: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { min: 11, max: 11, message: "请输入正确的手机号", trigger: "blur" },
          {
            validator: function (rule, value, callback) {
              if (/^[0-9]*$/.test(value) === false) {
                callback(new Error("请输入"));
              } else {
                // 校验通过
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        check_type: [
          { required: true, message: "请选择是否勾选协议", trigger: "blur" }
        ],
        origin_contract_no: [
          { required: true, message: "请选择需作废协议", trigger: "blur" }
        ]
      },
      orderId: "",
      dialogVisible: false,
      pushLoading: false,
      pushSaveLoading: false,
      originContractList: [],
      showOriginContractList: false
    };
  },
  computed: {},
  watch: {},
  methods: {
    async onShow(receipt_no, type, mobile) {
      this.clearData();
      this.receipt_no = receipt_no;
      this.dialogVisible = true;
      if (type) {
        const res = await refundApi.getCarryoverInfo({ receipt_no });
        console.log(res);
      } else {
        const res = await electronicApi.ContractInfo({ receipt_no });
        console.log(res);
        this.form.user_mobile = res.data.data.user_mobile || mobile;
        this.form.user_name = res.data.data.user_name;
        this.form.check_type = res.data.data.check_type;
        this.form.origin_contract_no = res.data.data.origin_contract_no;
        const response = await electronicApi.ContractCheck({
          receipt_no,
          check_type: this.form.check_type
        });
        if (res.data.code !== 0) {
          this.$message.error(res.data.message);
        } else {
          this.form.check_type = response.data.data;
        }
      }
    },
    // 保存
    pushSigns(type) {
      this.form.is_save = type === "save";
      this.form.receipt_no = this.receipt_no;
      if (
        this.form.check_type === "yes" &&
        this.form.origin_contract_no === ""
      ) {
        this.$message.error("请选择需作废协议");
      }
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.form.is_save) {
            this.pushLoading = true;
          } else {
            this.pushSaveLoading = true;
          }
          electronicApi
            .ContractApply({ ...this.form })
            .then((res) => {
              if (res.data.code === 0) {
                this.$message.success(
                  `${this.form.is_save ? "保存" : "推送"}成功`
                );
                this.pushLoading = false;
                this.pushSaveLoading = false;
                this.dialogVisible = false;
                this.$emit("pushSuccess");
              } else {
                this.$message.error(res.data.message);
                this.pushLoading = false;
                this.pushSaveLoading = false;
              }
            })
            .catch(() => {
              this.pushLoading = false;
              this.pushSaveLoading = false;
            });
        }
      });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    clearData() {
      this.form = {
        user_mobile: "",
        user_name: "",
        template_id: "",
        receipt_no: "",
        is_save: true,
        check_type: "",
        origin_contract_no: ""
      };
    },
    async getOriginContractList() {
      const res = await electronicApi.ContractList({
        student_id: this.student_id,
        status: "done"
      });
      if (res.data.code !== 0) {
        this.$message.error(res.data.message);
      } else {
        this.originContractList = res.data.data.results || [];
        if (this.originContractList.length > 0) {
          this.showOriginContractList = true;
        } else {
          this.$message.error("暂无需要作废的协议");
        }
      }
    },
    originClose() {
      this.showOriginContractList = false;
    },
    originCancellation(row) {
      this.form.origin_contract_no = row;
      this.showOriginContractList = false;
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__error {
  // display: block !important;
}
.el-form-item {
  // margin-bottom: 12px !important;
}
</style>
