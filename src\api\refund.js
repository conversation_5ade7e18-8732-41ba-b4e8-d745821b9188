import qs from "qs";
import axios from "../http";
const fetch = require("../fetch");

// 费用详情列表
function getCostDetailList(data) {
  return fetch.fetchGet(`/api/order-service/admin/refund/good-list`, {
    params: data
  });
}
// 学员的可退费列表
function getRefundItemsList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/refund/to-refund-list?${new_data}`
  );
}
// 学员的可结转列表
function getCarryOverItemsList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/transfer/to-transfer-list?${new_data}`
  );
}
// 退费申请详情
function getRefundApplyDetail(data) {
  return fetch.fetchGet(`/api/order-service/admin/refund/refund-info`, {
    params: data
  });
}
// 结转详情
function getCarryOverDetail(data) {
  return fetch.fetchGet(`/api/order-service/admin/transfer/transfer-info`, {
    params: data
  });
}
// 退费管理-交费记录
function getPayList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/order-service/admin/receipt/list?${new_data}`);
}
// 根据订单和商品匹配退费来源
function orderChannelType(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/order/channel-type?${new_data}`
  );
}
// 退费申请保存
function refundApplySave(data) {
  return fetch.fetchPost(`/api/order-service/admin/refund/refund`, data, "");
}

// 退费驳回
function refundCancel(data) {
  return fetch.fetchPost(`/api/order-service/admin/refund/cancel`, data, "");
}

// 确认付款
function refundPay(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/refund/commit-pay`,
    data,
    ""
  );
}
// 费用结转保存
function transferSave(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/transfer/transfer`,
    data,
    ""
  );
}

// 退费申请列表
function getRefundApplyList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/refund/refund-list?${new_data}`
  );
}
function getTransferList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/transfer/transfer-list?${new_data}`
  );
}

// 退费申请导出
function refundExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/refund/refund-list-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 结转申请导出
function transferExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/transfer/transfer-list-export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
function getCarryoverInfo(data) {
  console.log(data);
  return fetch.fetchGet(`/api/order-service/admin/receipt/carryover/detail`, {
    params: data
  });
}

// 退款支行列表
function getRefundBranchList(data) {
  return fetch.fetchGet(`/api/order-service/admin/refund/hose-branch-list`, {
    params: data
  });
}

export default {
  getCostDetailList,
  getRefundItemsList,
  refundApplySave,
  transferSave,
  getRefundApplyList,
  getRefundApplyDetail,
  getCarryOverItemsList,
  getTransferList,
  getCarryOverDetail,
  getPayList,
  orderChannelType,
  refundExport,
  transferExport,
  getCarryoverInfo,
  refundCancel,
  refundPay,
  getRefundBranchList
};
