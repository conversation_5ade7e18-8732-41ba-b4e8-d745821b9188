import axios from "../http";
import Vue from "vue";
import qs from "qs";
function getFeeTransferList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/report-center-service/admin/fee-transfer-log/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportFeeTransferList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/fee-transfer-log/export?${new_data}`,
      {
        params: { exportData: 1 }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async GetFeeTransferList(data) {
    return getFeeTransferList(data);
  },
  async ExportFeeTransferList(data) {
    return exportFeeTransferList(data);
  }
};
