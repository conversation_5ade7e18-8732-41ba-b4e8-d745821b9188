import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 查找对应校区的教室数据
function getCurrentClass(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/schoolroom/list-with-scheduling?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async GetCurrentClass(data) {
    return getCurrentClass(data);
  }
};
