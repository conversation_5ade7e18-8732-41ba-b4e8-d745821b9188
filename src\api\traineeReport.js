import qs from "qs";
import { fetchGet, fetchPost, fetchExport } from "../fetch";
// 学员报告列表
function getList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/school-service/admin/feedback/list?${new_data}`);
}
// 报告详情
function getInfo(data) {
  return fetchGet(`/api/school-service/admin/feedback/info`, {
    params: data
  });
}
// 导出
function exportData(data) {
  return fetchExport(`/api/school-service/admin/feedback/export`, data);
}
// 撤销
function revoke(data) {
  return fetchPost(`/api/school-service/admin/feedback/cancel`, data, "");
}
export default { getList, getInfo, exportData, revoke };
