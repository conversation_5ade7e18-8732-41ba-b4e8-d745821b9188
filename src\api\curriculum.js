import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 课程列表
function getCourseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/course-service/course-mapping/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

//
// 获取课程类型
function getCourseServiceCourseMapType(data) {
  return axios
    .get(`/api/course-service/course/map/type`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程属性
function getCourseProperty(data) {
  return axios
    .get(`/api/course-service/course/map/property`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取年份
function getCourseYear(data) {
  return axios
    .get(`/api/course-service/course/map/year`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程类别
function getCourseCategory(data) {
  return axios
    .get(`/api/course-service/course/map/category`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 课程列表
  async GetCourseList(data) {
    return getCourseList(data);
  },
  // 获取课程类型
  async GetCourseServiceCourseMapType(data) {
    return getCourseServiceCourseMapType(data);
  },
  async getCourseProperty(data) {
    return getCourseProperty(data);
  },
  async getCourseYear(data) {
    return getCourseYear(data);
  },
  async getCourseCategory(data) {
    return getCourseCategory(data);
  }
};
