import { fetchGet, fetchPut, fetchPost, fetchDel } from "../fetch";
import qs from "qs";
// 渠道管理，新增父渠道
function createChannel(data) {
  return fetchPost("/api/market-service/channel/create", data);
}
// 获取父渠道列表import qs from "qs";
function getChannelList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/channel/list?${new_data}`);
}
// 获取渠道树形数据列表
function getChannelTreeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/channel/tree-list?${new_data}`);
}
// 根据父渠道id得到父渠道
function getChannelById(data) {
  return fetchGet("/api/market-service/channel/info", data);
}
// 更新父渠道
function updateChannel(data) {
  return fetchPut("/api/market-service/channel/update", data);
}
// 删除父渠道
function delChannel(data) {
  return fetchDel("/api/market-service/channel/delete", data);
}
// 创建子渠道
function createSubChannel(data) {
  return fetchPost("/api/market-service/subchannel/create", data);
}
// 根据子渠道id得到子渠道
function getSubChannelById(data) {
  return fetchGet("/api/market-service/subchannel/info", data);
}
// 更新子渠道
function updateSubChannel(data) {
  return fetchPut("/api/market-service/subchannel/update", data);
}
// 删除子渠道
function delSubChannel(data) {
  return fetchDel("/api/market-service/subchannel/delete", data);
}
// 根据父渠道id查询子渠道,子渠道列表
function getSubChannelByChannelId(data) {
  return fetchGet("/api/market-service/subchannel/list", data);
}

// 创建费用
function createChannelFee(data) {
  return fetchPost("/api/market-service/channelfee/create", data);
}
// 删除费用
function delChannelFee(data) {
  return fetchDel("/api/market-service/channelfee/delete", data);
}

// 费用列表，根据渠道id查费用
function getChannelFeeByChannelId(data) {
  return fetchGet("/api/market-service/channelfee/list", data);
}
// 搜索
function searchChannelList(data) {
  return fetchGet("/api/market-service/channel/list", data);
}
// 由市场渠道得到一级渠道
function getChannelListByChannelSource(data) {
  return fetchGet(
    "/api/market-service/channel/get-channel-list-by-channelsourceid",
    data
  );
}
// 获取市场渠道列表
function getChannelSourceList(data) {
  return fetchGet("/api/market-service/channel/channel-source-list", data);
}

// 同步
function syncChannel(data) {
  return fetchGet("/api/market-service/channel/sync", data);
}

export default {
  async getChannelList(data) {
    return getChannelList(data);
  },
  async getChannelTreeList(data) {
    return getChannelTreeList(data);
  },
  async createChannel(data) {
    return createChannel(data);
  },
  async getChannelById(data) {
    return getChannelById(data);
  },
  async updateChannel(data) {
    return updateChannel(data);
  },
  async createSubChannel(data) {
    return createSubChannel(data);
  },
  async getSubChannelById(data) {
    return getSubChannelById(data);
  },
  async updateSubChannel(data) {
    return updateSubChannel(data);
  },
  async getChannelFeeByChannelId(data) {
    return getChannelFeeByChannelId(data);
  },
  async createChannelFee(data) {
    return createChannelFee(data);
  },
  async getSubChannelByChannelId(data) {
    return getSubChannelByChannelId(data);
  },
  async delChannel(data) {
    return delChannel(data);
  },
  async delSubChannel(data) {
    return delSubChannel(data);
  },
  async delChannelFee(data) {
    return delChannelFee(data);
  },
  async searchChannelList(data) {
    return searchChannelList(data);
  },
  async getChannelListByChannelSource(data) {
    return getChannelListByChannelSource(data);
  },
  async syncChannel(data) {
    return syncChannel(data);
  },
  async getChannelSourceList(data) {
    return getChannelSourceList(data);
  }
};
