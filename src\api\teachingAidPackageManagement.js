import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 教辅包详情
function teachAidPackageInfo(data) {
  return axios
    .get(`/api/course-service/teach-aid-package/info`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包详情
function teachAidPackageArticleInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/course-service/teach-aid-package/article-info?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包列表
function teachAidPackageList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/course-service/teach-aid-package/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 教辅包详情
function teachAidPackageSellList(data) {
  return axios
    .get(`/api/course-service/teach-aid-package/sell-list`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建教辅包
function teachAidPackageCreate(data) {
  return axios
    .post(`/api/course-service/teach-aid-package/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除教辅包
function teachAidPackageDelete(data) {
  return axios
    .post(`/api/course-service/teach-aid-package/delete`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 编辑教辅包
function teachAidPackageUpdate(data) {
  return axios
    .post(`/api/course-service/teach-aid-package/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async teachAidPackageInfo(data) {
    return teachAidPackageInfo(data);
  },
  async teachAidPackageArticleInfo(data) {
    return teachAidPackageArticleInfo(data);
  },
  async teachAidPackageList(data) {
    return teachAidPackageList(data);
  },
  async teachAidPackageSellList(data) {
    return teachAidPackageSellList(data);
  },
  async teachAidPackageCreate(data) {
    return teachAidPackageCreate(data);
  },
  async teachAidPackageDelete(data) {
    return teachAidPackageDelete(data);
  },
  async teachAidPackageUpdate(data) {
    return teachAidPackageUpdate(data);
  }
};
