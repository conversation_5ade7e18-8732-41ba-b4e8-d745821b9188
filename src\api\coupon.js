import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 小程序优惠券管理
export function studentCouponList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/studentCoupon/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 优惠券明细
export function couponHistory(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/coupon-service/studentCoupon/history?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 手动增加优惠券
export function couponAdd(data) {
  return axios
    .post(`/api/questionnaire-service/admin/studentCoupon/add`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 核销优惠券
export function couponRedeem(data) {
  return axios
    .post(`/api/questionnaire-service/admin/studentCoupon/redeem`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 优惠券列表
export function couponList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/questionnaire-service/admin/coupon/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 更新优惠券
export function couponUpdate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/coupon/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除优惠券
export function couponDelete(data) {
  return axios
    .post(`/api/questionnaire-service/admin/coupon/delete`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 创建优惠券
export function couponCreate(data) {
  return axios
    .post(`/api/questionnaire-service/admin/coupon/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 优惠券详情
export function couponInfo(data) {
  return axios
    .get(`/api/questionnaire-service/admin/coupon/info`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
