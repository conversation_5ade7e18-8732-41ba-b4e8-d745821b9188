<template>
  <div class="market-channel">
    <div class="market-channel-header">
      <div class="title">渠道市场分析表</div>
      <!-- 搜索条件区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="时间范围:">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="searchData">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
            <el-button type="success" @click="exportExcel">导出</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :span-method="spanMethod"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
        v-loading="loading"
      >
        <!-- 年级列 -->
        <el-table-column prop="grade" label="年级" width="80" align="center" />

        <!-- 班型列 -->
        <el-table-column
          prop="classType"
          label="班型"
          width="80"
          align="center"
        />

        <!-- 校区列 -->
        <el-table-column
          prop="campus"
          label="校区"
          width="120"
          align="center"
        />

        <!-- 动态渠道列组 -->
        <el-table-column
          v-for="channel in channelList"
          :key="channel.id"
          :label="channel.name"
          align="center"
        >
          <!-- 线上-人数(报名) -->
          <el-table-column
            :prop="`${channel.id}_online_signup_count`"
            label="人数"
            width="60"
            align="center"
          >
            <template slot="header">
              <div class="multi-header">
                <div class="header-main">线上-人数(报名)</div>
              </div>
            </template>
            <template slot-scope="scope">
              <span class="number-cell">{{
                scope.row[`${channel.id}_online_signup_count`] || 0
              }}</span>
            </template>
          </el-table-column>

          <!-- 线上-金额 -->
          <el-table-column
            :prop="`${channel.id}_online_amount`"
            label="金额"
            width="80"
            align="center"
          >
            <template slot="header">
              <div class="multi-header">
                <div class="header-main">线上-金额</div>
              </div>
            </template>
            <template slot-scope="scope">
              <span class="amount-cell">{{
                formatAmount(scope.row[`${channel.id}_online_amount`])
              }}</span>
            </template>
          </el-table-column>

          <!-- 线上-退费金额 -->
          <el-table-column
            :prop="`${channel.id}_online_refund`"
            label="退费"
            width="80"
            align="center"
          >
            <template slot="header">
              <div class="multi-header">
                <div class="header-main">线上-退费金额</div>
              </div>
            </template>
            <template slot-scope="scope">
              <span class="refund-cell">{{
                formatAmount(scope.row[`${channel.id}_online_refund`])
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 线下-市场渠道数据 -->
        <el-table-column label="线下-市场渠道数据" align="center">
          <el-table-column
            prop="offline_market_count"
            label="人数"
            width="60"
            align="center"
          >
            <template slot-scope="scope">
              <span class="number-cell">{{
                scope.row.offline_market_count || 0
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_market_amount"
            label="金额"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="amount-cell">{{
                formatAmount(scope.row.offline_market_amount)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_market_refund"
            label="退费"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="refund-cell">{{
                formatAmount(scope.row.offline_market_refund)
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 线下-校区自主数据 -->
        <el-table-column label="线下-校区自主数据" align="center">
          <el-table-column
            prop="offline_campus_count"
            label="人数"
            width="60"
            align="center"
          >
            <template slot-scope="scope">
              <span class="number-cell">{{
                scope.row.offline_campus_count || 0
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_campus_amount"
            label="金额"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="amount-cell">{{
                formatAmount(scope.row.offline_campus_amount)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_campus_refund"
            label="退费"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="refund-cell">{{
                formatAmount(scope.row.offline_campus_refund)
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 线下-广告 -->
        <el-table-column label="线下-广告" align="center">
          <el-table-column
            prop="offline_ad_count"
            label="人数"
            width="60"
            align="center"
          >
            <template slot-scope="scope">
              <span class="number-cell">{{
                scope.row.offline_ad_count || 0
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_ad_amount"
            label="金额"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="amount-cell">{{
                formatAmount(scope.row.offline_ad_amount)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_ad_refund"
            label="退费"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="refund-cell">{{
                formatAmount(scope.row.offline_ad_refund)
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 线下-转介绍 -->
        <el-table-column label="线下-转介绍" align="center">
          <el-table-column
            prop="offline_referral_count"
            label="人数"
            width="60"
            align="center"
          >
            <template slot-scope="scope">
              <span class="number-cell">{{
                scope.row.offline_referral_count || 0
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_referral_amount"
            label="金额"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="amount-cell">{{
                formatAmount(scope.row.offline_referral_amount)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_referral_refund"
            label="退费"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="refund-cell">{{
                formatAmount(scope.row.offline_referral_refund)
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 线下-自然量数据 -->
        <el-table-column label="线下-自然量数据" align="center">
          <el-table-column
            prop="offline_natural_count"
            label="人数"
            width="60"
            align="center"
          >
            <template slot-scope="scope">
              <span class="number-cell">{{
                scope.row.offline_natural_count || 0
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_natural_amount"
            label="金额"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="amount-cell">{{
                formatAmount(scope.row.offline_natural_amount)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="offline_natural_refund"
            label="退费"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="refund-cell">{{
                formatAmount(scope.row.offline_natural_refund)
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 合计 -->
        <el-table-column label="合计" align="center">
          <el-table-column
            prop="total_count"
            label="人数"
            width="60"
            align="center"
          >
            <template slot-scope="scope">
              <span class="total-cell">{{ scope.row.total_count || 0 }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="total_amount"
            label="金额"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="total-cell">{{
                formatAmount(scope.row.total_amount)
              }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="total_refund"
            label="退费"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="total-cell">{{
                formatAmount(scope.row.total_refund)
              }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import marketChannelApi from "@/api/marketChannel"; // 需要创建对应的API文件

export default {
  name: "MarketChannel",
  data() {
    return {
      loading: false,
      searchForm: {
        dateRange: []
      },
      tableData: [],
      channelList: [], // 动态渠道列表
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0
      }
    };
  },
  mounted() {
    this.getChannelList();
    this.getTableData();
  },
  methods: {
    // 获取渠道列表
    async getChannelList() {
      try {
        const res = await marketChannelApi.getChannelList();
        if (res.data.code === 0) {
          this.channelList = res.data.data;
        }
      } catch (error) {
        console.error("获取渠道列表失败:", error);
      }
    },

    // 获取表格数据
    async getTableData() {
      this.loading = true;
      try {
        const params = {
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          startDate: this.searchForm.dateRange?.[0],
          endDate: this.searchForm.dateRange?.[1]
        };

        const res = await marketChannelApi.getMarketChannelData(params);
        if (res.data.code === 0) {
          this.tableData = res.data.data.list;
          this.pagination.total = res.data.data.total;
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 搜索
    searchData() {
      this.pagination.page = 1;
      this.getTableData();
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        dateRange: []
      };
      this.pagination.page = 1;
      this.getTableData();
    },

    // 导出Excel
    async exportExcel() {
      try {
        const params = {
          startDate: this.searchForm.dateRange?.[0],
          endDate: this.searchForm.dateRange?.[1]
        };

        const res = await marketChannelApi.exportMarketChannelData(params);
        // 处理文件下载
        const blob = new Blob([res.data]);
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `渠道市场分析表_${new Date().getTime()}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);

        this.$message.success("导出成功");
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败");
      }
    },

    // 格式化金额
    formatAmount(amount) {
      if (amount === null || amount === undefined) return "0.00";
      return Number(amount).toFixed(2);
    },

    // 表格合并行方法
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 可以根据需要实现行合并逻辑
      return [1, 1];
    },

    // 表头样式
    headerCellStyle({ row, column, rowIndex, columnIndex }) {
      return {
        backgroundColor: "#f5f7fa",
        color: "#606266",
        fontWeight: "bold",
        textAlign: "center"
      };
    },

    // 单元格样式
    cellStyle({ row, column, rowIndex, columnIndex }) {
      return {
        textAlign: "center"
      };
    },

    // 分页大小改变
    handleSizeChange(size) {
      this.pagination.pageSize = size;
      this.pagination.page = 1;
      this.getTableData();
    },

    // 当前页改变
    handleCurrentChange(page) {
      this.pagination.page = page;
      this.getTableData();
    }
  }
};
</script>

<style lang="scss" scoped>
.market-channel {
  padding: 16px;

  .market-channel-header {
    margin-bottom: 16px;

    .title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #303133;
    }

    .search-area {
      background: #fff;
      padding: 16px;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .search-form {
        margin: 0;
      }
    }
  }

  .table-container {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px;

    // 表格样式
    ::v-deep .el-table {
      .multi-header {
        .header-main {
          font-size: 12px;
          line-height: 1.2;
          white-space: normal;
          word-break: break-all;
        }
      }

      // 数字单元格样式
      .number-cell {
        color: #2d8cf0;
        font-weight: 500;
      }

      // 金额单元格样式
      .amount-cell {
        color: #19be6b;
        font-weight: 500;
      }

      // 退费单元格样式
      .refund-cell {
        color: #ff9900;
        font-weight: 500;
      }

      // 合计单元格样式
      .total-cell {
        color: #ed4014;
        font-weight: bold;
      }

      // 表头样式
      .el-table__header-wrapper {
        .el-table__header {
          th {
            background-color: #f5f7fa !important;

            .cell {
              font-weight: bold;
              color: #303133;
            }
          }
        }
      }
    }

    .pagination-container {
      margin-top: 16px;
      text-align: right;
    }
  }
}

// 响应式处理
@media (max-width: 1200px) {
  .market-channel {
    .table-container {
      overflow-x: auto;
    }
  }
}
</style>
