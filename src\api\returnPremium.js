import { fetchGet } from "../fetch";
import qs from "qs";

// 退费率
function refundRatioList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/refund/refund-ratio?${new_data}`);
}

// 退费率导出
function refundRatioExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/refund/refund-ratio-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}
// 退费占比-产品
function refundProportionList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/refund/refund-proportion?${new_data}`
  );
}
// 退费占比-产品导出
function refundProportionExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/refund/refund-proportion-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

// 退费占比-原因
function refundReasonList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/refund/refund-reason?${new_data}`);
}

// 退费占比-原因导出
function refundReasonExport(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/refund/refund-reason-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

export default {
  refundRatioList,
  refundRatioExport,
  refundProportionList,
  refundProportionExport,
  refundReasonList,
  refundReasonExport
};
