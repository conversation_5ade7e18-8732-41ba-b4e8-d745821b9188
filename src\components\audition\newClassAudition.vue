<!--新开班试听-->
<template>
  <el-dialog
    :visible="true"
    title="新开班试听"
    class="new-class-audition"
    custom-class="new-class-audition"
    width="1016px"
    :before-close="cancel"
  >
    <el-form
      class="new-class-audition__content tg-box--margin"
      :model="form"
      :rules="rules"
      ref="form"
      label-width="94px"
      :inline="true"
    >
      <el-form-item label="校区名称" required prop="department_name">
        <el-input
          placeholder="请选择所属校区"
          readonly
          show-word-limit
          :validate-event="false"
          v-model="form.department_name"
          class="tg-select--dialog"
          :disabled="true"
        >
          <!-- <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i> -->
          <img
            slot="suffix"
            src="../../assets/图片/icon_more.png"
            alt=""
            class="btn__img--dotted"
          />
        </el-input>
      </el-form-item>
      <el-form-item
        label-width="78px"
        label="课程形式"
        required
        prop="scheduling_form"
      >
        <el-select
          :popper-append-to-body="false"
          clearable
          placeholder="请选择课程形式"
          v-model="form.scheduling_form"
        >
          <el-option
            v-for="(item, index) in scheduling_list"
            :key="index"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="课程名称"
        required
        label-width="78px"
        class="custom--select"
        prop="course_name"
      >
        <el-input
          v-model="form.course_name"
          readonly
          placeholder="请选择课程"
          @click.native="choose_course_visible = true"
          @mouseenter.native="course_flag = true"
          @mouseleave.native="course_flag = false"
          :class="{ 'border--active': course_flag }"
        >
          <img
            :src="
              !course_flag
                ? require('../../assets/图片/icon_more.png')
                : require('../../assets/图片/icon_more_ac.png')
            "
            slot="suffix"
            alt=""
            class="more"
          />
        </el-input>
      </el-form-item>
      <el-row class="tg-box--margin">
        <el-form-item label="任课老师" required prop="teacher_name">
          <course-staff
            :check_id.sync="form.teacher_id"
            :check_name.sync="form.teacher_name"
            :department_id="department_id"
            staff_placeholder="请选择任课老师"
          ></course-staff>
        </el-form-item>
        <el-button
          type="primary"
          class="tg-button--primary"
          :disabled="form.teacher_id == ''"
          @click="openTeacherDialog"
          >查看老师空闲时间</el-button
        >
      </el-row>
      <el-row class="tg-box--margin">
        <el-form-item label="助教老师" class="tg-form--large">
          <market-staff
            :check_ids.sync="form.assistant_teacher_id"
            :check_names.sync="form.assistant_teacher_name"
            :obj="form.assistant_teacher_map"
            :staff_placeholder="'请选择助教老师'"
            :img_type="'square'"
          ></market-staff>
        </el-form-item>
      </el-row>
      <el-row class="tg-box--margin">
        <el-form-item label="上课时间" required prop="start_date">
          <el-date-picker
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择日期"
            v-model="form.start_date"
            :picker-options="date_picker_options"
            popper-class="tg-date-picker"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          label="上课教室"
          required
          v-if="form.scheduling_form == 'offline'"
          prop="school_room_id"
        >
          <el-select
            :popper-append-to-body="false"
            placeholder="请选择上课教室"
            v-model="form.school_room_id"
            clearable
          >
            <template v-for="(item, index) in class_room_list">
              <el-option
                :label="item.name"
                :value="item.id"
                :key="index"
                v-if="item.is_enabled"
              ></el-option
            ></template>
          </el-select>
        </el-form-item>
        <el-button
          type="primary"
          class="tg-button--primary"
          @click="openClassDialog"
          v-if="form.scheduling_form == 'offline'"
          :disabled="form.school_room_id == ''"
          >查看空闲教室</el-button
        >
      </el-row>
      <el-row class="tg-box--margin">
        <el-form-item label="开始时间" required prop="start_time">
          <el-time-picker
            popper-class="tg-time-picker"
            placeholder="请选择开始时间"
            format="HH:mm"
            value-format="HH:mm"
            v-model="form.start_time"
            @change="timeChange('begin')"
          >
          </el-time-picker>
          <!-- 
            :picker-options="{
              selectableRange: '06:00:00 - 23:59:59'
            }" -->
        </el-form-item>
        <el-form-item label="上课时长" required prop="minute">
          <!-- <el-time-picker
            format="HH:mm"
            popper-class="tg-time-picker"
            placeholder="请选择上课时长"
            value-format="HH:mm"
            :picker-options="{
              selectableRange: '00:00:00 - 12:59:59',
            }"
            v-model="form.duration"
            @change="timeChange('duration')"
          >
          </el-time-picker> -->
          <el-select
            :popper-append-to-body="false"
            clearable
            placeholder="时"
            v-model="form.hours"
            class="class_time_select"
            @change="classTimeChange"
          >
            <el-option
              v-for="(item, index) in hours"
              :key="index"
              :label="item"
              :value="index"
            ></el-option>
          </el-select>
          <span style="margin-right: 10px">小时</span>
          <el-select
            :popper-append-to-body="false"
            filterable
            allow-create
            clearable
            placeholder="分"
            v-model="form.minute"
            class="class_time_select"
            @change="classTimeChange"
            @blur="Nameblur($event)"
            default-first-option
          >
            <el-option
              v-for="(item, index) in minute"
              :key="index"
              :label="item"
              :value="parseInt(index) * 5"
            ></el-option>
          </el-select>
          <span>分钟</span>
        </el-form-item>
        <el-form-item label="结束时间">
          <el-time-picker
            format="HH:mm"
            value-format="HH:mm"
            popper-class="tg-time-picker"
            placeholder="请选择结束时间"
            v-model="form.end_time"
            :picker-options="end_picker_options"
            @change="timeChange('end')"
          >
          </el-time-picker>
        </el-form-item>
      </el-row>
      <el-row class="tg-box--margin">
        <el-form-item label="上课学员列表" class="tg-form--large">
          <div class="student-list__wrap">
            <div
              class="student"
              v-for="(item, index) in student_list"
              :key="index"
            >
              {{ item.student_name }}
            </div>
          </div>
        </el-form-item>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-checkbox v-model="form.check_audit">检查上课冲突</el-checkbox>
      <div>
        <el-button class="tg-button--plain" type="plain" @click="cancel"
          >取消</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="submitData"
          >确定</el-button
        >
      </div>
    </div>
    <choose-course
      :check_id.sync="form.course_id"
      :check_name.sync="form.course_name"
      :check_arr.sync="course_check_arr"
      :choose_course_visible="choose_course_visible"
      :department_id="department_id"
      attribute_type="class"
      v-if="choose_course_visible"
      type="radio"
      :status="true"
      @close="choose_course_visible = false"
    ></choose-course>
    <teacher-free-time
      :id="form.teacher_id"
      :name="form.teacher_name"
      :department_id="form.department_id"
      :check_map="{ [form.teacher_id]: form.teacher_name }"
      v-if="teacher_free_time_visible"
      @close="teacher_free_time_visible = false"
    ></teacher-free-time>

    <class-free-time
      :id="[form.school_room_id]"
      :department_id="[form.department_id]"
      v-if="class_free_time_visible"
      @close="class_free_time_visible = false"
    ></class-free-time>
  </el-dialog>
</template>
<script>
import ChooseCourse from "@/components/studentInfo/chooseCourse.vue";
import CourseStaff from "@/components/staff/courseStaff.vue";
import marketStaff from "@/components/staff/marketStaff";
import TeacherFreeTime from "@/components/freeTime/teacherFreeTime.vue";
import ClassFreeTime from "@/components/freeTime/classFreeTime";
import classRoomApi from "@/api/classroomManagement";
import classManagementApi from "@/api/classManagement";
import auditionApi from "@/api/audition";
import moment from "moment";

export default {
  data() {
    return {
      form: {
        department_id: "",
        department_name: "",
        course_id: "",
        course_name: "",
        teacher_id: "",
        teacher_name: "",
        assistant_teacher_id: [],
        assistant_teacher_map: {},
        assistant_teacher_name: [],
        check_audit: true,
        start_time: "",
        end_time: "",
        duration: "",
        school_room_id: "",
        scheduling_form: "",
        week_day: "",
        hours: 0,
        minute: 0
      },
      rules: {},
      school_tree_visible: false,
      choose_course_visible: false,
      course_flag: false,
      teacher_free_time_visible: false,
      class_free_time_visible: false,
      class_room_list: [],
      scheduling_list: [],
      course_check_arr: [],
      end_picker_options: {},
      date_picker_options: {
        disabledDate(time) {
          return time.getTime() < new Date().getTime() - 24 * 60 * 60 * 1000;
        }
      },
      hours: [],
      minute: [],
      class_time_form: {
        hours: "",
        minute: ""
      }
    };
  },
  props: {
    department_name: String,
    department_id: String,
    student_list: Array
  },
  mounted() {
    this.$set(this.form, "department_id", this.department_id);
    this.$set(this.form, "department_name", this.department_name);
    this.getSchedulingMap();
    this.getClassRoomList({ department_id: this.department_id });
    this.getTimeList();
  },
  watch: {
    "form.start_date": {
      handler(val) {
        if (val != null) {
          this.form.end_date = this.form.start_date;
          const week_day = moment(val).day();
          this.$set(this.form, "week_day", week_day.toString());
        }
      },
      immediate: true,
      deep: true
    },
    "form.duration": {
      handler(val) {
        const arr_time = val.split(":");
        this.form.hours = Number(arr_time[0]);
        this.form.minute = Number(arr_time[1]);
      }
    }
  },
  methods: {
    Nameblur(e) {
      if (e.target.value) {
        this.form.minute = e.target.value;
        this.classTimeChange();
      }
    },
    cancel() {
      this.$emit("close");
    },
    openTeacherDialog() {
      this.teacher_free_time_visible = true;
    },
    openClassDialog() {
      this.class_free_time_visible = true;
    },
    timeChange(type) {
      if (type === "begin") {
        const { start_time, duration } = this.form;
        const new_duration = this.changeSecond(duration);
        const start = this.changeSecond(start_time);
        const end = start + new_duration;
        this.$set(this.form, "end_time", this.toHoursMinutes(end));
        this.end_picker_options = {
          selectableRange: `'${start_time}:00 - 23:59:59'`
        };
      } else if (type === "duration") {
        const { start_time, duration } = this.form;
        const new_duration = this.changeSecond(duration);
        const start = this.changeSecond(start_time);
        const end = start + new_duration;
        this.$set(this.form, "end_time", this.toHoursMinutes(end));
      } else {
        const { end_time, start_time } = this.form;
        const duration =
          this.changeSecond(end_time) - this.changeSecond(start_time);
        this.$set(this.form, "duration", this.toHoursMinutes(duration));
      }
    },
    changeSecond(val) {
      if (val == null || val === "") return "";
      const new_val_arr = val.split(":");
      const new_val =
        Number(new_val_arr[0] * 3600) + Number(new_val_arr[1] * 60);
      return new_val;
    },
    toHoursMinutes(val) {
      if (val == null || val === "") return "";
      const h = Math.floor(val / 3600);
      let hrStr = h.toString();
      if (hrStr.length === 1) hrStr = "0" + hrStr;
      // 分钟位
      const min = Math.floor((val / 60) % 60);
      let minStr = min.toString();
      if (minStr.length === 1) minStr = "0" + minStr;
      return `${hrStr}:${minStr}`;
    },
    getClassRoomList(data) {
      classRoomApi.GetSchoolroomList(data).then((res) => {
        this.class_room_list = res.data == null ? [] : res.data;
      });
    },
    async getSchedulingMap(d) {
      const { data } =
        await classManagementApi.GetSchoolServiceSchedulingMapForm(d);
      const arr = data == null ? [] : data;
      this.scheduling_list = [];
      for (const key in arr) {
        this.scheduling_list.push({ id: key, name: arr[key] });
      }
    },
    classTimeChange() {
      // if (
      //   typeof this.form?.minute === "string" &&
      //   this.form?.minute?.indexOf("分钟") > -1
      // ) {
      //   this.form.new_minute = this.form.minute;
      //   this.form.new_minute = this.form.new_minute.substr(
      //     0,
      //     this.form?.new_minute?.indexOf("分钟")
      //   );
      // }
      console.log(this.form.minute);
      this.form.duration =
        this.zeroFill(this.form.hours) + ":" + this.zeroFill(this.form.minute);
      const { start_time, duration } = this.form;
      const new_duration = this.changeSecond(duration);
      const start = this.changeSecond(start_time);
      const end = start + new_duration;
      this.$set(this.form, "end_time", this.toHoursMinutes(end));
    },
    zeroFill(val) {
      if (val > 9) {
        return val + "";
      } else {
        return "0" + val;
      }
    },
    submitData() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const {
            assistant_teacher_id,
            check_audit,
            course_id,
            end_date,
            department_id,
            start_date,
            teacher_id,
            start_time,
            end_time,
            duration,
            school_room_id,
            scheduling_form,
            week_day
          } = this.form;
          if (duration === "00:00" || duration === "") {
            this.$message.info("上课时长不能小于0！");
            return;
          }
          const obj = {
            assistant_teacher_id,
            check_audit,
            class_time_data: [
              {
                start_time,
                end_time,
                duration: this.changeSecond(duration),
                school_room_id,
                scheduling_form,
                week_day
              }
            ],
            course_id,
            department_id,
            end_date,
            start_date,
            student_list: this.student_list.map((item) => item.student_id),
            teacher_id
          };
          try {
            const { data } = await auditionApi.createNewClassAudition(obj);
            if (data === "success") {
              this.$message.success("创建成功");
              this.$emit("really");
            }
          } catch (e) {
            console.error(e);
          }
        } else {
          this.$message.error("请输入必填项！");
          return false;
        }
      });
    },
    getTimeList() {
      for (let i = 0; i < 12; i++) {
        this.minute.push(i * 5);
      }
      for (let i = 0; i < 12; i++) {
        this.hours.push(i);
      }
    }
  },
  components: {
    ChooseCourse,
    CourseStaff,
    marketStaff,
    TeacherFreeTime,
    ClassFreeTime
  }
};
</script>
<style lang="less" scoped>
.new-class-audition {
  ::v-deep & > .el-dialog__body {
    padding: 0 12px;
    height: 573px;
  }
  ::v-deep .el-form-item__label,
  ::v-deep .el-form-item__content {
    line-height: 32px;
  }
  .custom--select {
    ::v-deep .el-input .el-input__inner {
      cursor: pointer;
    }
    ::v-deep .el-input .el-input__suffix-inner {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: inherit;
      margin-right: 10px;
      cursor: pointer;
    }
    img {
      height: 4px;
      width: 16px;
    }
  }
  ::v-deep .el-form--inline div.el-form-item.tg-form--large {
    margin-right: 0;
  }
  .tg-form--large {
    ::v-deep .permission_select,
    ::v-deep .el-input {
      width: 884px;
    }
    ::v-deep .border--active {
      &:after {
        width: 884px;
      }
    }
  }
  .student-list__wrap {
    width: 884px;
    box-sizing: border-box;
    border: 1px solid #d0dce7;
    border-radius: 4px;
    display: inline-flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 16px;
    height: 241px;
    overflow-y: auto;
    align-content: flex-start;
    .student {
      padding: 0 22px;
      box-sizing: border-box;
      border: 1px solid @base-color;
      background: #ebf4ff;
      border-radius: 4px;
      height: 30px;
      line-height: 30px;
      font-size: @text-size_normal;
      font-family: @text-famliy_medium;
      color: @text-color_second;
      text-align: center;
      margin-bottom: 16px;
      margin-right: 16px;
    }
    .student:nth-child(8n) {
      margin-right: 0;
    }
  }
  .dialog-footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
.class_time_select {
  margin-right: 5px;
  /deep/ .el-input {
    width: 70px;
  }
}
</style>
