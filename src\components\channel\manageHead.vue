<!--管理名称头部-->
<template>
  <div class="tg-content__head">
    <el-button type="primary">{{ headTitle }}</el-button>
    <div class="tg-text">
      <i class="el-icon-delete tg-content__icon"></i>
      <p>功能演示</p>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {};
  },
  props: {
    headTitle: {
      type: String,
      default: "",
      require: true
    }
  }
};
</script>
<style lang="less" scoped>
.tg-content__head {
  display: flex;
  flex-direction: row;
  align-items: center;
  .tg-text {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 20px;
    cursor: pointer;
  }
  .tg-content__icon {
    color: #2f7fff;
    font-size: 16px;
  }
  p {
    font-size: 14px;
    color: #2f7fff;
    text-decoration: underline;
    margin-left: 5px;
  }
}
::v-deep .el-button--mini {
  font-size: 14px;
}
</style>
