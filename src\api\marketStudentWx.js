import axios from "../http";
import Vue from "vue";
import qs from "qs";
const fetch = require("../fetch");
// 获取期次列表
function getPeriodList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/period/list-all?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取销售列表
function getSaleList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/tw-sale/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取表头列表
function getTableHeadList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/tw-sale/field-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新配置内容
function updateConfig(data) {
  return axios
    .post(`/api/market-service/tw-sale/field-fill`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 表头新增
function addTableHead(data) {
  return axios
    .post(`/api/market-service/tw-sale/field-create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新列表信息
function updateList(data) {
  return axios
    .post(`/api/market-service/tw-sale/content-fill`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导出
function exportList(data) {
  return fetch.fetchExport(`/api/market-service/tw-sale/export`, data);
}
// 销售列表合计
function getSaleListTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/tw-sale/list-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除
function deleteTableHead(data) {
  return axios
    .post(`/api/market-service/tw-sale/field-delete`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 修改加微状态
function updateWxStatus(data) {
  return axios
    .post(`/api/student-service/student/update-wechat-status-by-customer`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 修改年龄状态
function updateAge(data) {
  return axios
    .post(
      `/api/market-service/customer/updateAge?customer_id=${data.customer_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  getPeriodList,
  getSaleList,
  getTableHeadList,
  updateConfig,
  addTableHead,
  updateList,
  exportList,
  getSaleListTotal,
  deleteTableHead,
  updateWxStatus,
  updateAge
};
