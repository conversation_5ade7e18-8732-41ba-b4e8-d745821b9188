import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 获取转介绍列表
export function getReferralList(query) {
  const new_data = qs.stringify(query, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/recommend/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 发放奖励
export function sendReward(body) {
  return axios
    .post(`/api/order-service/admin/recommend/send-cancel`, body)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 导出转介绍数据
export function exportReferral(query) {
  return axios
    .get(`/api/order-service/admin/recommend/export`, {
      params: query,
      responseType: "blob"
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新转介绍
export function updateData(body) {
  return axios
    .post(`/api/order-service/admin/recommend/update-recommend`, body)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
