<template>
  <div>
    <el-dialog
      :title="type == 'add' ? '新增班级信息' : '修改班级信息'"
      :visible="dialogState"
      width="1020px"
      class="tg-dialogs--custom"
      :before-close="back"
      custom-class="tg-dialogs--custom"
    >
      <div class="tg-dialogs__content--custom">
        <el-form
          ref="form_refs"
          :model="form"
          :inline="true"
          :rules="rules"
          class="tg-form"
        >
          <el-form-item prop="course_name" required>
            <div class="tg-label--custom tg-form--required">课程名称</div>
            <div class="custom--select">
              <el-input
                :disabled="type === 'editor'"
                v-model="form.course_name"
                readonly
                :placeholder="`请输入课程名称`"
                @click.native="clickCourseName"
                class="class-course"
              >
                <img
                  src="../../assets/图片/icon_more.png"
                  slot="suffix"
                  alt=""
                  class="more"
                />
              </el-input>
              <choose-course
                v-if="choose_course_visible && type === 'add'"
                type="radio"
                :isChild="true"
                attribute_type="class"
                :check_id.sync="form.course_id"
                :check_name.sync="form.course_name"
                :check_arr.sync="course_check_arr"
                :choose_course_visible="choose_course_visible"
                :department_id="addDepartmentId"
                @close="handleCourseClose"
                :status="true"
              ></choose-course>
            </div>
          </el-form-item>

          <el-form-item prop="name">
            <div class="tg-label--custom">班级名称</div>
            <el-input
              disabled
              type="input"
              v-model="form.name"
              placeholder=""
            ></el-input>
          </el-form-item>

          <el-form-item required prop="alias_name">
            <div class="tg-label--custom tg-form--required">班级别名</div>
            <el-input
              type="input"
              v-model="form.alias_name"
              placeholder="请输入班级别名"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <div class="tg-label--custom" style="color: #f56c6c">
              天弈课程名称
            </div>
            <el-tooltip
              v-if="ty_disabled"
              :content="form.ty_link_course_name"
              placement="bottom"
              effect="light"
              popper-class="tg-tooltip"
            >
              <el-input
                disabled
                type="input"
                v-model="form.ty_link_course_name"
                placeholder=""
              ></el-input>
            </el-tooltip>
            <el-select
              :disabled="disableInput"
              placeholder="请选择天弈课程名称"
              v-model="form.ty_link_course_name"
              v-else
            >
              <el-option
                v-for="(item, index) in course_info.ty_link_course"
                :value="item"
                :label="item"
                :key="index"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="header_teacher_name" required>
            <div class="tg-label--custom tg-form--required">班主任</div>
            <course-staff
              v-if="
                type === 'add'
                  ? true
                  : typeof form.header_teacher_id !== 'undefined'
              "
              :check_id.sync="form.header_teacher_id"
              :check_name.sync="form.header_teacher_name"
              :staff_placeholder="'请选择班主任'"
            ></course-staff>
          </el-form-item>
          <el-form-item :required="!disableInput" prop="pre_enrolment_numb">
            <div
              :class="{ 'tg-form--required': !disableInput }"
              class="tg-label--custom"
            >
              预招人数
            </div>
            <el-input-number
              :precision="0"
              :controls="false"
              style="width: 100%"
              :disabled="!$_has({ m: 'class', o: 'updatePreNum' })"
              :min="0"
              class="min-student-input"
              @blur="minNumZero('pre_enrolment_numb')"
              v-model="form.pre_enrolment_numb"
              placeholder="请输入预招人数"
            ></el-input-number>
          </el-form-item>
          <el-form-item prop="student_mobile">
            <div class="tg-label--custom">默认教室</div>
            <el-select
              :disabled="disableInput"
              v-model="form.school_room_id"
              placeholder="请选择默认教室"
              class="tg-select"
              :popper-append-to-body="false"
            >
              <template v-for="item in schoolroom_list">
                <el-option
                  :disabled="!item.is_enabled"
                  :key="item.name"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item :required="!disableInput" prop="pre_lesson_numb">
            <div
              :class="{ 'tg-form--required': !disableInput }"
              class="tg-label--custom"
            >
              计划排课
            </div>
            <el-input-number
              :disabled="isEditPreLessonNumb"
              :min="0"
              :precision="0"
              :controls="false"
              style="width: 100%"
              @blur="minNumZero('pre_lesson_numb')"
              v-model="form.pre_lesson_numb"
              placeholder="请输入计划排课次数"
              class="min-student-input"
            ></el-input-number>
          </el-form-item>
          <el-form-item :required="!disableInput" prop="minimum_student_numb">
            <div
              :class="{ 'tg-form--required': !disableInput }"
              class="tg-label--custom"
            >
              最低开班人数
            </div>
            <el-input-number
              :min="1"
              :precision="0"
              :controls="false"
              style="width: 100%"
              @blur="minNumZero('minimum_student_numb')"
              v-model="form.minimum_student_numb"
              placeholder="请输入最低开班人数"
              class="min-student-input"
              :disabled="disableInput"
            >
            </el-input-number>
          </el-form-item>

          <el-form-item :required="!disableInput" prop="pre_start_time">
            <div
              :class="{ 'tg-form--required': !disableInput }"
              class="tg-label--custom"
            >
              计划开班日期
            </div>
            <el-date-picker
              :disabled="type === 'editor' || disableInput"
              style="width: 220px"
              v-model="form.pre_start_time"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              popper-class="tg-date-picker tg-date--range"
              @change="startChange"
            >
            </el-date-picker>
          </el-form-item>

          <el-form-item :required="!disableInput" prop="pre_end_time">
            <div
              :class="{ 'tg-form--required': !disableInput }"
              class="tg-label--custom"
            >
              计划结业日期
            </div>
            <el-date-picker
              :disabled="disableInput"
              style="width: 220px"
              v-model="form.pre_end_time"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
              popper-class="tg-date-picker tg-date--range"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>

          <!-- <el-form-item required prop="department_name">
            <div class="tg-label--custom tg-form--required">所属校区</div>
            type === 'editor' @click.native="clickDepartment_name"
            <el-input
              :disabled="true"
              style="width: 226px; margin-left: -16px"
              placeholder="请选择所属校区"
              readonly
              show-word-limit
              :validate-event="false"
              v-model="form.department_name"
            >
              <i slot="suffix" class="el-input__icon el-icon-arrow-down"></i>
            </el-input>
          </el-form-item> -->
          <el-form-item prop="schoolroom_list">
            <div class="tg-label--custom">班级类型</div>
            <el-select
              v-model="form.classroom_type"
              placeholder="请选择班级类型"
              class="tg-select"
              :disabled="type === 'editor'"
              :popper-append-to-body="false"
              @change="classroomTypeChange"
            >
              <el-option
                v-for="(value, key, index) in schoolServiceClassroomMapType"
                :key="index"
                :value="key"
                :label="value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            :required="!disableInput"
            prop="renew_time"
            v-if="
              addDepartmentId === 'cnprbbm9ojcs73ekg8v0' ||
              addDepartmentId === '2' ||
              addDepartmentId === 'ci1fmgdsrj8s73b043a0' ||
              addDepartmentName === '聂卫平围棋网校-新'
            "
          >
            <div
              :class="{ 'tg-form--required': !disableInput }"
              class="tg-label--custom"
            >
              续报时间点
            </div>
            <el-date-picker
              :disabled="disableInput"
              style="width: 220px"
              v-model="form.renew_time"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="选择日期"
              popper-class="tg-date-picker tg-date--range"
              :picker-options="pickerOptions"
            >
            </el-date-picker>
          </el-form-item>
          <!-- <el-form-item required prop="course_attribute">
            <div class="tg-form--required tg-label--custom">课程属性</div>
            <el-select
              v-model="form.course_attribute"
              placeholder="请选择课程属性"
              class="tg-select"
              disabled
              :popper-append-to-body="false"
            >
              <el-option
                v-for="item in course_attribute_list"
                :key="item.config_value"
                :value="item.config_value"
                :label="item.config_name"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item class="tg-form-item--custom" prop="memo">
            <div class="tg-label--custom">备注</div>
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
              v-model="form.memo"
            >
            </el-input>
          </el-form-item>
        </el-form>
        <div class="is-valid__wrap other-info">
          <span class="record__title">其他信息</span>
          <el-form
            ref="otherForm"
            :rules="rules"
            :model="form"
            :inline="true"
            class="other tg-box--margin"
          >
            <el-form-item prop="teacher_id" required label="任课老师">
              <course-staff
                v-if="
                  type === 'add' ? true : typeof form.teacher_id !== 'undefined'
                "
                :check_id.sync="form.teacher_id"
                :check_name.sync="form.teacher_name"
              ></course-staff>
            </el-form-item>
            <el-form-item label="助教">
              <market-staff
                v-if="
                  type === 'add'
                    ? true
                    : typeof form.assistant_teacher_map !== 'undefined'
                "
                :check_ids.sync="form.assistant_teacher_id"
                :check_names.sync="form.assistant_teacher_name"
                :obj="form.assistant_teacher_map"
              ></market-staff>
            </el-form-item>
          </el-form>
        </div>
        <div class="custom-table tg-box--margin">
          <div class="tg-box--border"></div>
          <el-table
            :data="form.class_time_data"
            ref="tableData"
            tooltip-effect="dark"
            class="tg-table"
            :header-cell-style="{ 'background-color': '#F5F8FC' }"
          >
            <el-table-column prop="week_day" label="星期">
              <template slot-scope="scope">
                {{ weekOptions[scope.row[scope.column.property]] }}
              </template>
            </el-table-column>
            <el-table-column prop="start_time" label="开始时间">
              <template slot-scope="scope">
                <span
                  v-if="
                    weekTimeOverlappingArr[scope.row.week_day].indexOf(
                      scope.row.start_time
                    ) > -1
                  "
                  style="color: red"
                  >{{ scope.row.start_time }}</span
                >
                <span v-else>{{ scope.row.start_time }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="end_time" label="结束时间">
              <template slot-scope="scope">
                <span
                  v-if="
                    weekTimeOverlappingArr[scope.row.week_day].indexOf(
                      scope.row.end_time
                    ) > -1
                  "
                  style="color: red"
                  >{{ scope.row.end_time }}</span
                >
                <span v-else>{{ scope.row.end_time }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="scheduling_form" label="课程形式">
              <template slot-scope="scope">
                {{
                  scope.row[scope.column.property] === "offline"
                    ? "线下课程"
                    : "线上课程"
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="school_room_name"
              label="教室"
              show-overflow-tooltip
            ></el-table-column>

            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button
                  @click.native.prevent="
                    del(scope.$index, form.class_time_data)
                  "
                  type="text"
                  class="tg-text--green"
                  size="small"
                >
                  移除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="tg-box--margin tg-button__wrap" @click="addTo">
            <img
              class="tg-button--pointer"
              src="../../assets/图片/icon_add.png"
              alt=""
            />
            <span type="text" class="tg-button--pointer">添加上课时间</span>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="back"
          >返回</el-button
        >
        <el-button
          class="tg-button--primary"
          type="primary"
          v-throttle="() => determine('form')"
          >保存</el-button
        >
      </span>
      <!-- <school-tree
        :flag.sync="school_tree_visible"
        :id.sync="form.department_id"
        :name.sync="form.department_name"
        type="radio"
      >
      </school-tree> -->
    </el-dialog>
    <el-dialog :visible.sync="createSuccessDialog" title="提示" width="30%">
      <div style="text-align: center">
        <p style="margin-bottom: 10px">
          <img
            style="width: 28px; height: 28px; vertical-align: middle"
            src="@/assets/图片/submit_success.png"
          />
          新增班级成功
        </p>
        <p>
          班级名称：<span style="color: #409eff">{{ form.name }}</span>
        </p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button
          class="tg-button--plain"
          type="plain"
          @click="$emit('close', false)"
          >确定</el-button
        >
      </span>
    </el-dialog>
    <addTo
      :list="form.class_time_data"
      @close="Addto = false"
      v-if="Addto"
      @add="addList"
      :classroom_type="form.classroom_type"
      :courseId="form.course_id"
      :departmentId="addDepartmentId"
      :departmentName="addDepartmentName"
    >
    </addTo>
  </div>
</template>
<script>
import marketStaff from "@/components/staff/marketStaff"; // 任课老师
import courseStaff from "@/components/staff/courseStaff"; // 助教
import addTo from "../../components/classManagement/addTo.vue"; // 新增班级
import chooseCourse from "../studentInfo/chooseCourse.vue";
// import schoolTree from "@/components/schoolTree/schoolTree"; //校区弹框
// import moment from "moment";
import { timeIsOverlapping } from "@/public/utils.js";
import {
  getCourseRuleInfo,
  getCourseInfoBasic
  // getCourseConfigByType
} from "@/api/courseManagement.js";
import classManagementApi from "@/api/classManagement.js";
export default {
  components: { marketStaff, courseStaff, addTo, chooseCourse },

  data() {
    // const checkNum = (rule, value, callback) => {
    //   const reg = /^[1-9]\d*$/; // 整数
    //   const reg2 = /^[1-9]\d*\.\d*|0\.\d*[1-9]\d*$/; // 小数
    //   if (!value) {
    //     return callback(new Error("不能为空"));
    //   }
    //   setTimeout(() => {
    //     if (!reg.test(value)) {
    //       if (reg2.test(value)) {
    //         callback(new Error("请输入整数"));
    //       } else {
    //         callback(new Error("请输入符合规则的数字"));
    //       }
    //     } else if (Number(value) > 9999) {
    //       callback(new Error("请输入1-9999的数字"));
    //     } else {
    //       callback();
    //     }
    //   }, 100);
    // };
    return {
      dialogState: true,
      weekOptions: [
        "星期日",
        "星期一",
        "星期二",
        "星期三",
        "星期四",
        "星期五",
        "星期六"
      ],
      choose_course_visible: false,
      Addto: false,
      value1: "",
      school_tree_visible: false, // 校区弹框
      form: {
        class_time_data: [],
        minimum_student_numb: "", // 最低开班人数
        name: "", // 班级名
        course_rule_class_room: "", // 规则班级名称
        alias_name: "", // 班级别名
        memo: "", // 备注
        teacher_id: "", // 老师id
        assistant_teacher_id: [], // 助教id
        course_id: "", // 课程id
        course_name: "",
        department_id: "", // 校区
        pre_end_time: "", // 计划结业日期
        pre_start_time: "", // 计划开班时间
        pre_enrolment_numb: "", // 预招人数
        pre_lesson_numb: "", // 计划排课次数
        classroom_type: "normal", // 班级类型,
        course_attribute: "", // 班级类型
        header_teacher_id: "", // 班主任id
        school_room_id: "", // 教室id
        assistant_teacher_name: [], // 助教
        department_name: "", //
        ty_link_course_name: "",
        renew_time: ""
      },
      // course_attribute_list: [],
      employee_list: [],
      valueType: "",
      value: "",
      options: [],
      rules: {
        course_name: [{ required: true, message: "", trigger: "blur" }],
        alias_name: [{ required: true, message: "", trigger: "blur" }],
        // department_name: [{ required: true, message: "", trigger: "blur" }],
        // pre_end_time: [{ required: true, message: "", trigger: "blur" }],
        // pre_start_time: [{ required: true, message: "", trigger: "blur" }],

        // minimum_student_numb: [
        //   { required: true, message: "", trigger: "blur" },
        //   {
        //     validator: checkNum,
        //     message: "请输入大于0的正整数",
        //     trigger: "blur"
        //   }
        // ],
        // pre_lesson_numb: [{ required: true, message: "", trigger: "blur" }],
        // pre_enrolment_numb: [{ required: true, message: "", trigger: "blur" }],

        // name: [{ required: true, message: "", trigger: "blur" }],
        course_id: [{ required: true, message: "", trigger: "blur" }]
        // course_attribute: [{ required: true, message: "", trigger: "change" }]
      },
      pickerOptions: {},
      course_check_arr: [],
      course_info: {},
      ty_disabled: false,
      classroomIndex: "",
      createSuccessDialog: false
    };
  },
  props: {
    id: {
      type: String
    },
    visible: {
      type: Boolean
    },
    type: {
      type: String
    },
    addDepartmentId: String,
    addDepartmentName: String
  },
  computed: {
    // 班级类型为补课班，试听班时，部分输入框不可编辑
    disableInput() {
      return (
        this.form.classroom_type === "audition" ||
        this.form.classroom_type === "reschedule"
      );
    },
    isEditPreLessonNumb() {
      if (this.$_has({ m: "course", o: "pre_lesson_numb" })) {
        return false;
      } else {
        return this.type === "editor" || this.disableInput;
      }
    },
    // 获取教室列表
    schoolroom_list() {
      return this.$store.getters.doneGetSchoolroomList;
    },
    // 获取班级类型
    schoolServiceClassroomMapType() {
      return this.$store.getters.doneGetSchoolServiceClassroomMapType;
    },
    schoolServiceClassroomCreate() {
      return this.$store.getters.doneGetSchoolServiceClassroomCreate;
    },
    // 获取班级详情
    schoolServiceClassroomInfo() {
      return this.$store.getters.doneGetSchoolServiceClassroomInfo;
    },
    success() {
      return this.$store.getters.doneSchoolServiceClassSuccess;
    },
    // 按星期分组出重复时间段
    weekTimeOverlappingArr() {
      const { class_time_data } = this.form;
      const obj = this.$lodash.groupBy(class_time_data, function (item) {
        return item.week_day;
      });
      const obj1 = {};
      for (const key in obj) {
        obj1[key] = timeIsOverlapping(obj[key], "start_time", "end_time");
      }
      return obj1;
    },
    isoverlapping() {
      let bool = false;
      for (const key in this.weekTimeOverlappingArr) {
        if (this.weekTimeOverlappingArr[key].length > 0) {
          bool = true;
        }
      }
      return bool;
    },
    all_permission() {
      return this.$store.getters.doneGetPermissionInfo;
    }
  },
  watch: {
    // 转
    success(new_bool) {
      if (new_bool) {
        if (this.type === "add") {
          this.createSuccessDialog = true;
        } else {
          this.$emit("close", false);
        }
      }
    },
    schoolServiceClassroomCreate(data) {
      this.form.name = data;
    },
    schoolServiceClassroomInfo(data) {
      console.log(data);
      this.form = data;
      this.form.class_time_data =
        data.class_time_data == null ? [] : data.class_time_data;
      this.form.id = data.id;
      this.ty_disabled = !!data.ty_link_course_name;
      this.getTYCoursLink(data.course_id);
      this.pickerOptions = {
        disabledDate: (date) => {
          return date.getTime() < new Date(this.form.pre_start_time).getTime();
        }
      };
    },
    "form.course_name": {
      handler(newVal, oldVal) {
        if (this.type === "add") {
          if (newVal !== oldVal) {
            this.getCourseRuleInfo(this.form.course_id);
            this.getTYCoursLink(this.form.course_id);
          }
        }
        // if (this.type === "add") {
        //   this.form.classroom_type = "normal";
        // }
      },
      deep: false,
      immediate: false
    }
  },
  created() {
    this.$store.dispatch("getSchoolroomList", {
      department_id: this.addDepartmentId
    });
    // 课程属性列表
    // getCourseConfigByType({}, "course_attribute").then((res) => {
    //   if (res.data) {
    //     this.course_attribute_list = res.data;
    //   }
    // });
    this.$store.dispatch("getSchoolServiceClassroomMapType", {});
    this.form.department_id = this.addDepartmentId;
    this.form.department_name = this.addDepartmentName;
  },
  mounted() {
    // this.form.department_name = this.department_name;
    // this.form.department_id = this.department_id;
  },
  methods: {
    handleCourseClose() {
      if (this.form.course_name) {
        this.form.class_time_data = [];
      }
      this.choose_course_visible = false;
    },
    classroomTypeChange(val) {
      const course_name = this.course_info.course_name ?? "";
      // const { course_rule_class_room } = this.form;
      this.form.pre_enrolment_numb = 100;
      this.form.pre_lesson_numb = 9999;

      this.changeResetForm();
      if (val === "audition") {
        this.form.name = course_name + "_试听班";
      } else if (val === "reschedule") {
        this.form.name = course_name + "_补课班";
      } else {
        this.form.name = "";
        this.form.pre_lesson_numb = "";
        this.getCourseRuleInfo(this.form.course_id);
      }
    },
    changeResetForm() {
      this.form.school_room_id = "";
      this.form.minimum_student_numb = 1;
      this.form.pre_end_time = "";
      this.form.pre_start_time = "";
      this.form.ty_link_course_name = "";
    },
    async getCourseRuleInfo(d) {
      if (d) {
        const { data } = await getCourseRuleInfo(d);
        if (data) {
          this.form.pre_enrolment_numb = data.max_student_numb || "";
          this.form.minimum_student_numb = data.mini_student_numb || "";
          this.form.pre_lesson_numb = data.plan_lesson_numb || "";
          this.form.course_rule_class_room = data.classroom_name || "";
          this.form.course_attribute = data.course_attribute || "";
          // const department_id = this.addDepartmentId;
          // const params = {
          //   course_id: this.form.course_id,
          //   course_rule_class_room: data.classroom_name,
          //   department_id
          // };

          // classManagementApi.getClassRoomNum(params).then((res) => {
          // if (res.data !== "") {
          // const index = res.data + 1;
          // this.classroomIndex = index;
          const { classroom_type } = this.form;
          let ext = "";
          if (classroom_type === "audition") {
            ext = "_试听班";
            this.form.name = data.classroom_name + ext;
          } else if (classroom_type === "reschedule") {
            ext = "_补课班";
            this.form.name = data.classroom_name + ext;
          } else {
            this.form.name = "";
          }
          // }
          // });
        }
      }
    },
    async getTYCoursLink(id) {
      const { data } = await getCourseInfoBasic({ id });
      console.log(data);
      this.course_info = data;
      this.form.course_attribute = "" + this.course_info.course_attribute;
      console.log("course_attribute :>> ", this.form.course_attribute);
    },
    getClassRoomRuleNum() {
      return classManagementApi;
    },

    clickDepartment_name() {
      if (this.type === "edit") {
        return;
      }
      this.school_tree_visible = true;
    },
    clickCourseName() {
      if (this.type === "edit") {
        return;
      }
      this.choose_course_visible = true;
    },
    determine() {
      this.$refs.form_refs.validate((valid) => {
        if (valid) {
          this.$refs.otherForm.validate((valid2) => {
            if (valid2) {
              if (this.isoverlapping) {
                this.$message.error(
                  "相同星期时间段有重叠部分，请检查后重新提交！"
                );
                return false;
              }

              const { pre_start_time, pre_end_time } = this.form;
              this.form.pre_end_time = pre_end_time
                ? this.moment(this.form.pre_end_time).format("YYYY-MM-DD")
                : "";

              this.form.pre_start_time = pre_start_time
                ? this.moment(this.form.pre_start_time).format("YYYY-MM-DD")
                : "";
              this.form.pre_enrolment_numb = parseInt(
                this.form.pre_enrolment_numb
              );
              this.form.pre_lesson_numb = parseInt(this.form.pre_lesson_numb);
              this.form.minimum_student_numb = parseInt(
                this.form.minimum_student_numb
              );
              this.form.classroom_category = this.course_info.course_genre;
              if (this.type === "add") {
                this.$store.commit("setSchoolServiceClassSuccess", "");
                this.$store.dispatch(
                  "getSchoolServiceClassroomCreate",
                  this.form
                );
              } else {
                this.$store.commit("setSchoolServiceClassSuccess", "");
                this.$store.dispatch(
                  "getSchoolServiceClassroomUpdate",
                  this.form
                );
              }
            } else {
              this.$message.info("请输入必填项");
              return false;
            }
          });
        } else {
          this.$message.info("请输入必填项");
          return false;
        }
      });
    },
    // 添加弹窗
    addTo() {
      if (!this.form.course_name) {
        this.$message.error("请先选择课程名称！");
        return;
      }
      this.Addto = true;
    },
    addList(arr) {
      this.form.class_time_data.push(...arr);
      // this.form.class_time_data = this.$lodash.unionBy(
      //   this.form.class_time_data,
      //   "week_day"
      // );
    },
    // 删除
    del(index, rows) {
      rows.splice(index, 1);
    },

    back() {
      this.$emit("close", false);
      this.form = {};
    },
    startChange() {
      this.form.pre_end_time = "";
      this.pickerOptions = {
        disabledDate: (date) => {
          return date.getTime() < new Date(this.form.pre_start_time).getTime();
        }
      };
    },
    minNumZero(props) {
      if (+this.form[props] < 0) {
        this.$message.error("输入数值不能为负");
        this.form[props] = "";
      }
    }
  }
};
</script>
<style lang="less" scoped>
.tg-dialogs--custom {
  ::v-deep & > .el-dialog__body {
    padding: 0 16px;
  }
}
.custom-table {
  position: relative;
  margin-left: 28px;
  margin-right: 28px;
  ::v-deep .el-table__header-wrapper {
    background-color: rgb(245, 248, 252);
    padding: 0 16px;
  }
  ::v-deep .el-table__body {
    padding: 0 16px;
  }
  .el-table {
    padding: 0;
  }
}
.tg-button--pointer {
  cursor: pointer;
}
.tg-dialogs__content--custom {
  height: 600px;
  overflow-y: scroll;
  overflow-x: hidden;
  .tg-form {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0 16px;
    padding: 0 12px;
  }
  .custom-table {
    position: relative;
    margin-left: 0;
    margin-right: 0;
    .el-table {
      padding: 0;
    }
  }
  .tg-button__wrap {
    padding-bottom: 30px;
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    img {
      height: 14px;
      width: 14px;
      margin-right: 8px;
    }
    span {
      color: @base-color;
    }
  }
  .tg-label--custom {
    padding-top: 16px;
    padding-bottom: 10px;
  }
  .min-student-input {
    ::v-deep input {
      text-align: left;
    }
  }
  ::v-deep .el-form-item__content {
    line-height: 1;
  }
  ::v-deep .tg-form .el-form-item {
    width: calc((100% - 48px) / 4);
  }
  ::v-deep .tg-form .el-form-item__content,
  .tg-form .tg-form-item--custom {
    width: 100%;
  }
  ::v-deep .el-rate {
    margin-top: 7px;
  }
  ::v-deep .el-form--inline div.el-form-item {
    margin-right: 0;
  }
  .tg-form--required {
    &::before {
      content: "*";
      color: #ff0317;
      margin-right: 4px;
    }
  }
  ::v-deep .tg-form .el-form-item .el-input,
  .tg-form .el-form-item .el-select {
    width: 100%;
  }
  .record__title {
    margin-top: 16px;
    height: 20px;
    line-height: 20px;
    display: inline-block;
    position: relative;
    padding-left: 12px;
    font-family: @text-famliy_medium;
    &::after {
      content: "";
      position: absolute;
      width: 6px;
      height: 6px;
      background-color: @base-color;
      border-radius: 50%;
      top: 7px;
      left: 0;
      z-index: 1;
    }
  }
  .more {
    width: 16px;
    height: 4px;
    margin-right: 5px;
    vertical-align: middle;
  }
  ::v-deep.market_staff .permission_select {
    width: 280px;
  }
  .is-valid__wrap {
    ::v-deep .el-form-item__label,
    ::v-deep .el-form-item__content {
      line-height: 32px;
    }
  }
  .tg-select {
    ::v-deep .el-select-dropdown.el-popper {
      width: 220px !important;
    }
  }
  ::v-deep .class-course {
    .el-input__inner {
      display: flex;
    }
    .el-input__suffix {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .el-input__suffix-inner {
      display: flex;
    }
  }
  .other {
    padding: 0 12px;
  }
  .other-info {
    ::v-deep .el-form-item__label {
      line-height: 32px;
    }
    ::v-deep .course-staff > .el-input,
    ::v-deep .market-staff .permission-select,
    ::v-deep .market-staff .market-staff--placeholder {
      width: 300px;
    }
  }
  ::v-deep .other.el-form--inline div.el-form-item {
    margin-right: 16px;
  }
  ::v-deep .tg-select .el-select-dropdown.el-popper {
    width: calc((100% - 48px) / 4);
  }
}
</style>
