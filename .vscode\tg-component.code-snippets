{"fetchGet": {"scope": "javascript,typescript", "prefix": ["tg_fg", "fetch_get"], "body": ["function ${1:fn_name}(data) {", "\treturn fetch.fetchGet(\"${2:api_url}\", {", "\t\tparams: data,", "\t});", "}"], "description": "调用公共fetch.js的get方法获取服务端数据"}, "fetchGet_Qs": {"scope": "javascript,typescript", "prefix": ["tg_fgqs", "fetch_get_qs"], "body": ["function ${1:fn_name}(data) {", "\tlet new_data = qs.stringify(data, { arrayFormat: \"repeat\" });", "\treturn fetch.fetchGet(\"${api_url}?\" + new_data);", "}"], "description": "调用公共fetch.js的get方法获取服务端数据，此方式将参数又转化为字符串处理"}, "fetchPost": {"scope": "javascript,typescript", "prefix": ["tg_fp", "fetch_post"], "body": ["function ${1:fn_name}(data) {", "\treturn fetch.fetchPost(", "\t\t\"${2:api_url}\",", "\t\tdata,", "\t\t\"提交成功\"", "\t);", "}"], "description": "调用公共fetch.js的post方法获取服务端数据"}, "coupon.vue": {"scope": "html", "prefix": ["tg_Coupon", "Coupon"], "body": ["<Coupon", ":chooseIds=\"${1:[]}\"", ":list=\"${2:[]}\"", "@close=\"${3:() => {}}\"", "@chooseOne=\"${4:(item) => {}}\"", "@confirm=\"${5:() => {}}\"", "@init=\"${6:() => {}}\"", "></Coupon>"], "description": ["优惠券弹出组件", "组件在系统中前台业务->收费管理-结算页面(src/views/charge/settlement.vue)有使用", "", "@prop chooseIds — 选中优惠券的ids", "@prop list — 必填参数，优惠券列表数据", "@event close — 弹窗关闭事件", "@event chooseOne — 点击某一张优惠券事件", "@event confirm — 点击弹窗确认按钮事件", "@event init — 初始化优惠券弹窗事件"]}, "FinancialLockDate.vue": {"scope": "html", "prefix": ["tg_FinancialLockDate", "FinancialLockDate"], "body": ["<FinancialLockDate", ":departmentId=\"${1}\"", ":date=\"${2}\"", ":placeholder=\"${3}\"", "@change=\"${4:() => {}}\"", "></FinancialLockDate>"], "description": ["财务锁账区间日期选择组件", "该日期选择组件是控制在多个时间区间，这些区间的日期不可选", "组件在系统中前台业务->收费管理(src/views/charge/index.vue)有使用", "", "@prop departmentId — 必填参数，校区ID", "@prop date — 日期，YYYY-MM-DD格式", "@prop placeholder — placeholder文字，默认显示请选择", "@event change — 日期变化时的事件"]}, "ToolTip.vue": {"scope": "html", "prefix": ["tg_ToolTip", "ToolTip"], "body": ["<ToolTip", ":effect=\"${1}\"", ":content=\"${2}\"", ":placement=\"${3}\"", "></ToolTip>"], "description": ["文字提示", "简单的文字提示组件", "组件在系统中财务管理->收据管理->收据详情(src/views/receiptManagement/components/detail.vue)有使用", "", "@prop effect — 同el-tooltip参数", "@prop content — 同el-tooltip参数", "@prop placement — 同el-tooltip参数"]}}