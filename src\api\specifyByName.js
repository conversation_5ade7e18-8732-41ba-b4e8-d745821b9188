// 点名上课
import axios from "../http";
import Vue from "vue";

// 提交点名
function specifyCommit(data) {
  return axios
    .post(`/api/school-service/check-in/roll-call`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}
// 锁定点名
function specifyLock(data) {
  return axios
    .post(`/api/school-service/check-in/roll-call-lock`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 锁定续期
function specifyRenewLock(data) {
  return axios
    .post(`/api/school-service/check-in/roll-call-sustain-lock`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 点名解锁
function specifyUnlock(data) {
  return axios
    .post(`/api/school-service/check-in/roll-call-unlock`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async specifyCommit(data) {
    return specifyCommit(data);
  },
  async specifyLock(data) {
    return specifyLock(data);
  },
  async specifyRenewLock(data) {
    return specifyRenewLock(data);
  },
  async specifyUnlock(data) {
    return specifyUnlock(data);
  }
};
