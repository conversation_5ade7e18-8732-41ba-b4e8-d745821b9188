import { fetchGet } from "../fetch";
import qs from "qs";

// 课程顾问加微率
function getAddwechat(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/customer/count/add/wechat?${new_data}`);
}
// 班主任加微率
function getTeacherAddwechat(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/school-service/classroom-student/count/add/wechat?${new_data}`
  );
}
// 课程顾问加微率导出
function exportAddwechat(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/market-service/customer/add/wechat/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 班主任加微率导出
function exportTeacherAddwechat(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/school-service/classroom-student/add/wechat/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
export default {
  getAddwechat,
  getTeacherAddwechat,
  exportAddwechat,
  exportTeacherAddwechat
};
