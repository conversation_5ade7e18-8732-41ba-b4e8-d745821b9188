import { fetchGet, fetchPost, fetchDel } from "../fetch";
import qs from "qs";
// 创建物品类别
function createCategroy(data) {
  return fetchPost("/api/course-service/article-category/create", data);
}
// 获取类别信息
function getCategroyInfo(data) {
  return fetchGet(
    `/api/course-service/article-category/info?article_category_id=${data}`
  );
}
// 获取类别列表
function getCategroyList(data) {
  return fetchGet("/api/course-service/article-category/list", data);
}
// 修改类别
function updateCategroy(data, msg) {
  return fetchPost(
    `/api/course-service/article-category/update?article_category_id=${data.id}`,
    data,
    msg
  );
}
// 删除类别
function delCategroy(data) {
  return fetchDel(`/api/course-service/article-category/delete`, {
    params: data
  });
}
// 批量删除物品
function batchDelGoods(data) {
  return fetchDel("/api/course-service/article/batch-delete", { data });
}
// 批量修改物品
function batchUpdateGoods(data, msg) {
  return fetchPost("/api/course-service/article/batch-update", data, msg);
}
// 添加物品
function createGoods(data) {
  return fetchPost("/api/course-service/article/create", data);
}
// 删除物品
function delGoods(data) {
  return fetchDel("/api/course-service/article/delete", { params: data });
}
// 物品导出
function exportGoods(data) {
  return fetchGet("/api/course-service/article/export", {
    params: { ...data, exportData: 1 }
  });
}
// 导入物品
function importGoods(data, msg) {
  return fetchPost("/api/course-service/article/import", data, msg);
}
// 物品详情
function getGoodsInfo(data) {
  return fetchGet("/api/course-service/article/info", { params: data });
}
// 物品列表
function getGoodsList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/course-service/article/list?${new_data}`);
}
// 仓库物品列表
function getWarehousAmountList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/course-service/article-bank-amount/article-amount-list?${new_data}`
  );
}
// 更新物品
function updateGoods(data, msg) {
  return fetchPost(
    `/api/course-service/article/update?article_id=${data.id}`,
    data,
    msg
  );
}

// 绑定物品
function createCourseGoods(data) {
  return fetchPost(`/api/course-service/course-article/create`, data);
}
// 删除绑定物品
function delCourseGoods(data) {
  return fetchDel(`/api/course-service/course-article/delete`, { data });
}
// 获取绑定物品列表
function getCourseGoodsList(data) {
  return fetchGet(`/api/course-service/course-article/list`, { params: data });
}
// 修改绑定物品
function updateCourseGoods(data, msg) {
  return fetchPost(`/api/course-service/course-article/update`, data, msg);
}
// 获取教辅包售卖列表，收费时用
function getTeachAidPackageSellList(data) {
  return fetchGet("/api/course-service/teach-aid-package/sell-list", {
    params: data
  });
}
export default {
  async createCategroy(data) {
    return createCategroy(data);
  },
  async getCategroyInfo(data) {
    return getCategroyInfo(data);
  },
  async getCategroyList(data) {
    return getCategroyList(data);
  },
  async updateCategroy(data, msg) {
    return updateCategroy(data, msg);
  },
  async delCategroy(data) {
    return delCategroy(data);
  },
  async batchDelGoods(data) {
    return batchDelGoods(data);
  },
  async batchUpdateGoods(data, msg) {
    return batchUpdateGoods(data, msg);
  },
  async createGoods(data) {
    return createGoods(data);
  },
  async delGoods(data) {
    return delGoods(data);
  },
  async exportGoods(data) {
    return exportGoods(data);
  },
  async importGoods(data, msg) {
    return importGoods(data, msg);
  },
  async getGoodsInfo(data) {
    return getGoodsInfo(data);
  },
  async getGoodsList(data) {
    return getGoodsList(data);
  },
  async updateGoods(data, msg) {
    return updateGoods(data, msg);
  },
  async createCourseGoods(data) {
    return createCourseGoods(data);
  },
  async delCourseGoods(data) {
    return delCourseGoods(data);
  },
  async getCourseGoodsList(data) {
    return getCourseGoodsList(data);
  },
  async updateCourseGoods(data, msg) {
    return updateCourseGoods(data, msg);
  },
  async getWarehousAmountList(data) {
    return getWarehousAmountList(data);
  },
  async getTeachAidPackageSellList(data) {
    return getTeachAidPackageSellList(data);
  }
};
