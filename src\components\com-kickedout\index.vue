<template>
  <div v-if="clasOver" class="bg">
    <div class="sz">
      <div class="text">
        {{ errText }}
      </div>
      <div class="class-but" @click="getBack">
        <img src="@/assets/living/queren.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "class-isover",
  props: {},
  components: {},
  data() {
    return {
      clasOver: false,
      errText: "结课已下课"
    };
  },
  // 计算属性
  computed: {},
  // 侦听器
  watch: {},
  methods: {
    onShow(text) {
      this.errText = text;
      this.clasOver = true;
    },
    getBack() {
      this.clasOver = false;
      this.$emit("destroyLocalStream");
      this.$router.push({
        path: "/courseManages"
      });
    }
  },
  created() {},
  mounted() {}
};
</script>

<style lang="scss" scoped>
.bg {
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  .sz {
    width: 400px;
    height: 300px;
    position: relative;
    top: 179px;
    left: 534px;
    border-radius: 15px;
    background: url("../../assets/living/wecom-temp-127771-4f3033ab8beae0fdddba3ea0e492a10e.48face5a.png")
      no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    justify-content: space-evenly;
    .text {
      position: absolute;
      top: 120px;
      font-size: 20px;
      font-weight: bold;
    }
    .class-but {
      position: absolute;
      top: 200px;
      img {
        width: 100px;
        height: 40px;
        cursor: pointer;
      }
      // position: absolute;
      // bottom: 100px;
    }
  }
}
</style>
