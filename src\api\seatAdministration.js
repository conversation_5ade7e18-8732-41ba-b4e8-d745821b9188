/**
 * 外呼接口
 */
import qs from "qs";
const fetch = require("../fetch");

// 通话记录列表
function getPhoneRecord(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/voip/findDialRecord?${new_data}`);
}
// 坐席列表
function getSeatList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/voip/extension-list?${new_data}`);
}
// 坐席信息
function getSeatInfo(data) {
  return fetch.fetchGet(`/api/market-service/voip/extension-info`, {
    params: data
  });
}
// 同步坐席列表
function syncSeatList(data) {
  return fetch.fetchGet(`/api/market-service/voip/extension-sync`, {
    params: data
  });
}

// 易宝商户修改
function seatUpdate(data) {
  return fetch.fetchPost(`/api/market-service/voip/extension-update`, data, "");
}
export default {
  getPhoneRecord,
  getSeatList,
  syncSeatList,
  seatUpdate,
  getSeatInfo
};
