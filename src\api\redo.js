// 补课
import axios from "../http";
import Vue from "vue";
import qs from "qs";

function getRedoList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/makeup/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function createNewClassroom(data) {
  return axios
    .post(`/api/school-service/makeup/new-classroom`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量删除
function batchDel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/school-service/makeup/delete?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 导出
function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/makeup/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function studentCancelMakeup(data) {
  return axios
    .post(`/api/school-service/scheduling/student/cancel-makeup`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 跟班补课
function addStudent(data) {
  return axios
    .post(`/api/school-service/scheduling/student/add-makeup`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async getRedoList(data) {
    return getRedoList(data);
  },
  async createNewClassroom(data) {
    return createNewClassroom(data);
  },
  async batchDel(data) {
    return batchDel(data);
  },
  async exportExcel(data) {
    return exportExcel(data);
  },
  async addStudent(data) {
    return addStudent(data);
  },
  async studentCancelMakeup(data) {
    return studentCancelMakeup(data);
  }
};
