import axios from "../http";
import qs from "qs";
const template = `/api/workstream-service/template`;
const rule = `/api/workstream-service/rule`;
const condition = `/api/workstream-service/condition`;
// 流程审批-模板管理-列表
function list(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios.get(`${template}/list?${new_data}`);
}
// 流程审批-模板管理-新增
function create(data) {
  return axios.post(`${template}/create`, data);
}
// 流程审批-模板管理-详情
function info(data) {
  return axios.get(`${template}/info?id=${data}`);
}
// 流程审批-模板管理-更新
function updateStatus(data) {
  return axios.post(`${template}/update-status`, data);
}

// 流程审批-模板管理-停用、启用
function update(data) {
  return axios.post(`${template}/update`, data);
}
// 流程审批-模板流程-新增
function rule_create(data) {
  return axios.post(`${rule}/create`, data);
}
// 流程审批-模板流程-详情
function rule_info(data) {
  return axios.get(`${rule}/info?id=${data}`);
}
// 流程审批-模板流程-更新
function rule_update(data) {
  return axios.post(`${rule}/update?id=${data.template_id}`, data);
}
function typeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios.get(`${template}/list?${new_data}`);
}
// 流程审批-模板流程-条件
function getConditions(data) {
  return axios.get(`${condition}/list?template_type=${data}`);
}

export default {
  list,
  create,
  info,
  update,
  rule_create,
  rule_info,
  rule_update,
  typeList,
  updateStatus,
  getConditions
};
