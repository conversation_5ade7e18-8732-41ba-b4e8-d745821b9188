<template>
  <div class="compOriginList">
    <el-dialog
      title="已签署的收据"
      :visible.sync="originDialog"
      width="80%"
      :before-close="beforeClose"
    >
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="multipleTable"
          :data="originContractList"
          border
          style="width: 100%"
          @select="handleSelectionChange"
          :show-checkbox="false"
          :row-key="getRowKeys"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <!-- 合同编号 -->
          <el-table-column
            prop="contract_no"
            label="合同编号"
            align="center"
            width="150"
          ></el-table-column>
          <!-- 收据日期 -->
          <el-table-column
            prop="charge_date"
            label="收据日期"
            align="center"
            width="150"
          ></el-table-column>
          <!-- 收费类型 -->
          <el-table-column prop="fee_type_chn" label="收费类型" align="center">
            <template slot-scope="{ row }">
              {{ row.fee_type_chn.join(",") }}
            </template>
          </el-table-column>
          <!-- 签署人姓名 -->
          <el-table-column
            prop="user_name"
            label="签署人姓名"
            align="center"
          ></el-table-column>
          <!-- 签署人手机号 -->
          <el-table-column
            prop="user_mobile"
            label="签署人手机号"
            align="center"
            width="150"
          ></el-table-column>
          <!-- 签署时间 -->
          <el-table-column prop="sign_time" label="签署时间" align="center">
            <template slot-scope="{ row }">
              <template v-if="row.sign_time !== '0001-01-01T08:05:43+08:05'">
                {{ moment(row.sign_time).format("yyyy-MM-DD HH:mm:ss") }}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('originClose')">取 消</el-button>
        <el-button type="primary" @click="originCancellation">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "compOriginList",
  props: {
    originContractList: {
      type: Array,
      default: () => []
    },
    originContract: {
      type: String,
      default: ""
    }
  },
  components: {},
  data() {
    return {
      originDialog: true
    };
  },
  computed: {},
  watch: {},
  methods: {
    handleSelectionChange(list, row) {
      console.log(list, row);
      if (list.length === 0) {
        this.multipleSelection = "";
        return;
      }
      this.$refs.multipleTable.clearSelection();
      this.originContractList.forEach((ele) => {
        if (ele.id === row.id) {
          this.$refs.multipleTable.toggleRowSelection(ele, true);
          this.multipleSelection = ele;
        }
      });
    },
    originCancellation() {
      if (!this.multipleSelection) {
        this.$message.error("请选择一条收据信息！");
        return;
      }
      this.$emit("originCancellation", this.multipleSelection.contract_no);
    },
    beforeClose() {
      this.$emit("originClose");
    },
    getRowKeys(row) {
      return row.contract_no;
    }
  },
  created() {},
  mounted() {
    this.$nextTick(() => {
      this.$refs.multipleTable.toggleRowSelection(
        this.originContractList.find(
          (ele) => ele.contract_no === this.originContract
        ),
        true
      );
    });
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__header-wrapper .el-checkbox {
  display: none;
}
.el-table {
  padding: 0 !important;
}
</style>
