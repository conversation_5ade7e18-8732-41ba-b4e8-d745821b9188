<!--试听记录-->
<template>
  <div>
    <el-dialog
      :visible="true"
      title="试听记录"
      class="audition-record"
      custom-class="audition-record"
      width="1016px"
      top="4vh"
      :before-close="cancel"
    >
      <div v-has="{ m: 'audition', o: 'record_export' }" class="expor_row">
        <FrontExportTableButton
          table="table"
          :refs="$refs"
          :fileName="`试听记录-${stu_name}`"
        ></FrontExportTableButton>
      </div>
      <div class="tg-table__box">
        <div class="tg-box--border"></div>
        <el-table
          ref="table"
          :data="list"
          tooltip-effect="dark"
          class="record-table tg-table"
          height="559"
        >
          <el-table-column label="" width="50px" type="selection">
          </el-table-column>
          <el-table-column
            label="课程名称"
            width="100"
            prop="course_name"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column label="班级名称" width="100" prop="classroom_name">
          </el-table-column>
          <el-table-column label="上课时间" width="150" prop="start_time">
            <template slot-scope="scope">
              <div class="time__wrap">
                {{ getDate(scope.row.start_time) }}（{{
                  scope.row.start_time | getWeekDay | formatWeek
                }}） {{ getTime(scope.row.start_time) }}-{{
                  getTime(scope.row.end_time)
                }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="class_duration" label="时长" width="90">
            <template slot-scope="scope">
              {{ scope.row.class_duration | getClassDuration }}时
            </template>
          </el-table-column>
          <el-table-column label="老师" width="90" prop="teacher_name">
          </el-table-column>
          <el-table-column label="教室" width="90" prop="school_room_name">
          </el-table-column>
          <el-table-column label="状态" width="90" prop="status_chn">
          </el-table-column>
          <el-table-column label="学员状态" width="90" prop="status_type">
            <template slot-scope="scope">
              {{
                studentTypes.find((i) => i.id === scope.row.student_type)?.name
              }}
            </template>
          </el-table-column>
          <el-table-column label="出勤" width="80" prop="is_attendance">
            <template slot-scope="scope">
              {{ scope.row.is_attendance === "YES" ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作人"
            width="90"
            prop="handler_employee_name"
          >
          </el-table-column>
          <el-table-column
            label="操作"
            width="110"
            fixed="right"
            v-has="{ m: 'audition', o: 'remove_adution' }"
          >
            <template slot-scope="scope">
              <el-button
                :disabled="
                  ['is_started', 'is_cancelled'].includes(scope.row.status) ||
                  scope.row.status_chn === '已上课'
                "
                type="text"
                class="tg-text--blue"
                @click="openCancelAudition(scope.row.id)"
                >撤销试听</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="tg-button--plain" type="plain" @click="cancel"
          >关闭</el-button
        >
      </div>
    </el-dialog>
    <cancel-audition
      :id="id"
      :student_id="[rowIds]"
      v-if="audition_visible"
      @close="close"
    ></cancel-audition>
  </div>
</template>
<script>
import studentInforApi from "@/api/studentInfor";
import timeFormat from "@/public/timeFormat";
import CancelAudition from "@/components/audition/cancelAudition.vue";
export default {
  props: {
    rowIds: {
      type: String,
      default: "0"
    },
    stu_name: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      page: 1,
      total: 0,
      page_size: 10,
      audition_visible: false,
      id: "",
      list: [],
      studentTypes: []
    };
  },
  mounted() {
    console.log(this.rowIds);
  },
  methods: {
    getStudentStyleList(data) {
      studentInforApi.getStudentStyleList(data).then((res) => {
        const arr = res.data.map((i) => i.class_data);
        this.studentTypes = arr.flat();
        console.log(this.studentTypes);
      });
    },
    openCancelAudition(id) {
      if (id) {
        this.id = id;
        this.audition_visible = true;
      } else {
        this.$store.dispatch("getSchoolServiceAuditionStudent", {
          student_id: this.rowIds
        });
      }
    },
    close(id) {
      this.audition_visible = false;
      if (id) {
        this.$store.dispatch("getSchoolServiceAuditionStudent", {
          student_id: id
        });
      }
    },
    cancel() {
      this.$emit("close");
    },
    getTime(time) {
      return timeFormat.GetCustTime(time, "HH:mm");
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    }
  },
  computed: {
    // 班级列表
    student_list() {
      return this.$store.getters.doneGetSchoolServiceAuditionStudent;
    }
  },
  filters: {
    getClassDuration(val) {
      const h = val / 3600;
      return h.toFixed(2);
    }
  },
  watch: {
    student_list(val) {
      if (val) {
        this.list = val;
      }
      console.log(this.$refs.table.data);
    }
  },
  created() {
    this.getStudentStyleList();
    this.$store.dispatch("getSchoolServiceAuditionStudent", {
      student_id: this.rowIds
    });
  },
  components: {
    CancelAudition
  }
};
</script>
<style lang="less" scoped>
.audition-record {
  .tg-table__box {
    margin-left: 0 !important;
  }
  ::v-deep & > .el-dialog__body {
    padding: 0 16px;
    height: 573px;
  }
  ::v-deep .record-table {
    &:before,
    &:after {
      background: #f5f8fc;
      content: "";
      height: 48px;
      width: 16px;
      position: absolute;
      left: 0;
      top: 0;
    }
    &:before {
      right: 0;
      bottom: unset;
      left: unset;
    }
    th {
      background: #f5f8fc;
      &:nth-child(3) {
        text-align: center;
      }
    }
    .el-table__body tr.current-row > td {
      background-color: #ebf4ff;
    }
    .tg-text--blue {
      font-weight: normal;
    }
  }
  .tg-pagination {
    border-top: 1px solid #e0e6ed;
  }
  .time__wrap {
    text-align: center;
  }

  .expor_row {
    display: flex;
    align-items: center;
    margin-top: 16px;
  }
}
</style>
