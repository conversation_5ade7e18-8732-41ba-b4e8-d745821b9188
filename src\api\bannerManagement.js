import { fetchGet, fetchPut, fetchPost, fetchDel, fetchPatch } from "../fetch";
import qs from "qs";
// 获取banner列表
function getBannerList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/questionnaire-service/admin/banner/list?${new_data}`);
}
// 创建banner
function createBanner(data) {
  return fetchPost("/api/questionnaire-service/admin/banner/create", data);
}
// 更新banner状态
function updateBannerStatus(data) {
  return fetchPatch(
    "/api/questionnaire-service/admin/banner/update-status",
    data
  );
}
// 修改banner信息
function updateBannerInfo(data) {
  return fetchPut("/api/questionnaire-service/admin/banner/update", data);
}
// 删除banner
function deleteBanner(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchDel(`/api/questionnaire-service/admin/banner/delete?${new_data}`);
}
// 获取banner详情
function getBannerDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/questionnaire-service/admin/banner/info?${new_data}`);
}

export default {
  async getBannerList(data) {
    return getBannerList(data);
  },
  async createBanner(data) {
    return createBanner(data);
  },
  async updateBannerStatus(data) {
    return updateBannerStatus(data);
  },
  async deleteBanner(data) {
    return deleteBanner(data);
  },
  async getBannerDetail(data) {
    return getBannerDetail(data);
  },
  async updateBannerInfo(data) {
    return updateBannerInfo(data);
  }
};
