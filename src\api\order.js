import qs from "qs";
const fetch = require("../fetch");

// 订单状态下拉列表
export function getOrderStatus(data) {
  return fetch.fetchGet(`/api/order-service/admin/order/dic/status`, {
    params: data
  });
}
// 订单列表
export function getOrderList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(`/api/order-service/admin/order/list?${new_data}`);
}
// 订单删除
export function deleteOrder(data) {
  return fetch.fetchPost(
    `/api/enterprise/order/delete?orderId=${data.orderId}`,
    {},
    ""
  );
}
// 订单取消
export function cancelOrder(data) {
  return fetch.fetchPost(`/api/order-service/admin/order/cancel`, data, "");
}
// 订单导入
export function orderInto(data) {
  return fetch.fetchPost(`/api/order-service/admin/order/import`, data, "");
}

// 订单详情
export function getOrderInfo(data) {
  return fetch.fetchGet(`/api/order-service/admin/order/info`, {
    params: data
  });
}
// 订单详情
export function getOrderInfoPublic(data) {
  return fetch.fetchGet(`/api/order-service/public/order/info`, {
    params: data
  });
}

export function getPymentInfo(data) {
  return fetch.fetchGet(`/api/order-service/admin/receipt/payment/detail`, {
    params: data
  });
}
// 导出
export function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/order-service/admin/order/export?${new_data}`, {
    params: { exportData: 1 }
  });
}
// 收费收据详情
export function getReceiptInfo(data) {
  return fetch.fetchGet(
    `/api/order-service/admin/receipt/payment/detail-by-order-id`,
    {
      params: data
    }
  );
}
export default {
  getOrderList,
  getOrderStatus,
  deleteOrder,
  cancelOrder,
  getOrderInfo,
  getPymentInfo,
  getOrderInfoPublic,
  exportExcel,
  orderInto,
  getReceiptInfo
};
