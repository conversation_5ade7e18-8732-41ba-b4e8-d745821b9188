import { fetchGet, fetchPost, fetchDel } from "../fetch";
import axios from "../http";
import Vue from "vue";
function createStudentCrtegory(data) {
  return fetchPost(`/api/student-service/student-category/create`, data, "");
}

function getStudentCrtegoryList(data) {
  return fetchGet(`/api/student-service/student-category/list`, {
    params: data
  });
}

function delStudentCrtegory(data) {
  return fetchDel(`/api/student-service/student-category/delete?id=${data.id}`);
}

function saveStudentCrtegory(data) {
  return axios
    .post(`/api/student-service/student-category/save`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function batchUpdateStudentCrtegory(data) {
  return axios
    .post(`/api/student-service/student/student-category-updates`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async createStudentCrtegory(data) {
    return createStudentCrtegory(data);
  },
  async getStudentCrtegoryList(data) {
    return getStudentCrtegoryList(data);
  },
  async delStudentCrtegory(data) {
    return delStudentCrtegory(data);
  },
  async saveStudentCrtegory(data) {
    return saveStudentCrtegory(data);
  },
  async batchUpdateStudentCrtegory(data) {
    return batchUpdateStudentCrtegory(data);
  }
};
