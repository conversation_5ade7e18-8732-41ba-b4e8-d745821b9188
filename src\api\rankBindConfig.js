import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 员工组列表
export const employeeGroupList = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/organization-service/employee-group/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
// 员工组添加
export function employeeGroupCreate(data) {
  return axios
    .post(`/api/organization-service/employee-group/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 员工组添加
export function employeeGroupRemove(data) {
  return axios
    .post(`/api/organization-service/employee-group/delete`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 员工组详情
export const employeeGroupInfo = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/organization-service/employee-group/info?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 员工组编辑
export function employeeGroupUpdate(data) {
  return axios
    .post(`/api/organization-service/employee-group/update`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 员工组添加
export function employeeGroupGroupMemberSave(data) {
  return axios
    .post(`/api/organization-service/employee-group/group-member-save`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export const employeeGroupGroupMemberList = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/organization-service/employee-group/group-member-list?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
