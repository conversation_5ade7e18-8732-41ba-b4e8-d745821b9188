/**
 * 发票管理接口
 */

const fetch = require("../fetch");
const api_path = "/api/order-service/yop";

// pos列表
function getPosList(data) {
  return fetch.fetchGet(`/api/order-service/admin/yop/pos-list`, {
    params: data
  });
}
function getPosSync(data) {
  return fetch.fetchGet(`/api/order-service/admin/yop/pos-info-sync`, {
    params: data
  });
}
// pos绑定
function posInstall(data) {
  return fetch.fetchPost(`/api/order-service/admin/yop/pos-install`, data, "");
}
// pos解绑
function posUninstall(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/yop/pos-uninstall`,
    data,
    ""
  );
}
// pos支付
function posPay(data) {
  return fetch.fetchPost(`${api_path}/pos-pay`, data, "");
}
// 易宝聚合支付
function aggPay(data) {
  return fetch.fetchPost(`${api_path}/agg-pay`, data, "");
}
// 获取微信config参数
function getWxconfig(data) {
  return fetch.fetchPost(`/api/wechat-service/public/h5-sign`, data, "");
}
// 获取微信用户信息
function getWxUserInfo(data) {
  return fetch.fetchPost(`/api/wechat-service/public/xxxx`, data, "");
}
// 获取微信config参数
function getPayResult(data) {
  return fetch.fetchGet(`${api_path}/pay-result`, {
    params: data
  });
}
// 易宝商户信息列表
function getMerchantList(data) {
  return fetch.fetchGet(`/api/order-service/admin/yop/merchant-list`, {
    params: data
  });
}
// 易宝商户添加
function getMerchantAdd(data) {
  return fetch.fetchPost(`/api/order-service/admin/yop/merchant-add`, data, "");
}
// 易宝商户添加
function getMerchantDelete(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/yop/merchant-delete`,
    data,
    ""
  );
}
// 易宝商户修改
function getMerchantUpdate(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/yop/merchant-update`,
    data,
    ""
  );
}
export default {
  getPosList,
  posInstall,
  posUninstall,
  posPay,
  aggPay,
  getPayResult,
  getWxconfig,
  getPosSync,
  getWxUserInfo,
  getMerchantList,
  getMerchantAdd,
  getMerchantDelete,
  getMerchantUpdate
};
