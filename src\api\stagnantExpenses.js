import qs from "qs";
const fetch = require("../fetch");

// 呆滞费用列表
export function getList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(`/api/finance-service/idle-expense/list?${new_data}`);
}
// 呆滞费用清理记录列表
export function getCleanList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetch.fetchGet(
    `/api/finance-service/idle-expense/clear-list?${new_data}`
  );
}
// 呆滞费用列表导出
export function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/finance-service/idle-expense/export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 呆滞费用清理记录导出
export function exportClearExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/finance-service/idle-expense/clear-export?${new_data}`,
    { params: { exportData: 1 } }
  );
}
// 呆滞费用清理
function clean(data) {
  return fetch.fetchPost(`/api/finance-service/idle-expense/clear`, data, "");
}

export default {
  getList,
  exportExcel,
  clean,
  exportClearExcel,
  getCleanList
};
