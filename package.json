{"name": "content-admin", "version": "1.7.848", "private": true, "scripts": {"dev:proxy": "vue-cli-service serve --mode proxy", "dev": "cross-env NODE_OPTIONS='--max-old-space-size=5120' vue-cli-service serve --mode dev", "build:dev": "vue-cli-service build --mode development", "build:staging": "vue-cli-service build --mode staging", "build:prod": "vue-cli-service build --mode production", "lint": "vue-cli-service lint", "preview": "node build/index.js --preview", "report": "node build/index.js --preview --report", "pre-commit": "lint-staged", "lint2": "eslint --ext .js,.vue src --fix", "format": "prettier --write src"}, "lint-staged": {"src/**": ["eslint --fix"]}, "dependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@tencentcloud/chat": "^3.3.5", "@tencentcloud/tiw": "^2.9.2-alpha.9", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^0.21.1", "bignumber.js": "^9.0.1", "clipboard": "^2.0.11", "connect": "3.6.6", "core-js": "^3.32.0", "crypto": "^1.0.1", "echarts": "^5.5.1", "echarts-liquidfill": "^3.1.0", "element-ui": "^2.15.6", "eslint-plugin-promise": "^6.1.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "install": "^0.13.0", "js-base64": "^3.7.5", "js-md5": "^0.7.3", "js-web-screen-shot": "^1.9.9-rc.18", "jszip": "^3.10.1", "lodash": "^4.17.21", "moment": "^2.29.4", "node-forge": "^1.3.1", "npm": "^10.6.0", "nprogress": "^0.2.0", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.10.1", "runjs": "4.3.2", "sass-loader": "^10.5.2", "swiper": "^11.1.1", "tim-profanity-filter-plugin": "^1.1.0", "tim-upload-plugin": "^1.3.0", "trtc-js-sdk": "^4.15.20", "trtc-sdk-v5": "5.8.3", "uuid": "^8.3.2", "v-scale-screen": "^2.2.0", "vue": "^2.6.11", "vue-awesome-swiper": "^3.1.1", "vue-drag-it-dude": "^1.3.0", "vue-drag-resize": "^1.5.4", "vue-infinite-loading": "^2.4.5", "vue-photo-preview": "^1.1.3", "vue-qr": "^4.0.9", "vue-router": "^3.5.0", "vue-seamless-scroll": "^1.1.23", "vue-uuid": "^2.0.2", "vuedraggable": "^2.24.3", "vuetify": "^2.6.9", "vuex": "^3.4.0", "webpack": "^4.1.0", "weixin-js-sdk": "^1.6.0", "xss": "^1.0.14"}, "devDependencies": {"@babel/eslint-parser": "^7.22.10", "@mdi/font": "^7.0.96", "@types/eslint": "^9.6.1", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "babel-plugin-transform-remove-console": "^6.9.4", "eslint": "^8.47.0", "eslint-config-prettier": "^7.0.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-n": "^16.0.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-vue": "^9.17.0", "hard-source-webpack-plugin": "^0.13.1", "husky": "^8.0.3", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^13.3.0", "material-design-icons-iconfont": "^6.7.0", "mini-css-extract-plugin": "^2.9.0", "prettier": "^2.6.2", "sass": "^1.65.1", "script-loader": "^0.7.2", "style-resources-loader": "^1.4.1", "svg-sprite-loader": "^6.0.11", "thread-loader": "^4.0.2", "tim-js-sdk": "^2.27.5", "uglifyjs-webpack-plugin": "^2.2.0", "vue-cli-plugin-element-ui": "~1.1.4", "vue-cli-plugin-fontawesome": "~0.2.0", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-cli-plugin-vuetify": "~2.4.0", "vue-cookies": "^1.7.4", "vue-eslint-parser": "^7.6.0", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.7.0", "webpack": "^4.1.0"}}