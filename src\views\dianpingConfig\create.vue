<template>
  <div class="dialog">
    <el-dialog
      :visible="handlerDialog"
      width="800px"
      :title="type == 'add' ? '新增商户' : '修改商户信息'"
      :before-close="close"
    >
      <el-form
        ref="form"
        :model="form"
        class="tg-box--margin"
        label-width="120px"
      >
        <el-form-item
          label="商户名称"
          prop="name"
          class="tg-box--margin"
          required
        >
          <el-input
            type="input"
            placeholder="请输入商户名称"
            v-model="form.name"
            style="width: 300px"
          />
        </el-form-item>

        <el-form-item
          label="选择校区"
          prop="department_name"
          class="tg-box--margin"
          required
        >
          <el-button
            type="primary"
            size="small"
            @click="schools_authorize_show = true"
            :disabled="type == 'edit'"
          >
            {{
              form.department_name
                ? `已选 ${form.department_name}`
                : "请选择校区"
            }}
          </el-button>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close" class="tg-button--plain">取消</el-button>
        <el-button
          type="primary"
          @click="confirm"
          :loading="loading"
          class="tg-button--primary"
        >
          确定
        </el-button>
      </span>
    </el-dialog>
    <school-tree
      :flag.sync="schools_authorize_show"
      :id.sync="form.department_id"
      :name.sync="form.department_name"
      :required="true"
      :has_modal="true"
      type="radio"
      @confirm="confirmSchool"
      :use_store_options="false"
      :is_center="true"
    >
    </school-tree>
  </div>
</template>

<script>
import schoolTree from "@/components/schoolTree/schoolTree"; // 校区弹框
import dianpingConfigApi from "@/api/dianpingConfig";

export default {
  name: "createDialog",
  components: {
    schoolTree
  },
  data() {
    return {
      type: "add",
      handlerDialog: false,
      schools_authorize_show: false,
      loading: false,
      form: {
        id: "",
        name: "",
        department_id: "",
        department_name: ""
      },
      rules: {
        name: [{ required: true, message: "", trigger: "blur" }],
        department_name: [{ required: true, message: "", trigger: "blur" }]
      }
    };
  },
  methods: {
    openDialog(type, row) {
      this.type = type;
      this.handlerDialog = true;
      if (type === "edit") {
        this.form = {
          name: row.name,
          department_id: row.department_id,
          department_name: row.department_name,
          id: row.id
        };
      }
    },

    close() {
      for (const k in this.form) {
        this.form[k] = "";
        if (k === "type") {
          this.form[k] = "2";
        }
      }
      this.handlerDialog = false;
    },
    confirmSchool() {
      if (this.form.department_id) {
        this.schools_authorize_show = false;
      } else {
        this.$message.info("请勾选校区");
      }
    },
    confirm() {
      this.loading = true;
      if (!this.form.name) {
        this.$message.info("请输入商户名称!");
        this.loading = false;
        return;
      }
      if (!this.form.department_id) {
        this.$message.info("请选择校区!");
        this.loading = false;
        return;
      }
      const { department_id, name } = this.form;
      dianpingConfigApi
        .add({
          id: this.form.id,
          department_id,
          name
        })
        .then((res) => {
          if (+res.status === 200 && +res.data.code === 0) {
            if (this.type === "add") {
              this.$message.success("新增成功！");
            } else {
              this.$message.success("修改成功！");
            }
            this.close();
            this.loading = false;
            this.$emit("refresh");
          } else {
            this.loading = false;
            this.$message.error(res.data.message);
          }
        });
    }
  }
};
</script>
<style scoped lang="less">
.vessel {
  ::v-deep .el-dialog__body {
    padding: 16px;
  }
}
/deep/ .tg-select {
  .el-input {
    width: 300px;
  }
}
</style>
