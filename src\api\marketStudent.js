import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 创建线索，新增线索详情
function getCreateStudents_xs(data) {
  return axios
    .post(`/api/market-service/clue/create`, data)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("创建成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 编辑单个员工信息，获取线索详情
function getFindStudents_xs(data) {
  return axios
    .get(`/api/market-service/clue/info?customer_id=${data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 所有线索，获取线索列表
function getStudentsList_xs(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/clue/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 编辑后保存接口，更新线索详情
function UpdateEmployee_xs(data) {
  return axios
    .patch(`/api/market-service/clue/update?customer_id=${data.id}`, data)
    .then((response) => {
      Vue.prototype.$message.success("编辑成功");

      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 删除线索，删除线索
function deleteStudents_xs(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/market-service/clue/many-delete?${new_data}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function deleteStudentsOne(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/market-service/clue/delete?${new_data}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 责任人
function getObligator_xs(data) {
  return axios
    .get(`/api/v1/list-helper/get-obligator-list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 同步，未体现
function getTongbu(data) {
  return axios
    .post(`/api/market-service/clue/intention`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量上传，线索导入
function batchImport(data) {
  return axios
    .post(`/api/market-service/clue/import`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return error;
    })
    .finally();
}
// 导出，线索导出
function exportExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return axios
    .get(`/api/market-service/clue/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 意向级别
function getLevelList() {
  return axios
    .get(`/api/market-service/intention-level/list`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 取消转换
function cancelCustomerStatus(data) {
  return axios
    .patch(`/api/market-service/customer/cancel-customer-status`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取客户状态
function getStatusList() {
  return axios
    .get(`/api/market-service/customer/status-list`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取跟进类型
function getFollowTypeList() {
  return axios
    .get(`/api/market-service/customer/follow-type-list`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量修改
function batchEdit(data) {
  return axios
    .post(`/api/market-service/clue/many-update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 创建线索
  async GetCreateStudents_xs(data) {
    return getCreateStudents_xs(data);
  },
  // 编辑单个员工信息
  async GetFindStudents_xs(data) {
    return getFindStudents_xs(data);
  },
  // 所有线索
  async GetStudentsList_xs(data) {
    return getStudentsList_xs(data);
  },
  // 编辑后保存接口
  async UpdateEmployee_xs(data) {
    return UpdateEmployee_xs(data);
  },
  // 删除线索
  async DeleteStudents_xs(data) {
    return deleteStudents_xs(data);
  },
  async deleteStudentsOne(data) {
    return deleteStudentsOne(data);
  },
  // 责任人
  async GetObligator_xs(data) {
    return getObligator_xs(data);
  },
  // 同步
  async GetTongbu(data) {
    return getTongbu(data);
  },
  // 批量导入
  async BatchImport(data) {
    return batchImport(data);
  },
  // 导出
  async ExportExcel(data) {
    return exportExcel(data);
  },
  // 意向级别
  async GetLevelList(data) {
    return getLevelList(data);
  },
  // 取消转化
  async cancelCustomerStatus(data) {
    return cancelCustomerStatus(data);
  },
  // 获取客户状态
  async getStatusList(data) {
    return getStatusList(data);
  },
  async getFollowTypeList(data) {
    return getFollowTypeList(data);
  },
  async batchEdit(data) {
    return batchEdit(data);
  }
};
