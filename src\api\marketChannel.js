import request from "@/plugins/axios";

// 获取渠道列表
export function getChannelList() {
  return request({
    url: "/api/market/channel/list",
    method: "get"
  });
}

// 获取渠道市场分析数据
export function getMarketChannelData(params) {
  return request({
    url: "/api/market/channel/data",
    method: "get",
    params
  });
}

// 导出渠道市场分析数据
export function exportMarketChannelData(params) {
  return request({
    url: "/api/market/channel/export",
    method: "get",
    params,
    responseType: "blob"
  });
}

export default {
  getChannelList,
  getMarketChannelData,
  exportMarketChannelData
};
