<!--编辑/添加-->
<template>
  <div class="channel-edit">
    <el-dialog
      :title="operate != 7 ? title : ''"
      :visible="true"
      v-if="dialogVisible"
      width="800px"
      :before-close="handleClose"
    >
      <div slot="title" class="dialog-title" v-if="operate === 7">
        <span>{{ title.split("-")[0] }}-</span>
        <span class="special">{{ title.split("-")[1] }}</span>
      </div>
      <el-form ref="form" :model="form">
        <template v-for="(item, index) in editTitle">
          <el-form-item
            v-if="item.show"
            :label="item.label"
            :label-width="operate == 3 || operate == 5 ? '110px' : '85px'"
            :key="index"
            :rules="item.rules"
            :prop="item.props"
            class="tg-box--margin"
          >
            <el-col :span="8" v-if="item.type === 'input'">
              <el-input
                v-model="form[item.props]"
                :placeholder="`请输入${item.label}`"
                :disabled="item.disabled ? true : false"
              ></el-input>
            </el-col>
            <el-col :span="8" v-if="item.type === 'countNumber'">
              <el-input-number
                v-model="form[item.props]"
                :placeholder="`请输入${item.label}`"
                :disabled="item.disabled ? true : false"
                :controls="false"
                :min="0"
              >
              </el-input-number>
            </el-col>
            <el-col :span="8" v-if="item.type === 'number'">
              <el-input
                type="number"
                v-model="form[item.props]"
                :placeholder="`请输入${item.label}`"
                :disabled="item.disabled ? true : false"
                :controls="false"
                :min="0"
              >
              </el-input>
            </el-col>
            <el-select
              :popper-append-to-body="false"
              v-model="form[item.props]"
              :placeholder="`请选择${item.label}`"
              :disabled="item.disabled ? true : false"
              v-else-if="item.type === 'select'"
            >
              <el-option
                :label="item1.label"
                :value="item1.value"
                v-for="(item1, index1) in item.selectOptions"
                :key="index1"
              >
              </el-option>
            </el-select>
            <el-date-picker
              v-else-if="item.type === 'date'"
              v-model="form[item.props]"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="item.disabled ? true : false"
              popper-class="tg-date-picker tg-date--range"
            >
            </el-date-picker>
            <el-date-picker
              v-else-if="item.type === 'datetime'"
              v-model="form[item.props]"
              type="datetimerange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :disabled="item.disabled ? true : false"
              popper-class="tg-date-picker tg-date--range"
            >
            </el-date-picker>
            <el-checkbox-group
              v-model="form[item.props]"
              v-else-if="item.type === 'mutipleSelect' && form[item.props]"
            >
              <el-checkbox
                :label="item1.value"
                v-for="(item1, index1) in item.selectOptions"
                :key="`${index}-${index1}`"
                :disabled="item.disabled ? true : false"
              >
                {{ item1.label }}</el-checkbox
              >
            </el-checkbox-group>
            <el-switch
              v-else-if="item.type === 'switch'"
              v-model="form[item.props]"
            >
            </el-switch>
          </el-form-item>
        </template>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose" class="tg-button--plain"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="handleClick"
          class="tg-button--primary"
          >确定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { channel_source, tw_channel_list } from "@/public/dict";
export default {
  data() {
    return {
      form: {
        channelsource: ""
      },
      rules: {},
      parentEditTitle: [
        {
          show: true,
          props: "channelsource",
          label: "市场渠道",
          type: "select",
          disabled: false,
          selectOptions: channel_source,
          rules: { required: true, message: "请输入市场渠道", trigger: "blur" }
        },
        {
          show: false,
          props: "channel_type",
          label: "渠道类型",
          type: "select",
          selectOptions: tw_channel_list,
          rules: { required: true, message: "请选择渠道类型", trigger: "blur" }
        },
        {
          show: true,
          props: "name",
          label: "渠道名称",
          type: "input",
          rules: { required: true, message: "请输入渠道名称", trigger: "blur" }
        },
        {
          show: true,
          props: "status",
          label: "是否可用",
          type: "switch",
          radioOptions: [
            { label: "启用", value: true },
            { label: "禁用", value: false }
          ]
        }
      ],
      sonEditTitle: [
        {
          show: true,
          props: "name",
          label: "二级渠道名称",
          type: "input",
          rules: {
            required: true,
            message: "请输入二级渠道名称",
            trigger: "blur"
          }
        },
        {
          show: true,
          props: "status",
          label: "是否可用",
          type: "switch",
          radioOptions: [
            { label: "启用", value: true },
            { label: "禁用", value: false }
          ]
        }
      ], // 二级渠道编辑标题
      sonAddTitle: [
        {
          show: true,
          props: "name",
          label: "一级渠道",
          type: "input",
          disabled: true
        },
        {
          show: true,
          props: "subName",
          label: "二级渠道名称",
          type: "input",
          rules: {
            required: true,
            message: "请输入二级渠道名称",
            trigger: "blur"
          }
        },
        {
          show: true,
          props: "status",
          label: "是否可用",
          type: "switch",
          radioOptions: [
            { label: "启用", value: true },
            { label: "禁用", value: false }
          ]
        }
      ],
      deliveryEditTitle: [
        {
          show: true,
          props: "putInTime",
          label: "投放时间",
          type: "date",
          rules: { required: true, message: "请输入投放时间", trigger: "blur" }
        },
        {
          show: true,
          props: "fee",
          label: "费用/元",
          type: "countNumber",
          rules: {
            required: true,
            message: "请输入费用（元）",
            trigger: "blur"
          }
        },
        {
          show: true,
          props: "name",
          label: "备注",
          type: "input",
          rules: { required: true, message: "请输入备注", trigger: "blur" }
        }
      ], // 投放费用表头
      title: "",
      editTitle: [],
      deliveryName: ""
    };
  },
  computed: {
    channel_info() {
      return this.$store.getters.doneGetChannelInfo;
    },
    subchannel_info() {
      return this.$store.getters.doneGetSubChannelInfo;
    },
    success() {
      return this.$store.getters.doneGetStatus;
    },
    name() {
      return this.$store.getters.doneGetName;
    },
    parentId() {
      return this.$store.getters.doneGetParentId;
    },
    operate() {
      return this.$store.getters.doneGetOperate;
    },
    dialogVisible() {
      return this.$store.getters.doneGetDialogFlag;
    },
    deliveryId() {
      return this.$store.getters.doneGetDeliveryId;
    },
    oldChannel() {
      return this.$store.getters.doneGetOldChannel;
    }
  },
  watch: {
    channel_info(data) {
      this.form = data.reChannell;
      if (
        data.reChannell.channelsource === undefined ||
        data.reChannell.channelsource === null
      ) {
        this.$set(this.form, "channelsource", "");
      } else {
        this.form.channelsource = Number(
          data.reChannell.channelsource.toString()
        );
      }

      this.deliveryName = data.reChannell.name;
    },
    subchannel_info(data) {
      this.form = data.resubchannell;
      this.deliveryName = data.resubchannell.name;
    },
    operate(val) {
      switch (val) {
        case 1:
          this.title = "新增";

          this.editTitle = this.parentEditTitle;
          this.initData();
          this.parentEditTitle[0].disabled = false;

          break;
        case 2:
          this.title = "编辑";
          this.editTitle = this.parentEditTitle;
          this.parentEditTitle[0].disabled = true;

          break;
        case 3:
          this.title = "添加二级渠道";
          this.editTitle = this.sonAddTitle;
          this.initData();
          this.$set(this.form, "name", this.name);
          this.$set(this.form, "parentId", this.parentId);
          break;
        case 5:
          this.title = "编辑";
          this.editTitle = this.sonEditTitle;
          break;
        case 7:
          this.title = `新增费用-${this.deliveryName}`;
          this.editTitle = this.deliveryEditTitle;
          this.initData();
          this.$set(this.form, "channelid", this.deliveryId.id);
          this.$set(this.form, "subchannelid", this.deliveryId.subId);
          this.$set(this.form, "fee", undefined);
      }
    },
    success(new_bool) {
      if (new_bool != null) {
        this.$store.commit("setDialogFlag", false);
      }
    },
    name(val) {
      this.$set(this.form, "name", val);
    },
    parentId(val) {
      this.$set(this.form, "parentId", val);
    },
    "form.channelsource": {
      handler(val) {
        if (val === 1) {
          this.editTitle[1].show = true;
        } else if (val === 2) {
          this.editTitle[1].show = false;
        }
      },
      immediate: true
    }
  },
  methods: {
    // 编辑需要数据回显，title和数据的变量名要保持一致
    initData() {
      this.form = {};
      this.editTitle.forEach((item) => {
        switch (item.type) {
          case "mutipleSelect":
            this.$set(this.form, item.props, []);
            break;
          case "switch":
            this.$set(this.form, item.props, true);
            break;
          default:
            this.$set(this.form, item.props, "");
            break;
        }
      });
    },

    handleClose() {
      this.$store.commit("setDialogFlag", false);
      this.$store.commit("setOperate", -1);
    },
    handleClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitData(this.form);
          this.handleClose();
        } else {
          return false;
        }
      });
    },
    submitData(form) {
      switch (this.operate) {
        case 1:
          this.form.channelsource = [this.form.channelsource];
          this.$store.commit("setStatus", "");
          this.$store.commit("setSubStatus", "");
          this.$store.dispatch("createChannel", form);
          break;
        case 2:
          this.form.channelsource = [this.form.channelsource];
          this.$store.commit("setTreeId", form.id);
          this.$store.commit("setStatus", "");
          this.$store.commit("setSubStatus", "");
          this.$store.dispatch("updateChannel", form);
          break;
        case 3:
          // eslint-disable-next-line no-case-declarations
          const newObj = {
            status: form.status,
            name: form.subName,
            parentid: form.parentId
          };

          this.$store.commit("setTreeId", form.parentId);
          this.$store.commit("setSubStatus", "");
          this.$store.dispatch("createSubChannel", newObj);
          break;
        case 5:
          this.$store.commit("setTreeId", form.parentid);
          this.$store.dispatch("updateSubChannel", form);
          break;
        case 7:
          // eslint-disable-next-line no-case-declarations
          const newForm = {
            ...this.form,
            timebegin: form.putInTime[0],
            timeend: form.putInTime[1],
            fee: Number(form.fee)
          };
          delete newForm.putInTime;
          this.$store.commit("setFeeStatus", "");
          this.$store.dispatch("createChannelFee", newForm);
          break;
      }
    }
  }
};
</script>
<style lang="less" scoped>
.channel-edit {
  ::v-deep .el-form-item__content,
  ::v-deep .el-form-item__label {
    line-height: 32px;
  }
  ::v-deep .el-dialog__body {
    padding: 0 16px 16px 16px;
    height: 357px;
  }
  ::v-deep .el-input {
    width: 400px;
  }
  .special {
    color: @base-color;
  }
  ::v-deep .el-input-number {
    line-height: 32px;
    .el-input__inner {
      text-align: left;
    }
  }
  ::v-deep .el-date-editor.el-range-editor {
    width: 400px;
  }
}
</style>
