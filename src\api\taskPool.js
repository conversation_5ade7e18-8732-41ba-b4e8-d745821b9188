import axios from "../http";
import Vue from "vue";
import { fetchGet } from "../fetch";
// fetchPost
import qs from "qs";

// 排行榜
export const taskpoolRankingList = (data) => {
  return axios
    .get(`/api/order-service/admin/order/taskpool/responsible-board`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
// 新招量
export const taskpoolNewInvite = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/order-service/admin/order/taskpool/responsible-order?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 目标数
export const targetNum = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/taskpool/config/search?${new_data}`);
};
// 信息量、折线图
export const responsibleChart = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/taskpool/responsible-chart?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
// 有效量
export const responsibleValid = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/taskpool/responsible-valid?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 待跟进客户
export const responsibleFollow = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/taskpool/responsible-follow?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 待试听客户
export const responsibleVisit = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/market-service/taskpool/responsible-visit?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 试听量
export const responsibleAudition = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/school-service/audition/taskpool/responsible-audition?${new_data}`
    )
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 配置列表
export const configList = (data) => {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/market-service/taskpool/config/list?${new_data}`);
};
// 配置删除
export const configRemove = (data) => {
  return axios
    .get(`/api/market-service/taskpool/config/remove`, {
      params: data
    })
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 配置-创建
export const configCreate = (data) => {
  return axios
    .post(`/api/market-service/taskpool/config/create`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};

// 配置-创建
export const configSave = (data) => {
  return axios
    .post(`/api/market-service/taskpool/config/save`, data)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
};
