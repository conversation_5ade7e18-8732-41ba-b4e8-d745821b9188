import { fetchGet, fetchPost, fetchExport } from "../fetch";
import qs from "qs";

const api_path = "/api/student-service/student-transfer";
// 查询学员在校信息
function getStudentInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`${api_path}/student-school-info?${new_data}`);
}

// 转校原因
function getChangeReason(data) {
  return fetchGet(`${api_path}/reason-change-type-list`, { params: data });
}

// 选择校区
function getDepartmentList(data) {
  return fetchGet(`/api/organization-service/overr-view/school-list`, {
    params: data
  });
}

// 转校转费
function SchoolTransferFee(data) {
  return fetchPost(`${api_path}/student-school-wallet-change`, data, "");
}

// 转校
function changeSchool(data) {
  return fetchPost(`${api_path}/student-school-change`, data, "");
}

// 转费
function transferFee(data) {
  return fetchPost(`/api/order-service/admin/fee-transfer/create`, data, "");
}

// 学员剩余课程
function studentRemainCourse(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/wallet/residue/course?${new_data}`);
}

// 来源校区
function getSchoolOrigin(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });

  return fetchGet(`${api_path}/student-school-info?${new_data}`);
}
// 转校转费申请列表
function getApproveList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(`/api/order-service/admin/approve/list?${new_data}`);
}
// 转校转费申请导出
function approveListExport(data) {
  return fetchExport(`/api/order-service/admin/approve/export`, data);
}
// 转校转费申请详情
function getTransferFeeDetail(data) {
  return fetchGet(`/api/order-service/admin/approve/detail`, {
    params: data
  });
}

export default {
  getStudentInfo,
  getChangeReason,
  SchoolTransferFee,
  changeSchool,
  getDepartmentList,
  studentRemainCourse,
  transferFee,
  getSchoolOrigin,
  getApproveList,
  approveListExport,
  getTransferFeeDetail
};
