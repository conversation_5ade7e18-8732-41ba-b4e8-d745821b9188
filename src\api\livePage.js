import axios from "../http";
import Vue from "vue";
import qs from "qs";
const fetch = require("../fetch");
// 创建直播
function liveCreate(data) {
  return axios
    .post(`/api/live-go-service/admin/course/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      return Promise.reject(error);
    })
    .finally();
}

// 获取直播课选择排课班主任列表
function teacherList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/header-teacher-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取直播列表
function liveList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取台上人数类型
function stageType(data) {
  return axios
    .get(`/api/live-go-service/admin/course/stage-number-type`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取直播课选择排课学生列表
function studentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/student-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取监课管理考勤明细
function supervisionStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/live-go-service/admin/teacher/supervision/student-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 获取直播课详情
function liveEdit(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/detail?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取已选直播课
function schedulingSelected(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/scheduling/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取userSig
function getUserSig(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 开始录制
function liveCreateCloudRecording(data) {
  return axios
    .post(`/api/live-go-service/admin/course/create-cloud-recording`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 解散直播间
function liveDismissRoom(data) {
  return axios
    .post(`/api/live-go-service/admin/course/dismiss-room`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取直播间、老师信息
function getRoomInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/room-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 直播列表导出

function courseExport(data) {
  return fetch.fetchExport(`/api/live-go-service/admin/course/export`, data);
}
// 学生数导出
function outsideStudentExport(data) {
  return fetch.fetchExport(
    `/api/live-go-service/admin/course/outside-student-export`,
    data
  );
}
// 题库数据
function questionBank(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/question-bank?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取台上人员姓名和头像
function getStudentName(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/students-info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 老师点击全体人员操作
function teacherControl(data) {
  return axios
    .post(`/api/live-go-service/admin/course/control`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 结束答题
function endQuestion(data) {
  return axios
    .post(`/api/live-go-service/admin/course/end-question`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课件上传
// 新建分类
function coursewareCreate(data) {
  return axios
    .post(`/api/live-go-service/admin/courseware/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取抽奖学员列表
function lottoList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/teacher/lotto-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 授权
function coursewareAuth(data) {
  return axios
    .post(`/api/live-go-service/admin/courseware/auth`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取抽奖结果
function lottoResult(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/teacher/lotto-result?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除课件
function coursewareDelete(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/courseware/delete?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取台上人员排序
function upStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/teacher/up_student-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 花名册
function rosterList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/teacher/roster-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 答题器设置答案
function machineAnswer(data) {
  return axios
    .post(`/api/live-go-service/admin/course/machine-answer`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 答题器结束答题
function finashMachineAnswer(data) {
  return axios
    .post(`/api/live-go-service/admin/course/end-machine-answer`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 老师退出直播间
function teacherOut(data) {
  return axios
    .post(`/api/live-go-service/admin/course/outer`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课件列表
function coursewareList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/teacher/courseware-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取课件列表
function coursewareLists(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/courseware/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取直播状态
function courseStatus(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/course/course-status?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 上传课件
function coursewareUpload(data) {
  return axios
    .post(`/api/live-go-service/admin/courseware/upload`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取课件详情
function coursewareInfo(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/courseware/info?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 课件列表
function coursewareFileList(data) {
  return axios
    .get(`/api/live-go-service/admin/courseware/file-list`, {
      params: data,
      type: "live"
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}

// 获取课堂报告
function teacherLiveCourseReport(data) {
  return axios
    .get(`/api/live-go-service/admin/teacher/live-course-report`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 课程数据报告明细
function teacherLiveCourseReportDetail(data) {
  return axios
    .get(`/api/live-go-service/admin/teacher/live-course-report-detail`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}

// 导出课程数据报告明细
function teacherLiveCourseReportDetailExport(data) {
  return fetch.fetchExport(
    `/api/live-go-service/admin/teacher/live-course-report-detail-export`,
    data
  );
}

// 获取回放列表
function transcribeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/live-go-service/admin/transcribe/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 设置虚值
function transcribeVoid(data) {
  return axios
    .post(`/api/live-go-service/admin/transcribe/void`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 导出回放数据
function transcribeExport(data) {
  return fetch.fetchExport(
    `/api/live-go-service/admin/transcribe/export`,
    data
  );
}

// 获取回放数据
function transcribeInfo(data) {
  return axios
    .get(`/api/live-go-service/admin/transcribe/detail`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 上传回放
function transcribeUpload(data) {
  return axios
    .post(`/api/live-go-service/admin/transcribe/update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除回放
function transcribeDelete(data) {
  return axios
    .post(`/api/live-go-service/admin/transcribe/delete`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 监课管理获取直播列表
function supervisionCourseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/live-go-service/admin/teacher/supervision/course-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function TeacherMomentWonder(data) {
  return axios
    .post(`/api/live-go-service/admin/teacher/moment-wonder`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 监课老师进入直播间
function TeacherSupervisionEnter(data) {
  return axios
    .post(`/api/live-go-service/admin/teacher/supervision/enter`, data)
    .then((response) => {
      return response;
    })
    .finally();
}

// 监课老师退出直播间
function TeacherSupervisionOuter(data) {
  return axios
    .post(`/api/live-go-service/admin/teacher/supervision/outer`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 权限设置
function permissionSet(data) {
  return axios
    .post(`/api/live-go-service/admin/permission/set`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取权限
function permissionGet(data) {
  return axios
    .get(`/api/live-go-service/admin/permission/get`, {
      params: data
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 敏感词汇筛选
function courseSensitive(data) {
  return axios
    .post(`/api/live-go-service/admin/course/sensitive`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取排课的学生详情
function teacherLiveScheduleList(data) {
  return axios
    .get(`/api/live-go-service/admin/teacher/live-schedule-list`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 撤回聊天消息
function msgRecall(data) {
  return axios
    .post(`/api/live-go-service/admin/course/msg-recall`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 删除直播间
function courseCancel(data) {
  return axios
    .post(`/api/live-go-service/admin/course/cancel`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}

// 直播课外层学生列表
function outsideStudentList(data) {
  return axios
    .get("/api/live-go-service/admin/course/outside-student-list", {
      params: data,
      type: "live"
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}

// 白板推流接口
function whiteboardPush(data) {
  return axios
    .post("/api/live-go-service/admin/course/whiteboard-push", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 查询花名册内的学生是否在房间内
function checkRosterStudent(data) {
  return axios
    .get("/api/live-go-service/admin/teacher/roster-student", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 获取答题器状态
function checkMachineStatus(data) {
  return axios
    .get("/api/live-go-service/admin/course/machine-status", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// // 获取禁言状态
// function commentStatus(data) {
//   return axios
//     .get("/api/live-go-service/admin/course/comment-status", {
//       params: data,
//       type: "live"
//     })
//     .then((response) => {
//       return response;
//     })
//     .catch((error) => {
//       throw error;
//     })
//     .finally();
// }
// 获取评分列表
function teacherLiveEvaluation(data) {
  return axios
    .get("/api/live-go-service/admin/teacher/live-evaluation", {
      params: data,
      type: "live"
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 获取视频墙用户
function viewBoardStudentList(data) {
  return axios
    .get("/api/live-go-service/admin/teacher/view_board_student_list", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 云盘管理
// 上传管理
function cloudDiskUpload(data) {
  return axios
    .post("/api/live-go-service/admin/cloud-disk/upload", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 同步学院
function historyLiveStudent(data) {
  return axios
    .post(`/api/live-go-service/admin/history/live-student`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 删除文件
function cloudDiskDelete(data) {
  return axios
    .get("/api/live-go-service/admin/cloud-disk/delete", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}
// 云盘列表
function cloudDiskList(data) {
  return axios
    .get("/api/live-go-service/admin/cloud-disk/list", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}

// 清除多端登陆
function delManyCache(data) {
  return axios
    .get("/api/live-go-service/admin/course/del-many-cache", {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      throw error;
    })
    .finally();
}

// 课堂导出
function courseExportAll(data) {
  return fetch.fetchExport(
    `/api/live-go-service/admin/teacher/live-course-report-collect-export`,
    data
  );
}
export default {
  async LiveCreate(data) {
    return liveCreate(data);
  },
  async TeacherList(data) {
    return teacherList(data);
  },
  async LiveList(data) {
    return liveList(data);
  },
  async StageType(data) {
    return stageType(data);
  },
  async StudentList(data) {
    return studentList(data);
  },
  async LiveEdit(data) {
    return liveEdit(data);
  },
  async SchedulingSelected(data) {
    return schedulingSelected(data);
  },
  async getUserSig(data) {
    return getUserSig(data);
  },
  async liveCreateCloudRecording(data) {
    return liveCreateCloudRecording(data);
  },
  async liveDismissRoom(data) {
    return liveDismissRoom(data);
  },
  async CourseExport(data) {
    return courseExport(data);
  },
  async OutsideStudentExport(data) {
    return outsideStudentExport(data);
  },
  async getRoomInfo(data) {
    return getRoomInfo(data);
  },
  async questionBank(data) {
    return questionBank(data);
  },
  async getStudentName(data) {
    return getStudentName(data);
  },
  async teacherControl(data) {
    return teacherControl(data);
  },
  async endQuestion(data) {
    return endQuestion(data);
  },
  async lottoList(data) {
    return lottoList(data);
  },
  async lottoResult(data) {
    return lottoResult(data);
  },
  async upStudentList(data) {
    return upStudentList(data);
  },
  async rosterList(data) {
    return rosterList(data);
  },
  async machineAnswer(data) {
    return machineAnswer(data);
  },
  async finashMachineAnswer(data) {
    return finashMachineAnswer(data);
  },
  async teacherOut(data) {
    return teacherOut(data);
  },
  async coursewareList(data) {
    return coursewareList(data);
  },
  async CoursewareCreate(data) {
    return coursewareCreate(data);
  },
  async CoursewareAuth(data) {
    return coursewareAuth(data);
  },
  async CoursewareDelete(data) {
    return coursewareDelete(data);
  },
  async CoursewareLists(data) {
    return coursewareLists(data);
  },
  async CoursewareUpload(data) {
    return coursewareUpload(data);
  },
  async CoursewareInfo(data) {
    return coursewareInfo(data);
  },
  async CoursewareFileList(data) {
    return coursewareFileList(data);
  },
  async TeacherLiveCourseReport(data) {
    return teacherLiveCourseReport(data);
  },
  async TeacherLiveCourseReportDetail(data) {
    return teacherLiveCourseReportDetail(data);
  },
  async TeacherLiveCourseReportDetailExport(data) {
    return teacherLiveCourseReportDetailExport(data);
  },
  async courseStatus(data) {
    return courseStatus(data);
  },
  async TranscribeList(data) {
    return transcribeList(data);
  },
  async TranscribeVoid(data) {
    return transcribeVoid(data);
  },
  async TranscribeExport(data) {
    return transcribeExport(data);
  },
  async TranscribeInfo(data) {
    return transcribeInfo(data);
  },
  async TranscribeUpload(data) {
    return transcribeUpload(data);
  },
  async TranscribeDelete(data) {
    return transcribeDelete(data);
  },
  async SupervisionCourseList(data) {
    return supervisionCourseList(data);
  },
  async TeacherMomentWonder(data) {
    return TeacherMomentWonder(data);
  },
  async TeacherSupervisionEnter(data) {
    return TeacherSupervisionEnter(data);
  },
  async TeacherSupervisionOuter(data) {
    return TeacherSupervisionOuter(data);
  },
  async PermissionSet(data) {
    return permissionSet(data);
  },
  async PermissionGet(data) {
    return permissionGet(data);
  },
  async CourseSensitive(data) {
    return courseSensitive(data);
  },
  async TeacherLiveScheduleList(data) {
    return teacherLiveScheduleList(data);
  },
  supervisionStudentList,
  async msgRecall(data) {
    return msgRecall(data);
  },
  async CourseCancel(data) {
    return courseCancel(data);
  },
  async OutsideStudentList(data) {
    return outsideStudentList(data);
  },
  async whiteboardPush(data) {
    return whiteboardPush(data);
  },
  async checkRosterStudent(data) {
    return checkRosterStudent(data);
  },
  async checkMachineStatus(data) {
    return checkMachineStatus(data);
  },
  async TeacherLiveEvaluation(data) {
    return teacherLiveEvaluation(data);
  },
  async viewBoardStudentList(data) {
    return viewBoardStudentList(data);
  },
  async cloudDiskList(data) {
    return cloudDiskList(data);
  },
  async cloudDiskUpload(data) {
    return cloudDiskUpload(data);
  },
  async cloudDiskDelete(data) {
    return cloudDiskDelete(data);
  },
  async historyLiveStudent(data) {
    return historyLiveStudent(data);
  },
  async DelManyCache(data) {
    return delManyCache(data);
  },
  async courseExportAll(data) {
    return courseExportAll(data);
  }
  // async CommentStatus(data) {
  //   return commentStatus(data);
  // }
};
