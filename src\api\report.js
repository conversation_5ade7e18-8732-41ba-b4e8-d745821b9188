import qs from "qs";
const fetch = require("../fetch");

// 班级花名册
function getClassRoster(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/school-service/classroom/roster?${new_data}`);
}
// 班级花名册导出
function classRosterExport(data) {
  return fetch.fetchExport(`/api/school-service/classroom/roster-export`, data);
}

// 出勤明细表列表
function getAttendanceDetailList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/class/attendance?${new_data}`
  );
}
// 出勤明细表合计
function getAttendanceDetailListStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/class/attendance-statistics?${new_data}`
  );
}
// 退费明细表
function getRefundDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/refund/item/list?${new_data}`
  );
}
// 退费明细表合计
function getRefundDetailStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/refund/item/list-statistics?${new_data}`
  );
}
// 退费明细表导出
function refundDetailExport(data) {
  return fetch.fetchExport(`/api/order-service/admin/refund/item/export`, data);
}
// 出勤明细导出
function attendanceDetailsExport(data) {
  return fetch.fetchExport(
    `/api/order-service/admin/class/attendance/export`,
    data
  );
}

// 出勤明细报表-某个学员的课消情况
function getAttendancePaymentDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/class/payment/list?${new_data}`
  );
}
// 扣款汇总表
function getDeductListDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/refund/deduct/list?${new_data}`
  );
}
// 扣款汇总表合计
function getDeductListDetailStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/refund/deduct/list-statistics?${new_data}`
  );
}
// 扣款汇总表导出
function deductDetailExport(data) {
  return fetch.fetchExport(
    `/api/order-service/admin/refund/deduct/export`,
    data
  );
}
// 满班率列表
function getFullShiftRateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/school-service/classroom/full-shift-rate?${new_data}`
  );
}
// 满班率列表
function getFullShiftRateTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/school-service/classroom/full-shift-rate-total?${new_data}`
  );
}

// 满班率导出
function fullShiftRateExportExport(data) {
  return fetch.fetchExport(
    `/api/school-service/classroom/full-shift-rate-export`,
    data
  );
}
// 出勤率列表
function getDeductPresentRatio(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/deduct/present-ratio?${new_data}`
  );
}
// 出勤率合计
function getDeductPresentRatioTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/deduct/present-ratio-total?${new_data}`
  );
}
// 出勤率导出
function deductPresentRatioExport(data) {
  return fetch.fetchExport(
    `/api/report-center-service/admin/deduct/present-ratio-export`,
    data
  );
}
// 市场渠道报表
function getCustomerChannel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/market-service/report/customer-channel?${new_data}`
  );
}
// 市场渠道报表导出
function customerChannelExport(data) {
  return fetch.fetchExport(
    `/api/market-service/report/customer-channel-export`,
    data
  );
}
// 市场周转化率列表
function getConversionList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/conversion/list?${new_data}`);
}

// 市场任务指标列表
function getIndicatorList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/indicator/list?${new_data}`);
}
// 市场任务指标获取市场专员
function getIndicatorEmployee(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/market-service/indicator/market-list?${new_data}`
  );
}
export default {
  getClassRoster,
  getRefundDetail,
  getRefundDetailStatistics,
  refundDetailExport,
  classRosterExport,
  getAttendanceDetailList,
  getAttendanceDetailListStatistics,
  attendanceDetailsExport,
  getAttendancePaymentDetail,
  getDeductListDetail,
  getDeductListDetailStatistics,
  deductDetailExport,
  getFullShiftRateList,
  getFullShiftRateTotal,
  fullShiftRateExportExport,
  getDeductPresentRatio,
  getDeductPresentRatioTotal,
  deductPresentRatioExport,
  getCustomerChannel,
  customerChannelExport,
  getConversionList,
  getIndicatorList,
  getIndicatorEmployee
};
