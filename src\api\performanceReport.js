import qs from "qs";
const fetch = require("../fetch");

// 老师业绩明细
function getTeacherDeductDetail(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/deduct/teacher-deduct-detail?${new_data}`
  );
}
// 老师业绩明细汇总
function getTeacherDeductSummary(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/deduct/teacher-deduct-summary?${new_data}`
  );
}
// 老师业绩明细合计
function getTeacherDeductDetailTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/deduct/teacher-deduct-detail-total?${new_data}`
  );
}
// 老师业绩汇总合计
function getTeacherDeductSummaryTotal(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/deduct/teacher-deduct-summary-total?${new_data}`
  );
}
export default {
  getTeacherDeductDetail,
  getTeacherDeductSummary,
  getTeacherDeductDetailTotal,
  getTeacherDeductSummaryTotal
};
