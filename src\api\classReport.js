import axios from "../http";
import Vue from "vue";
import qs from "qs";

function exportClassStudentNumber(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/class/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getClassStudentNumber(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/class/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getClassStudentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/class/student-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportShiftCourseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/shift-classroom-course/course-export?${new_data}`,
      {
        params: { exportData: 1 }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getShiftCourseList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/shift-classroom-course/course-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getShiftClassList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/shift-classroom-course/classroom-list?${new_data}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function exportShiftClassList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(
      `/api/report-center-service/admin/shift-classroom-course/classroom-export?${new_data}`,
      {
        params: { exportData: 1 }
      }
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async exportClassStudentNumber(data) {
    return exportClassStudentNumber(data);
  },
  async getClassStudentNumber(data) {
    return getClassStudentNumber(data);
  },
  async getClassStudentList(data) {
    return getClassStudentList(data);
  },
  async exportShiftCourseList(data) {
    return exportShiftCourseList(data);
  },
  async getShiftCourseList(data) {
    return getShiftCourseList(data);
  },
  async getShiftClassList(data) {
    return getShiftClassList(data);
  },
  async exportShiftClassList(data) {
    return exportShiftClassList(data);
  }
};
