import axios from "../http";
import Vue from "vue";

// 创建部门
function departmentCreate(data) {
  return axios
    .post(`/api/organization-service/department/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 编辑部门信息
function departmentUpdate(data) {
  return axios
    .patch(
      `/api/organization-service/department/update?department_id=${data.department_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 部门信息
function departmentInfo(data) {
  return axios
    .get(
      `/api/organization-service/department/info?department_id=${data.department_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取部门列表
function organizationOverView(data) {
  return axios
    .get(`/api/organization-service/overr-view/info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除部门
function departmentDelete(data) {
  return axios
    .delete(
      `/api/organization-service/department/delete?department_id=${data.id}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取校区
function getSchoolPart(data) {
  return axios
    .get(`/api/organization-service/overr-view/school`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取带部门和区域的校区id
function getAllDepartment(data) {
  return axios
    .get(`/api/permission-service/employee/all-department`, {
      params: data
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  // 创建部门
  async DepartmentCreate(data) {
    return departmentCreate(data);
  },
  // 编辑单个员工信息
  async DepartmentUpdate(data) {
    return departmentUpdate(data);
  },
  // 部门信息信息
  async DepartmentInfo(data) {
    return departmentInfo(data);
  },

  // 删除线索
  async DepartmentDelete(data) {
    return departmentDelete(data);
  },
  // 部门总览
  async OrganizationOverView(data) {
    return organizationOverView(data);
  },
  async getSchoolPart(data) {
    return getSchoolPart(data);
  },
  async getAllDepartment(data) {
    return getAllDepartment(data);
  }
};
