<!--撤销试听-->
<template>
  <el-dialog
    :visible="true"
    title="撤销试听"
    class="cancel-audition"
    custom-class="cancel-audition"
    width="800px"
    top="6vh"
    :modal="false"
    :before-close="cancel"
  >
    <div class="cancel-audition__title tg-box--margin">
      <img src="../../assets/图片/icon_permission.png" alt="" />
      <span>此操作会将学员从<em>试听排课中移除</em>，是否确定撤销？</span>
    </div>
    <el-form :form="form" class="tg-box--margin" label-width="70px">
      <el-form-item label="撤销原因">
        <el-input
          type="textarea"
          placeholder="请输入撤销原因"
          v-model="form.memo"
        ></el-input>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="tg-button--plain" type="plain" @click="cancel"
        >取消</el-button
      >
      <el-button class="tg-button--primary" type="primary" @click="really"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
import schoolServiceSchedulingExcelApi from "@/api/schoolServiceScheduling";
export default {
  data() {
    return {
      form: {
        memo: ""
      }
    };
  },
  props: {
    id: String,
    student_id: Array
  },
  methods: {
    cancel(val) {
      this.$emit("close", val ? this.student_id : "");
    },
    really() {
      schoolServiceSchedulingExcelApi
        .removeAudition({
          memo: this.form.memo,
          student_ids: this.student_id,
          scheduling_id: this.id
        })
        .then((res) => {
          if (res && !res.err) {
            this.$message.success("提交成功!");
            this.dialogFinished = false;
          }
          this.cancel(true);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.cancel-audition {
  ::v-deep .el-dialog__body {
    padding: 0 16px;
    height: 373px;
  }
  .cancel-audition__title {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 32px;
    line-height: 32px;
    border: 1px solid @base-color;
    border-radius: 4px;
    padding-left: 16px;
    background-color: #f5f8fc;
    img {
      width: 12px;
      height: 12px;
      margin-right: 10px;
    }
    span {
      color: #b3b7c6;
      font-family: @text-famliy_medium;
      font-size: @text-size_special;
    }
    em {
      font-style: normal;
      color: #ff0317;
    }
  }
  ::v-deep .el-textarea__inner {
    min-height: 180px !important;
  }
}
</style>
