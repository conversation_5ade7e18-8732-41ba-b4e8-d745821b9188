/* 整个滚动条 */
html,
body {
  /* overflow-y: hidden!important; */
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #c2c6c8;
  /* 关键代码 */
  /* background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.4) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.4) 75%,
    transparent 75%,
    transparent
  ); */
  border-radius: 32px;
  cursor: pointer;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: #dbeffd;
  border-radius: 32px;
}

.el-select .el-select__tags {
  overflow: hidden !important;
}

.table-container:has(.el-table--scrollable-x)::after {
  bottom: 62px !important;
}

.table-container:not(:has(.tg-pagination)):has(.el-table--scrollable-x)::after {
  bottom: 10px !important;
}

.table-container:not(:has(.tg-pagination)):not(:has(.el-table--scrollable-x))::after {
  bottom: 0px !important;
}

/* 有合计且可滚动的表格更改合计高度 */
/* .el-table--scrollable-x .el-table__footer-wrapper,
.el-table--scrollable-x .el-table__footer-wrapper::after {
  bottom: 10px !important;
} */



.el-select .el-select__tags {
  overflow: hidden !important;
}

.tg-select--dialog [class*="select__inner"] {
  overflow-x: hidden !important;
}