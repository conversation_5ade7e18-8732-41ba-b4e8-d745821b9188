/**
 * 网校期次管理
 */

import qs from "qs";
const fetch = require("../fetch");
const api_path = "/api/market-service/period";
// 期次列表
function getList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${api_path}/list?${new_data}`);
}
// 收费时期次列表
function getChargeList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`${api_path}/list-all?${new_data}`);
}
// 新增期次
function add(data) {
  return fetch.fetchPost(`${api_path}/create`, data, "");
}
// 批量新增
function addBatch(data) {
  return fetch.fetchPost(`${api_path}/batch-create`, data, "");
}

export default {
  getList,
  add,
  getChargeList,
  addBatch
};
