import { fetchGet, fetchPost } from "../fetch";
import qs from "qs";
// 创建物品类别
function createCategroy(data) {
  return fetchPost(
    "/api/order-service/admin/category-configuration/parent/create",
    data
  );
}
// 获取类别列表
function getCategroyList(data) {
  return fetchGet(
    "/api/order-service/admin/category-configuration/parent/list",
    data
  );
}
// 修改类别
function updateCategroy(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/parent/update`,
    data
  );
}
// 删除类别
function delCategroy(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/parent/delete`,
    data
  );
}
// 添加物品
function createGoods(data) {
  return fetchPost(
    "/api/order-service/admin/category-configuration/son/update",
    data
  );
}
// 删除物品
function delGoods(data) {
  return fetchPost(
    "/api/order-service/admin/category-configuration/son/delete",
    data
  );
}
// 物品列表
function getGoodsList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/category-configuration/son/list?${new_data}`
  );
}
// 更新物品
function updateGoods(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/son/update/status`,
    data
  );
}
// 关联校区
function associatedSchool(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/son/relation/department`,
    data
  );
}

function updateStatus(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/son/update/status`,
    data
  );
}

function refundTransferReason(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/category-configuration/refund/transfer/reason?${new_data}`
  );
}

function reasonSonChildrenUpdate(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/son/children/update`,
    data
  );
}
function reasonSonChildrenUpdateStatus(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/son/children/update/status`,
    data
  );
}
function reasonSonChildrenUpdateAutoDrop(data) {
  return fetchPost(
    `/api/order-service/admin/category-configuration/son/auto-drop`,
    data
  );
}
function getReasonList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetchGet(
    `/api/order-service/admin/category-configuration/refund/reason?${new_data}`
  );
}
export default {
  async createCategroy(data) {
    return createCategroy(data);
  },
  async getCategroyList(data) {
    return getCategroyList(data);
  },
  async updateCategroy(data, msg) {
    return updateCategroy(data, msg);
  },
  async delCategroy(data) {
    return delCategroy(data);
  },
  async createGoods(data) {
    return createGoods(data);
  },
  async delGoods(data) {
    return delGoods(data);
  },
  async getGoodsList(data) {
    return getGoodsList(data);
  },
  async updateGoods(data, msg) {
    return updateGoods(data, msg);
  },
  async associatedSchool(data) {
    return associatedSchool(data);
  },
  async updateStatus(data) {
    return updateStatus(data);
  },
  async refundTransferReason(data) {
    return refundTransferReason(data);
  },
  async reasonSonChildrenUpdate(data) {
    return reasonSonChildrenUpdate(data);
  },
  async getReasonList(data) {
    return getReasonList(data);
  },
  async reasonSonChildrenUpdateStatus(data) {
    return reasonSonChildrenUpdateStatus(data);
  },
  async reasonSonChildrenUpdateAutoDrop(data) {
    return reasonSonChildrenUpdateAutoDrop(data);
  }
};
