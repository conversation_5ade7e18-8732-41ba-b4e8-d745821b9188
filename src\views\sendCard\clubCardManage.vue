<template>
  <div class="clubCardManage">
    <tg-search
      :searchTitle.sync="search_title"
      :form.sync="searchForm"
      :showNum="1"
      @reset="reset"
      @search="searchVal"
      :searchLoadingState="searchLoading"
      class="search"
      :isExport="isExport"
      @educe="exportExcel"
      :loadingState="exportLoading"
    ></tg-search>
    <el-row class="tg-box--margin tg-shadow--margin tg-row--height">
      <el-button type="plain" class="tg-button--plain" @click="importTemplate"
        >导入会员卡</el-button
      >
    </el-row>
    <div class="tg-table__box tg-table-margin">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        :data="list"
        tooltip-effect="dark"
        class="tg-table"
        border
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
      >
        <template v-for="(item, index) in tableTitle">
          <el-table-column
            v-if="item.show"
            :key="index"
            :prop="item.props"
            :label="item.label"
            :min-width="item.width"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <span v-if="item.props === 'card_number'" class="copy_name">
                {{ scope.row.card_number }}

                <div v-copy="scope.row.card_number"></div>
              </span>
              <span v-else-if="item.props === 'validity_type'">
                {{
                  scope.row.validity_type === 1
                    ? "月卡"
                    : scope.row.validity_type === 2
                    ? "半年卡"
                    : scope.row.validity_type === 3
                    ? "年卡"
                    : scope.row.validity_type === 4
                    ? "双周卡"
                    : scope.row.validity_type === 5
                    ? "季卡"
                    : scope.row.validity_type === 6
                    ? "双月卡"
                    : "周卡"
                }}
              </span>
              <div
                v-else-if="item.props === 'card_password'"
                :style="{
                  display: 'flex',
                  'justify-content': !$_has({ m: 'all_phone', o: 'has_limit' })
                    ? 'left'
                    : 'space-between',
                  'align-items': 'center'
                }"
              >
                <mobileHyposensitization
                  :mobileTemInfo="{
                    row: scope.row,
                    has_eye_limit: scope.row?.has_eye_limit,
                    mobile: scope.row.card_password
                  }"
                ></mobileHyposensitization>
                <div v-copy="scope.row.card_password"></div>
              </div>
              <span v-else>{{ scope.row[scope.column.property] }}</span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" width="220">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              v-has="{ m: 'goods', o: 'update' }"
              class="tg-text--blue tg-span__divide-line"
              @click="
                send_id = scope.row.id;
                add_student_visible = true;
              "
              >学员发卡</el-button
            >
            <el-button
              v-if="scope.row.validity_type === 15"
              type="text"
              size="small"
              class="tg-text--blue tg-span__divide-line"
              v-has="{ m: 'goods', o: 'delete' }"
              @click="
                send_id = scope.row.id;
                add_customer_visible = true;
              "
              >意向客户发卡</el-button
            >
          </template>
        </el-table-column>
        <template slot="empty">
          <div style="margin-top: 15%">
            <TgLoading v-if="loading"></TgLoading>
            <div class="empty-container" v-else>暂无数据～</div>
          </div>
        </template>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <orderImport v-if="importStu_visible" @close="closeWindow"></orderImport>
    <choose-student
      type="radio"
      v-if="add_student_visible"
      :has_modal="false"
      :check_arr.sync="studentList"
      :department_id="departmentId"
      @close="add_student_visible = false"
      @confirm="studentConfirm"
    ></choose-student>
    <choose-customer
      type="radio"
      v-if="add_customer_visible"
      :has_modal="false"
      :check_arr.sync="studentList"
      :department_id="departmentId"
      @close="add_customer_visible = false"
      @confirm="studentConfirm"
    ></choose-customer>
  </div>
</template>

<script>
import yikeCard from "@/api/yikeCard";
import orderImport from "./common/orderImport.vue";
import chooseStudent from "./common/chooseStudent.vue";
import chooseCustomer from "./common/chooseCustomer.vue";
// import { downLoadFile } from "@/public/downLoadFile";

export default {
  data() {
    return {
      studentList: [],
      add_student_visible: false,
      add_customer_visible: false,
      importStu_visible: false,
      can_see: false,
      loading: false,
      exportLoading: false,
      isExport: true,
      search_title: [
        {
          props: "validity_type",
          label: "有效期类型",
          type: "select",
          show: true,
          selectOptions: [
            { name: "全部", id: undefined },
            { name: "月卡", id: "1" },
            { name: "半年卡", id: "2" },
            { name: "年卡", id: "3" },
            { name: "双周卡", id: "4" },
            { name: "季卡", id: "5" },
            { name: "双月卡", id: "6" },
            { name: "周卡", id: "15" }
          ]
        }
      ],
      searchForm: {
        validity_type: undefined
      },
      searchLoading: false,
      page: 1,
      pageSize: 10,
      total: 0,
      tableTitle: [
        {
          props: "card_number",
          label: "卡号",
          show: true,
          width: 160
        },
        {
          props: "card_password",
          label: "卡密码",
          show: true,
          width: 100
        },
        { props: "department_name", label: "校区", show: true, width: 100 },
        { props: "validity_type", label: "有效期类型", show: true, width: 100 }
        // { props: "5", label: "卡类型", show: true, width: 100 }
      ],
      list: [],
      send_id: ""
    };
  },
  computed: {
    departmentId() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  components: { orderImport, chooseStudent, chooseCustomer },
  created() {
    this.searchVal();
  },
  methods: {
    studentConfirm(data) {
      console.log(this.studentList);
      yikeCard
        .sendMembership({
          id: this.send_id,
          card_type: 1,
          p_type: data.p_type,
          student_id: this.studentList[0].student_id
        })
        .then((res) => {
          console.log(res);
        });
    },
    closeWindow() {
      this.importStu_visible = false;
      this.searchVal();
    },
    importTemplate() {
      this.importStu_visible = true;
    },
    exportExcel() {
      this.$confirm("是否导出会员卡管理列表", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          yikeCard
            .membershipExport({
              departmentId: this.departmentId,
              validity_type: this.searchForm.validity_type
            })
            .then((res) => {
              console.log(res);
              const blob = new Blob([res.data], {
                type: "application/vnd.ms-excel"
              }); // type这里表示xlsx类型
              const downloadElement = document.createElement("a");
              const href = window.URL.createObjectURL(blob); // 创建下载的链接
              downloadElement.href = href;
              downloadElement.download = `会员卡管理列表.xlsx`; // 下载后文件名
              document.body.appendChild(downloadElement);
              downloadElement.click(); // 点击下载
              document.body.removeChild(downloadElement);
            });
          this.$message.success("导出成功!");
        })
        .catch(() => {
          this.$message.info("取消导出");
        });
    },

    reset() {
      this.page = 1;
      this.searchForm.validity_type = undefined;
      this.searchVal();
    },
    searchVal() {
      this.loading = true;
      yikeCard
        .membershipList({
          departmentId: this.departmentId,
          validity_type: this.searchForm.validity_type,
          page: this.page,
          page_size: this.pageSize
        })
        .then((res) => {
          if (res.data.code === 0) {
            this.list = res.data.data.results ?? [];
            this.total = res.data.data.count ?? 0;
            this.loading = false;
          } else {
            this.loading = false;
            this.$message.error(res.data.message);
          }
        });
    },
    currentChange(val) {
      this.page = val;
      this.searchVal();
    },
    sizeChange(val) {
      this.page = 1;
      this.pageSize = val;
      this.searchVal();
    }
  }
};
</script>

<style lang="less" scoped>
.clubCardManage {
  ::v-deep .close_eye {
    margin-left: 20px;
  }
  ::v-deep .open_eye {
    margin-left: 20px;
  }
  ::v-deep .copy_img {
    margin-left: 5px;
  }
}
</style>
