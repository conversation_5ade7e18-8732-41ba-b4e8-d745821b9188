import qs from "qs";
const fetch = require("../fetch");

// 在读课程统计
function getCourseStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/course-statistics?${new_data}`
  );
}

// 沟通记录统计
function getCommiuncateStatistics(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/market-service/comm-report/statistics?${new_data}`
  );
}

// 沟通记录列表
function getCommiuncateList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/comm-report/list?${new_data}`);
}

// 沟通记录详情列表
function getDetailList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/comm-report/detail?${new_data}`);
}

// 沟通记录导出
function exportConnectList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/market-service/comm-report/export?${new_data}`, {
    params: { exportData: 1 }
  });
}

// 在读课程统计导出
function exportCourseStatisticsList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/course-statistics-export?${new_data}`,
    {
      params: { exportData: 1 }
    }
  );
}

export default {
  getCourseStatistics,
  getCommiuncateStatistics,
  getCommiuncateList,
  getDetailList,
  exportConnectList,
  exportCourseStatisticsList
};
