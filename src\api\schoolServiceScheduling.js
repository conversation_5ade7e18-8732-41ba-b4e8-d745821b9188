import axios from "../http";
import Vue from "vue";
import qs from "qs";
const api_path = `/api/school-service/scheduling`;
// 排课管理-排课列表
function getSchoolServicesChedulingList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 补课跟班-选择排课排课列表
function getSchoolServicesChedulingMakeUpList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/scheduling/make-up/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 试听跟班-选择排课排课列表
function getSchoolServicesChedulingAuditionList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/scheduling/audition/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 直播管理-选择排课列表
function getSchoolServicesChedulingLiveList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/scheduling/live/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 课表和点名显示的排课信息
function getRollCallList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/roll-call/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 排课列表-总计
function getSchoolServicesChedulingTotalList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`${api_path}/list-total?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 新增排课
function createScheduling(data) {
  return axios
    .post(`${api_path}/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 排课详情
function infoScheduling(data) {
  return axios
    .get(`${api_path}/info`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 修改排课
function updateScheduling(data) {
  return axios
    .patch(`${api_path}/update?scheduling_id=${data.id}`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 动态获取课消信息
function getRelatedCourse(data) {
  return axios
    .post("/api/course-service/course-class/related-course", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 撤销排课
function getSchedulingRevocation(data) {
  return axios
    .post(`/api/school-service/scheduling/revocation`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 预览排课
function getClassroomPreview(data) {
  return axios
    .post(`/api/school-service/scheduling/preview`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return error;
    })
    .finally();
}

// 获取年份
function getCourseServiceCourseMapYear(data) {
  return axios
    .get(`/api/course-service/course/map/year`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程属性
function getCourseServiceCourseMapProperty(data) {
  return axios
    .get(`/api/course-service/course/map/property`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取课程类型
function getCourseServiceCourseMapType(data) {
  return axios
    .get(`/api/course-service/course/map/type`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取上课状态
function getCourseServiceCourseMapStatus(data) {
  return axios
    .get(`/api/school-service/scheduling/map/status`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取上课形式
function getCourseServiceSchedulingMapform(data) {
  return axios
    .get(`/api/school-service/scheduling/map/form`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 班级导出
function schoolServiceSchedulingExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/scheduling/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 批量删除班级
function schoolServiceSchedulingDelete(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/school-service/scheduling/delete?${new_data}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 取消排课
function cancelScheduling(data) {
  return axios
    .post(`/api/school-service/scheduling/cancelled`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 开始上课
function beginClass(data) {
  return axios
    .post(
      `/api/school-service/scheduling/begin?scheduling_id=${data.scheduling_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
function getClassStudentList(data) {
  return axios
    .get(
      `/api/school-service/scheduling/student/list?scheduling_id=${data.scheduling_id}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
// 添加学生
function addSchedulingStudent(data) {
  return axios
    .post(`/api/school-service/scheduling/student/add-temporary`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 学员进垃圾桶
function removeTrash(data) {
  return axios
    .post(`/api/school-service/scheduling/student/remove-trash`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除临加学员
function removeTemp(data) {
  return axios
    .post(`/api/school-service/scheduling/student/remove-temp`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除补课
function removeMakeup(data) {
  return axios
    .post(`/api/school-service/scheduling/student/remove-make-up`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 学员撤销垃圾桶
function rollbackTrash(data) {
  return axios
    .post(`/api/school-service/scheduling/student/rollback-trash`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 撤销试听
function removeAudition(data) {
  return axios
    .post(`/api/school-service/scheduling/student/remove-audition`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 修改上课内容
function updateContext(data) {
  return axios
    .patch(
      `/api/school-service/scheduling/update-context?scheduling_id=${data.scheduling_id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量修改
function batchUpdate(data) {
  return axios
    .post(`/api/school-service/scheduling/batch-update`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 批量修改 详情
function batchDetail(data) {
  return axios
    .get(`/api/school-service/scheduling/batch-detail`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

function lockRange(data) {
  return axios
    .post("/api/school-service/check-in/lock-account", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
function schedulingCopy(data) {
  return axios
    .post("/api/school-service/scheduling/copy", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
function schedulingMove(data) {
  return axios
    .post("/api/school-service/scheduling/move", data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
      return Promise.reject(error);
    })
    .finally();
}
export default {
  createScheduling,
  infoScheduling,
  updateScheduling,
  schedulingCopy,
  schedulingMove,
  // 补课管理-排课列表
  async GetSchoolServicesChedulingList(data) {
    return getSchoolServicesChedulingList(data);
  },
  // 补课-排课列表
  async GetSchoolServicesChedulingMakeUpList(data) {
    return getSchoolServicesChedulingMakeUpList(data);
  },
  // 试听-排课列表
  async GetSchoolServicesChedulingAuditionList(data) {
    return getSchoolServicesChedulingAuditionList(data);
  },
  // 直播-排课列表
  async GetSchoolServicesChedulingLiveList(data) {
    return getSchoolServicesChedulingLiveList(data);
  },
  async GetRollCallList(data) {
    return getRollCallList(data);
  },
  async GetSchoolServicesChedulingTotalList(data) {
    return getSchoolServicesChedulingTotalList(data);
  },
  // 导出
  async SchoolServiceSchedulingExcel(data) {
    return schoolServiceSchedulingExcel(data);
  },

  // 删除班级
  async schoolServiceSchedulingDelete(data) {
    return schoolServiceSchedulingDelete(data);
  },
  // 年份
  async GetCourseServiceCourseMapYear(data) {
    return getCourseServiceCourseMapYear(data);
  },
  // 课程属性
  async GetCourseServiceCourseMapProperty(data) {
    return getCourseServiceCourseMapProperty(data);
  },
  // 获取课程类型
  async GetCourseServiceCourseMapType(data) {
    return getCourseServiceCourseMapType(data);
  },
  // 获取上课状态
  async GetCourseServiceCourseMapStatus(data) {
    return getCourseServiceCourseMapStatus(data);
  },
  // 获取上课形式
  async GetCourseServiceSchedulingMapform(data) {
    return getCourseServiceSchedulingMapform(data);
  },
  // 撤销排课
  async GetSchedulingRevocation(data) {
    return getSchedulingRevocation(data);
  },
  // 预览排课
  async GetClassroomPreview(data) {
    return getClassroomPreview(data);
  },
  // 取消排课
  async cancelScheduling(data) {
    return cancelScheduling(data);
  },
  async beginClass(data) {
    return beginClass(data);
  },
  async getClassStudentList(data) {
    return getClassStudentList(data);
  },
  async addSchedulingStudent(data) {
    return addSchedulingStudent(data);
  },
  async removeTrash(data) {
    return removeTrash(data);
  },
  async removeTemp(data) {
    return removeTemp(data);
  },
  async removeMakeup(data) {
    return removeMakeup(data);
  },
  async rollbackTrash(data) {
    return rollbackTrash(data);
  },
  async removeAudition(data) {
    return removeAudition(data);
  },
  async updateContext(data) {
    return updateContext(data);
  },
  async batchUpdate(data) {
    return batchUpdate(data);
  },
  async batchDetail(data) {
    return batchDetail(data);
  },
  async GetRelatedCourse(data) {
    return getRelatedCourse(data);
  },
  async lockRange(data) {
    return lockRange(data);
  }
  // //导出
  // async SchoolServiceClassroomExcel(data) {
  //     return schoolServiceClassroomExcel(data);
  // },

  // // 删除班级
  // async schoolServiceAuditClassroomDelete(data) {
  //     return schoolServiceAuditClassroomDelete(data);
  // },
};
