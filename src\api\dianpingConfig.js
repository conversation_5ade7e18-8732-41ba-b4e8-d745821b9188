/**
 * 大众点评商户管理接口
 */

const fetch = require("../fetch");

// 列表
function getList(data) {
  return fetch.fetchGet(`/api/order-service/admin/dianping/list`, {
    params: data
  });
}
// 绑定
function bind(data) {
  return fetch.fetchPost(`/api/order-service/admin/dianping/bind`, data, "");
}
// 解绑
function uninstall(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/dianping/uninstall`,
    data,
    ""
  );
}

// 商户添加
function rchantAdd(data) {
  return fetch.fetchPost(` /api/order-service/admin/dianping/add`, data, "");
}

export default {
  getList,
  uninstall,
  rchantAdd,
  bind
};
