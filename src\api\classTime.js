import axios from "../http";
import Vue from "vue";

// 获取上课时间列表
function getClassTimeList(data) {
  return axios
    .get(`/api/school-service/class-time/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 获取上课时间
function getClassTimeInfo(data) {
  return axios
    .get(`/api/school-service/class-time/info?class_time_id=${data.id}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 新增上课时间
function getClassTimeCreate(data) {
  return axios
    .post(`/api/school-service/class-time/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 更新上课时间
function classTimeUpdate(data) {
  return axios
    .patch(
      `/api/school-service/class-time/update?class_time_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 删除上课时间
function classTimeDelete(data) {
  return axios
    .delete(`/api/school-service/class-time/delete?class_time_id=${data.id}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取上课时间左侧的列表
function getClassTimeGroupList(data) {
  return axios
    .get(`/api/school-service/class-time/group/list`, { params: data })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 新增上课时间左侧的列表
function getClassTimeGroupCreate(data) {
  return axios
    .post(`/api/school-service/class-time/group/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 删除上课时间左侧的数据
function classTimeGroupDelete(data) {
  return axios
    .delete(`/api/school-service/class-time/group/delete?group_id=${data.id}`)
    .then((response) => {
      if (response.status === 200) {
        Vue.prototype.$message.success("删除成功");
      }
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  // 获取上课时间列表
  async GetClassTimeList(data) {
    return getClassTimeList(data);
  },
  // 获取上课时间
  async GetClassTimeInfo(data) {
    return getClassTimeInfo(data);
  },
  // 新增上课时间
  async GetClassTimeCreate(data) {
    return getClassTimeCreate(data);
  },
  // 更新上课时间
  async ClassTimeUpdate(data) {
    return classTimeUpdate(data);
  },
  // 删除教室
  async ClassTimeDelete(data) {
    return classTimeDelete(data);
  },
  // 获取上课时间左侧的列表
  async GetClassTimeGroupList(data) {
    return getClassTimeGroupList(data);
  },
  // 新增上课时间左侧的列表
  async GetClassTimeGroupCreate(data) {
    return getClassTimeGroupCreate(data);
  },
  // 删除上课时间左侧的数据
  async ClassTimeGroupDelete(data) {
    return classTimeGroupDelete(data);
  }
};
