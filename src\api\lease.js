import axios from "../http";
import Vue from "vue";
import qs from "qs";
// 获取租赁明细列表
function getLeaseDetailList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/allocation/detail-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 结清租赁
function settleLease(data) {
  return axios
    .post(`/api/order-service/admin/allocation/settle-allocation`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取租赁汇总区域列表
function getLeaseSummaryAreaList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/allocation/summary-area?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取校区租赁汇总区域列表
function getLeaseSummaryDepartmentList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/allocation/summary-department?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取物品租赁汇总区域列表
function getLeaseSummaryItemList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/allocation/summary-goods?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取汇总列表租赁
function getLeaseSummaryList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/allocation/summary-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 获取订单下获取租赁物品列表
function getLeaseItemList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/order-service/admin/allocation/item-list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

export default {
  async getLeaseDetailList(data) {
    return getLeaseDetailList(data);
  },
  async settleLease(data) {
    return settleLease(data);
  },
  async getLeaseSummaryAreaList(data) {
    return getLeaseSummaryAreaList(data);
  },
  async getLeaseSummaryDepartmentList(data) {
    return getLeaseSummaryDepartmentList(data);
  },
  async getLeaseSummaryItemList(data) {
    return getLeaseSummaryItemList(data);
  },
  async getLeaseSummaryList(data) {
    return getLeaseSummaryList(data);
  },
  async getLeaseItemList(data) {
    return getLeaseItemList(data);
  }
};
