<template>
  <div id="invoice-index" class="container">
    <div class="invoice-tab">
      <span
        v-for="item in status"
        :key="item.value"
        :class="{ 'refund-tab--active': tab === item.value }"
        @click="changeTab(item.value)"
        >{{ item.label }}</span
      >
    </div>
    <div style="margin-top: 20px; width: 100%">
      <tg-search
        ref="search"
        :searchTitle.sync="search_title"
        :form.sync="searchForm"
        :showNum="3"
        @reset="reset"
        @search="searchVal"
      ></tg-search>
    </div>
    <div class="tg-table__box">
      <div class="tg-box--border"></div>
      <el-table
        ref="table"
        class="tg-table"
        tooltip-effect="dark"
        :data="list"
        :key="tab"
        v-loading="tableInfo.loading"
        border
        :cell-style="{ borderRightColor: '#e0e6ed75' }"
        :header-cell-style="{ borderRightColor: '#e0e6ed75' }"
      >
        <el-table-column type="index" label="序号" width="80">
          <template slot-scope="scope">
            <span>{{
              (tableInfo.page - 1) * tableInfo.page_size + scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="tab !== 3 && tab !== 4"
          prop="id"
          show-overflow-tooltip
          label="发票编号"
        >
        </el-table-column>
        <el-table-column prop="receipt_id" width="120" label="收据号">
          <template slot-scope="scope">
            <div>
              <div v-if="scope.row.receipt_id.length === 1" class="copy_name">
                <el-button
                  @click="openChargeReceiptDetail(scope.row.receipt_id[0])"
                  class="tg-table__name--ellipsis tg-text--blue"
                  type="text"
                  >{{ scope.row.receipt_id[0] }}</el-button
                >
                <div v-copy="scope.row.receipt_id[0]"></div>
              </div>
              <div v-else>
                <el-popover placement="right" width="100" trigger="hover">
                  <div
                    v-for="item in scope.row.receipt_id"
                    :key="item"
                    class="copy_name"
                  >
                    <el-button
                      @click="openChargeReceiptDetail(item)"
                      class="tg-table__name--ellipsis tg-text--blue"
                      type="text"
                      >{{ item }}</el-button
                    >
                    <div v-copy="item"></div>
                  </div>
                  <el-button
                    class="tg-table__name--ellipsis tg-text--blue"
                    type="text"
                    slot="reference"
                    >{{ scope.row.receipt_id.join("、") }}</el-button
                  >
                </el-popover>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="apply_man" width="" label="申请人">
        </el-table-column>
        <el-table-column
          v-if="tab !== 3 && tab !== 4"
          width=""
          prop="apply_price"
          label="开票金额"
        >
          <template slot-scope="scope">
            {{ (scope.row.apply_price / 100).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          prop="apply_time"
          label="申请时间"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.apply_time).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column v-if="tab === 2" prop="operator" label="开票人">
        </el-table-column>
        <el-table-column prop="review_status" label="审核状态">
          <template slot-scope="scope">
            {{ scope.row.review_status | review_status_cn }}
          </template>
        </el-table-column>

        <el-table-column
          v-if="tab === 2"
          show-overflow-tooltip
          prop="receipt_time"
          label="开票时间"
        >
          <template slot-scope="scope">
            {{ moment(scope.row.receipt_time).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="tab === 3 || tab === 4"
          prop="reason"
          show-overflow-tooltip
          label="原因"
        >
          <template slot-scope="scope">
            {{ scope.row.reason }}
          </template>
        </el-table-column>
        <el-table-column v-if="tab != 3 && tab != 4" label="操作" width="180">
          <template slot-scope="scope">
            <div v-if="tab === 1">
              <el-button
                v-if="scope.row.review_status === 'complete'"
                @click="toInvoice(scope.row)"
                type="text"
                v-has="{ m: 'invoice', o: 'agree' }"
                class="tg-text--blue tg-span__divide-line"
              >
                开票
              </el-button>
              <el-button
                v-if="scope.row.review_status === 'complete'"
                @click="openRefuseDialog(scope.row, 'refuse')"
                type="text"
                v-has="{ m: 'invoice', o: 'refuse' }"
                class="tg-text--blue tg-span__divide-line"
              >
                拒绝
              </el-button>
            </div>
            <div v-if="tab === 2">
              <el-button
                v-has="{ m: 'invoice', o: 'info' }"
                @click="toDetail(scope.row)"
                type="text"
                class="tg-text--blue tg-span__divide-line"
              >
                查看
              </el-button>
              <el-button
                @click="openRefuseDialog(scope.row, 'discard')"
                type="text"
                v-has="{ m: 'invoice', o: 'discard' }"
                class="tg-text--blue tg-span__divide-line"
              >
                作废
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="tg-pagination">
        <span class="el-pagination__total">共 {{ tableInfo.total }} 条</span>
        <el-pagination
          background
          layout="sizes,prev,pager,next,jumper"
          :total="tableInfo.total"
          :page-size="tableInfo.page_size"
          :current-page="tableInfo.page"
          @current-change="currentChange"
          @size-change="sizeChange"
          :page-sizes="[10, 20, 50, 100]"
        >
        </el-pagination>
      </div>
    </div>
    <Edit
      v-if="edit_visible"
      @close="editDialogClose"
      :pageType="pageType"
      :invoice_id="invoice_id"
      :price="invoice_price"
    ></Edit>
    <Refuse
      v-if="refuse_visible"
      @close="refuseDialogClose"
      :invoice_id="invoice_id"
      :type="refuse_type"
    ></Refuse>
    <receipt-detail
      :readonly="true"
      v-if="receipt_detail_visible"
      :receiptNo="receipt_no"
      :receiptType="receipt_type"
      @close="receipt_detail_visible = false"
    ></receipt-detail>
  </div>
</template>

<script>
import invoiceApi from "@/api/invoice";
import ReceiptDetail from "@/views/receiptManagement/components/detail.vue"; // 收费收据详情
import Edit from "./edit.vue";
import Refuse from "./refuse.vue";
import { BigNumber } from "bignumber.js";
import { workflow_status } from "@/public/dict";

export default {
  name: "invoiceIndex",
  data() {
    return {
      tab: 1,
      list: [],
      pageType: "edit",
      receipt_detail_visible: false,
      edit_visible: false,
      refuse_visible: false,
      invoice_id: "",
      invoice_price: 0,
      receipt_type: "order_paid",
      order_id: "",
      receipt_no: "",
      search_title: [
        {
          props: "receipt_id",
          label: "收据号",
          type: "input",
          show: true,
          placeholder: "请输入收据号"
        },
        {
          props: "apply_time",
          label: "申请时间",
          type: "date",
          show: true,
          selectOptions: [],
          has_options: true
        }
      ],
      searchForm: {
        receipt_id: "",
        apply_time: [],
        operator: "",
        apply_price: "",
        price_condition: "",
        invoice_id: ""
      },
      tableInfo: {
        loading: false,
        total: 0,
        page_size: 10,
        page: 1
      },
      refuse_type: "",
      status: [
        {
          label: "待开发票",
          value: 1
        },
        {
          label: "已开发票",
          value: 2
        },
        {
          label: "开票失败",
          value: 3
        },
        {
          label: "作废开票",
          value: 4
        }
      ]
    };
  },
  components: {
    Edit,
    Refuse,
    ReceiptDetail
  },
  computed: {
    school_id() {
      return this.$store.getters.doneGetSchoolId;
    }
  },
  created() {
    this.search();
  },
  filters: {
    review_status_cn(val) {
      const obj = workflow_status.find((item) => item.id === val);
      if (obj) {
        return obj.name;
      }
      return "-";
    }
  },
  methods: {
    // 开票
    toInvoice(row) {
      this.edit_visible = true;
      this.pageType = "agree";
      this.invoice_price = row.apply_price;
      this.invoice_id = row.id;
    },
    openChargeReceiptDetail(receipt_id) {
      this.receipt_detail_visible = true;
      // this.order_id = order_id;
      this.receipt_no = receipt_id;
    },
    toDetail(row) {
      this.invoice_id = row.id;
      this.edit_visible = true;
      this.pageType = "info";
    },
    editDialogClose(val) {
      this.edit_visible = false;
      if (val === "confirm") {
        this.search();
      }
    },
    openRefuseDialog(row, type) {
      this.refuse_type = type;
      this.refuse_visible = true;
      this.invoice_id = row.id;
    },
    refuseDialogClose(val) {
      this.refuse_visible = false;
      if (val === "confirm") {
        this.search();
      }
    },
    changeTab(val) {
      this.tab = val;
      this.$refs.search.showAllSearch(false);
      if (val === 2) {
        this.search_title = [
          {
            props: "receipt_id",
            label: "收据号",
            type: "input",
            show: true,
            placeholder: "请输入收据号"
          },
          {
            props: "invoice_id",
            label: "发票编号",
            type: "input",
            show: true,
            placeholder: "请输入发票编号"
          },
          {
            props: "apply_time",
            label: "申请时间",
            type: "date",
            show: true,
            selectOptions: [],
            has_options: false
          },
          {
            props: "operator",
            label: "开票人",
            type: "mark_staff",
            is_leave: true,
            placeholder: "请输入开票人",
            show: false
          },
          {
            props: ["apply_price", "price_condition"],
            label: "开票金额",
            placeholder: "请输入开票金额",
            type: "input-with-select",
            show: false,
            selectOptions: [
              { name: "不限", id: "" },
              { name: "小于", id: "lt" },
              { name: "等于", id: "eq" },
              { name: "大于", id: "gt" }
            ]
          }
        ];
      } else {
        this.search_title = [
          {
            props: "receipt_id",
            label: "收据号",
            type: "input",
            show: true,
            placeholder: "请输入收据号"
          },
          {
            props: "apply_time",
            label: "申请时间",
            type: "date",
            show: true,
            selectOptions: [],
            has_options: false
          }
        ];
      }
      this.reset();
    },
    // 重置查询条件
    reset() {
      this.searchForm = {
        receipt_id: "",
        apply_time: [],
        operator: "",
        apply_price: "",
        price_condition: "",
        invoice_id: ""
      };
      this.tableInfo.page = 1;
      this.tableInfo.page_size = 10;
      this.search();
    },
    searchVal() {
      this.tableInfo.page = 1;
      this.search();
    },
    // 查询列表
    search() {
      const { page, page_size } = this.tableInfo;
      const { apply_time, apply_price } = this.searchForm;
      this.list = [];
      const query = {
        page,
        page_size,
        status: this.tab,
        department_id: this.school_id,
        ...this.searchForm
      };
      if (apply_time) {
        query.search_begin = apply_time[0] ?? "";
        query.search_end = apply_time[1] ?? "";
      }
      if (apply_price) {
        query.apply_price = new BigNumber(apply_price)
          .multipliedBy(100)
          .toNumber();
      }
      if (!query.operator) {
        query.operator = undefined;
      }
      this.tableInfo.loading = true;
      invoiceApi.getList(query).then((res) => {
        const { data, code, message } = res.data;
        if (code === 0) {
          this.list = data.results;
          this.tableInfo.total = data.count;
          this.tableInfo.loading = false;
        } else {
          this.list = [];
          this.tableInfo.total = 0;
          this.tableInfo.loading = false;
          this.$message.error(message);
        }
      });
    },
    // 改变页数
    currentChange(page) {
      this.tableInfo.page = page;
      this.search();
    },
    sizeChange(val) {
      this.tableInfo.page = 1;
      this.tableInfo.page_size = val;
      this.search();
    }
  }
};
</script>

<style lang="less" scoped>
#invoice-index {
  padding-top: 46px;
  height: calc(100% - 46px);
  ::v-deep .el-table td:last-child {
    border-right-color: transparent !important;
  }
  ::v-deep .el-table__header th:last-child {
    border-right-color: transparent !important;
  }
  .invoice-tab {
    position: absolute;
    top: 56px;
    // border-top: 1px solid #e0e6ed;
    left: 0;
    height: 46px;
    background: #fff;
    padding: 0 16px;
    box-shadow: 0 2px 6px 0 rgba(204, 208, 217, 0.35);
    width: 100%;
    box-sizing: border-box;
    span {
      font-family: @text-famliy_semibold;
      color: @text-color_second;
      font-size: @text-size_normal;
      display: inline-block;
      height: 44px;
      border-bottom: 2px solid transparent;
      line-height: 44px;
      cursor: pointer;
      font-weight: bold;
    }

    span + span {
      margin-left: 32px;
    }

    span.refund-tab--active {
      color: #2d80ed;
      border-color: #2d80ed;
    }
  }
}
</style>
