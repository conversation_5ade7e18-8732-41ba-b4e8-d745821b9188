import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 开班班级列表
function getSchoolServiceAuditClassroomList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/audit-classroom/list?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 开班申请详情
function getSchoolServiceAuditClassroomInfo(data) {
  return axios
    .get(
      `/api/school-service/audit-classroom/info?audit_classroom_id=${data.id}`
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 创建开班申请
function getSchoolServiceAuditClassroomCreate(data) {
  return axios
    .post(`/api/school-service/audit-classroom/create`, data)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 更新班级
function getSchoolServiceAuditClassroomUpdate(data) {
  return axios
    .patch(
      `/api/school-service/audit-classroom/update?audit_classroom_id=${data.id}`,
      data
    )
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
// 班级导出
function schoolServiceClassroomExcel(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .get(`/api/school-service/audit-classroom/export?${new_data}`, {
      params: { exportData: 1 }
    })
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}

// 批量删除班级
function schoolServiceAuditClassroomDelete(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return axios
    .delete(`/api/school-service/audit-classroom/delete?${new_data}`)
    .then((response) => {
      return response;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  // 开班班级列表
  async GetSchoolServiceAuditClassroomList(data) {
    return getSchoolServiceAuditClassroomList(data);
  },
  // 开班申请详情
  async GetSchoolServiceAuditClassroomInfo(data) {
    return getSchoolServiceAuditClassroomInfo(data);
  },

  // 创建开班申请
  async GetSchoolServiceAuditClassroomCreate(data) {
    return getSchoolServiceAuditClassroomCreate(data);
  },
  // 导出
  async SchoolServiceClassroomExcel(data) {
    return schoolServiceClassroomExcel(data);
  },
  // 更新班级
  async GetSchoolServiceAuditClassroomUpdate(data) {
    return getSchoolServiceAuditClassroomUpdate(data);
  },
  // 删除班级
  async schoolServiceAuditClassroomDelete(data) {
    return schoolServiceAuditClassroomDelete(data);
  }
};
