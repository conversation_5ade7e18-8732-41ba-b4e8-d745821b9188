import qs from "qs";

const fetch = require("../fetch");
// 有效期选项
function getExpirationDateList(data) {
  return fetch.fetchGet("/api/course-service/charge/expire-strategy", {
    params: data
  });
}
// 收费类型info
function getFeeTypeInfo(data) {
  return fetch.fetchGet("/api/order-service/admin/fee-type/info", {
    params: data
  });
}
// 班级关联课程商品列表
function getClassRelated(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    "/api/course-service/settle/class-product-list?" + new_data
  );
}

// 收费类型保存
function courseChargeCreate(data) {
  return fetch.fetchPost("/api/order-service/admin/fee-type/create", data);
}

// 收费类型更新
function courseChargeUpdate(data) {
  return fetch.fetchPost(
    "/api/order-service/admin/fee-type/save",
    data,
    "数据更新成功!"
  );
}

// 收费类型授权校区查看
export function chargeListCampus(data) {
  return fetch.fetchGet(
    `/api/enterprise/order/charge/opt/${data.businessType}/listCampus`,
    {
      params: data
    }
  );
}

// 某个课程的收费类型
export function getChargeList(data) {
  return fetch.fetchGet(`/api/order-service/admin/fee-type/list`, {
    params: data
  });
}
// 后台计算收费-列表新增时
export function charge_calculate(data) {
  // return fetch.fetchPost(`/api/enterprise/order/pre/calculate`, data, "");
  return fetch.fetchPost(`/api/course-service/charge/pre/calculate`, data, "");
}
// 后结算页面的计算
export function settlement_calculate(data) {
  // return fetch.fetchPost(`/api/enterprise/order/calculate`, data, "");
  return fetch.fetchPost(
    `/api/course-service/charge/final/calculate`,
    data,
    ""
  );
}
// 后台计算收费-列表新增时
export function charge_pre_check(data) {
  return fetch.fetchPost(`/api/course-service/charge/pre/check`, data, "");
}
// 收费类型配置列表折扣方案
export function ddpList(data) {
  return fetch.fetchGet(`/api/enterprise/order/ddp/list`, {
    params: data
  });
}
// 收费类型配置列表折扣方案
export function feeTypeAutoMatch(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/fee-type/auto-match?${new_data}`
  );
}
// 收费类型配置列表-授权校区保存
export function saveCampus(data) {
  return fetch.fetchPost(
    `/api/enterprise/order/charge/opt/${data.businessType}/saveCampus`,
    data,
    "保存成功！"
  );
}
// 收费类型配置列表-授权校区保存
export function saveDepartment(data) {
  return fetch.fetchPost(
    `/api/order-service/admin/fee-type/save-department-ids`,
    data
  );
}

// 结算页面--检查是否有相同订单
export function orderCheck(data) {
  // return fetch.fetchPost(`/api/enterprise/order/save/frontend`, data, "");
  return fetch.fetchPost(
    `/api/order-service/admin/order/repeat-order-check`,
    data,
    ""
  );
}

// 结算页面--保存
export function chargeSaveOrder(data) {
  // return fetch.fetchPost(`/api/enterprise/order/save/frontend`, data, "");
  return fetch.fetchPost(`/api/order-service/admin/order/create`, data, "");
}
// 结算页面--收费
export function chargeSettConfirm(data) {
  return fetch.fetchPost(`/api/enterprise/order/create/frontend`, data, "");
}
// 收费类型配置列表付款方式
export function paymentMethod(data) {
  return fetch.fetchGet(`/api/order-service/admin/pay/method`, {
    params: data
  });
}
// 待收费的结算单
export function settExists(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(`/api/order-service/admin/order/list?${new_data}`);
}
// 结算页面的其他信息
export function invoiceInfo(data) {
  return fetch.fetchGet(`/api/enterprise/order/invoice/opt/list`, {
    params: data
  });
}
// 结算页面的收款方式信息
export function paymentInfo(data) {
  return fetch.fetchGet(`/api/enterprise/order/payment/opt/list`, {
    params: data
  });
}
// 获取订单的优惠券信息
export function getOrderCoupons(data) {
  return fetch.fetchGet(`/api/enterprise/order/discount/coupons/order`, {
    params: data
  });
}
// 预存订金-info
export function prestorePrice(data) {
  return fetch.fetchGet(`/api/enterprise/wallet/balance/info`, {
    params: data
  });
}

// 获取优惠券列表信息
export function getCouponList(data) {
  return fetch.fetchGet(`/api/enterprise/order/discount/coupons/list`, {
    params: data
  });
}

// 获取租赁订单列表
export function getLeaseOrderList(data) {
  const new_data = qs.stringify(data, { arrayFormat: "repeat" });
  return fetch.fetchGet(
    `/api/order-service/admin/wallet/to-allocation-list?${new_data}`
  );
}

export default {
  async getExpirationDateList(data) {
    return getExpirationDateList(data);
  },
  async getClassRelated(data) {
    return getClassRelated(data);
  },

  courseChargeCreate,
  courseChargeUpdate,
  chargeListCampus,
  getChargeList,
  charge_calculate,
  settlement_calculate,
  ddpList,
  feeTypeAutoMatch,
  saveCampus,
  saveDepartment,
  getFeeTypeInfo,
  paymentMethod,
  async orderCheck(data) {
    return orderCheck(data);
  },
  chargeSaveOrder,
  chargeSettConfirm,
  settExists,
  invoiceInfo,
  paymentInfo,
  getCouponList,
  getOrderCoupons,
  prestorePrice,
  charge_pre_check,
  getLeaseOrderList
};
