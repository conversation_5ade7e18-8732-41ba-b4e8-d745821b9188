<!--结算中心-->
<template>
  <div>
    <el-dialog
      :visible="true"
      title="结算中心"
      width="1100px"
      :before-close="back"
      class="settlement-class"
      top="60px"
    >
      <!-- 是网校并且购买的有课程 -->
      <div v-if="is_period" slot="title">
        <div class="tg-dialog__header">
          <div class="tg-dialog__header-title">
            <span class="title">结算中心</span>
            <span class="tg-dialog__qichi">
              <span>期次：</span>
              <el-select
                clearable
                filterable
                :popper-append-to-body="false"
                v-model="period"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in period_options"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </span>
          </div>
        </div>
      </div>

      <div class="tg-dialog__content">
        <div v-if="studentInfo" class="stu-info">
          <div class="s-title">学员信息</div>
          <div class="info">
            <span><em>学员姓名:</em>{{ studentInfo.student_name }}</span>
            <span><em>学号:</em>{{ studentInfo.student_number }}</span>
            <span><em>所属校区:</em>{{ studentInfo.department_name }}</span>
            <span><em>手机号:</em>{{ studentInfo.mobile }}</span>
            <span style="width: 200px" class="tg_ellipsis"
              ><em>学生类别:</em
              ><el-tooltip
                effect="light"
                :content="
                  studentInfo.student_category_ch
                    ? studentInfo.student_category_ch.join()
                    : ''
                "
                placement="top-start"
              >
                <span>
                  {{
                    studentInfo.student_category_ch
                      ? studentInfo.student_category_ch.join()
                      : ""
                  }}
                </span>
              </el-tooltip></span
            >
            <span
              ><em>可用钱包余额:</em>
              <span v-if="studentInfo">
                {{
                  (studentInfo.left_cash + studentInfo.left_prepay).toFixed(2)
                }}
              </span>
            </span>
          </div>
        </div>
        <div class="divider"></div>
        <div class="tg-table__box">
          <div class="tg-box--border"></div>
          <el-table
            ref="table"
            :data="table_list"
            tooltip-effect="dark"
            class="tg-table"
            row-key="id"
            highlight-current-row
            :tree-props="{ children: 'children' }"
            style="width: 100%"
          >
            <el-table-column
              class-name="product-name"
              width="140"
              fixed
              label="产品名称"
              prop="name"
            >
              <template slot-scope="scope">
                <div class="row__box--flex">
                  <img
                    :src="
                      scope.row.add_type == 'course'
                        ? require('@/assets/图片/icon_charge_course.png')
                        : scope.row.add_type == 'article'
                        ? require('@/assets/图片/icon_charge_goods.png')
                        : scope.row.add_type == 'match'
                        ? require('@/assets/图片/icon_charge_match.png')
                        : ''
                    "
                    class="mark"
                  />
                  <span>{{ scope.row[scope.column.property] }}</span>
                </div>
                <div v-if="scope.row.class_id">
                  <img
                    :src="require('@/assets/图片/icon_charge_class.png')"
                    class="mark"
                  />
                  <span> {{ scope.row.class_name }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="单价" prop="standard_price" width="80">
              <template slot-scope="scope">
                {{ scope.row.standard_price }}
              </template>
            </el-table-column>
            <el-table-column
              label="数量"
              prop="num"
              width="50"
            ></el-table-column>
            <el-table-column label="应交金额" width="80">
              <template slot-scope="scope">
                {{ scope.row.originalTotalPrice }}
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip label="折扣方案" width="80">
              <template slot-scope="scope">
                {{ discount_label(scope.row.discount, scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="折后单价" prop="discount_price" width="80">
              <template slot-scope="scope">
                {{ Number(scope.row.discount_price).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="折后金额" width="80" prop="total_price">
              <template slot-scope="scope">
                {{ Number(scope.row.total_price).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column
              label="赠送数量"
              width="80"
              prop="present_num"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column label="直减分摊" prop="apportion" width="80">
              <template slot-scope="scope">
                <!-- <el-checkbox
                  :true-label="1"
                  :false-label="2"
                  @change="getCalcTableData"
                  v-model="scope.row.apportion"
                ></el-checkbox> -->
                <el-checkbox
                  :true-label="1"
                  :false-label="2"
                  v-model="scope.row.apportion"
                  @change="getCalcTableData"
                  v-if="scope.row.add_type == 'course'"
                ></el-checkbox>
                <el-checkbox
                  v-else
                  :true-label="1"
                  :false-label="2"
                  v-model="scope.row.apportion"
                  disabled
                ></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="分摊金额" width="80">
              <template slot-scope="scope">
                {{ scope.row.total_apportion }}
                <!-- <span>0.00</span> -->
              </template>
            </el-table-column>
            <el-table-column label="应收金额" prop="dueCollectPrice" width="80">
              <template slot-scope="scope">
                {{ scope.row.dueCollectPrice }}
              </template>
            </el-table-column>
            <el-table-column
              width="136"
              fixed="right"
              label="收费类型"
              prop="charge_type"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.add_type == 'article'">物品</span>
                <span v-else-if="scope.row.add_type == 'match'">赛事</span>
                <el-select
                  v-else
                  v-model="scope.row.charge_type"
                  placeholder="请选择"
                  @change="handleChargeType"
                  class="charge-type-list-select"
                >
                  <el-option
                    v-for="item in disableChargeBnt
                      ? ChargeCategory
                      : scope.row.charge_type_list"
                    :key="item.category_id"
                    :label="item.category_name"
                    :value="item.category_id"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
          </el-table>
          <div class="tg-table-bottom">
            <span
              >课程金额<em>￥{{ table_statistics.course.toFixed(2) }}</em></span
            >
            <span
              >物品金额<em
                >￥{{ table_statistics.article.toFixed(2) }}</em
              ></span
            >
            <span
              >赛事金额<em>￥{{ table_statistics.match.toFixed(2) }}</em></span
            >
            <span
              >合计金额<em>￥{{ table_statistics.total.toFixed(2) }}</em></span
            >
          </div>
        </div>
        <div class="section-middle">
          <div class="s-left">
            <div class="you-hui-box">
              <div class="s-title">优惠信息</div>
              <div class="s-content">
                <div class="you-hui-row">
                  <span>
                    <el-checkbox
                      :disabled="hold_state"
                      @change="getCalcTableData"
                      v-model="useDirectDiscount_is_check"
                      >直减优惠</el-checkbox
                    >
                  </span>
                  <span class="directBox">
                    <el-input
                      v-model.trim="useDirectDiscount"
                      placeholder="请输入"
                      :readonly="only_choose_direct || hold_state"
                      @input="changeDirectNum"
                      @blur="useDirectDiscountBlur"
                      @focus="keepHoldState"
                    >
                      <span
                        v-if="!only_manual"
                        class="searchBtn"
                        slot="suffix"
                        @click="showDirectPro"
                      >
                        <img
                          src="@/assets/图片/icon_more.png"
                          alt=""
                          class="more"
                        />
                      </span>
                    </el-input>
                  </span>
                </div>
                <div class="you-hui-row">
                  <span>
                    <el-checkbox
                      :disabled="hold_state"
                      @change="getCalcTableData"
                      v-model="is_check_coup"
                      >使用优惠券</el-checkbox
                    >
                  </span>
                  <span style="display: flex; align-items: center">
                    <div @click="showCoupon" class="custom--select">
                      <el-input
                        v-model.number="useCoupons"
                        readonly
                        placeholder="请选择优惠券"
                      >
                        <img
                          src="@/assets/图片/icon_more.png"
                          slot="suffix"
                          alt=""
                          class="more"
                        />
                      </el-input>
                    </div>
                    <span style="margin-left: 16px"
                      >已选择
                      <span style="color: #2d80ed; font-weight: bold">{{
                        useCouponsParam.length
                      }}</span>
                      张优惠券</span
                    >
                  </span>
                </div>
              </div>
            </div>
            <div class="di-kou-content">
              <div class="s-title">余额抵扣</div>
              <div class="s-content">
                <div class="you-hui-row">
                  <span>
                    <el-checkbox
                      :disabled="hold_state"
                      @change="getCalcTableData"
                      v-model="useDeposit_is_check"
                      >订金余额</el-checkbox
                    >
                  </span>
                  <span>
                    <inputNumber
                      :controls="false"
                      @blur="useDepositBlur"
                      :max="studentInfo.left_prepay"
                      :min="0"
                      :precision="2"
                      :readonly="hold_state"
                      @focus="keepHoldState"
                      v-model.number="useDeposit"
                      placeholder="请输入"
                    >
                    </inputNumber>
                  </span>
                  <span
                    >可用余额：<span style="color: #ff0517"
                      >￥{{
                        studentInfo && formatAmount(studentInfo.left_prepay)
                      }}</span
                    ></span
                  >
                </div>
                <div class="you-hui-row">
                  <span>
                    <el-checkbox
                      :disabled="hold_state"
                      @change="getCalcTableData"
                      v-model="useWallet_is_check"
                      >零钱余额</el-checkbox
                    >
                  </span>
                  <span>
                    <inputNumber
                      :controls="false"
                      @blur="useWalletBlur"
                      :max="formatAmount(studentInfo.left_cash)"
                      :min="0"
                      :precision="2"
                      v-model.number="useWallet"
                      placeholder="请输入"
                      :readonly="hold_state"
                      @focus="keepHoldState"
                    >
                    </inputNumber>
                  </span>
                  <span
                    >可用余额：<span style="color: #ff0517"
                      >￥{{
                        studentInfo && formatAmount(studentInfo.left_cash)
                      }}</span
                    >
                    <el-tooltip
                      popper-class="tg-tooltip"
                      placement="bottom"
                      effect="light"
                    >
                      <span slot="content">
                        实际余额:
                        {{
                          studentInfo && formatAmount(studentInfo.left_cash, 3)
                        }}
                      </span>
                      <img
                        src="../../assets/图片/icon_question.png"
                        alt=""
                        style="
                          width: 12px;
                          margin-left: 5px;
                          vertical-align: middle;
                        "
                      />
                    </el-tooltip>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="settlement-amount-box">
            <div class="s-title">结算金额</div>
            <div class="s-content">
              <div class="amount-row">
                <span>应交金额</span>
                <span
                  >￥{{ sette_amount.orderOriginTotalPrice.toFixed(2) }}</span
                >
              </div>
              <div class="amount-row">
                <span>折后金额</span>
                <span
                  >￥{{
                    sette_amount.orderAfterDiscountTotalPrice.toFixed(2)
                  }}</span
                >
              </div>
              <div class="amount-row">
                <span>直减冲抵</span>
                <span style="color: #ff0517"
                  >-￥{{
                    Number(sette_amount.useDirectDiscount).toFixed(2)
                  }}</span
                >
              </div>
              <div class="amount-row">
                <span>优惠券冲抵</span>
                <span style="color: #ff0517"
                  >-￥{{ Number(useCoupons).toFixed(2) }}</span
                >
              </div>
              <div class="amount-row">
                <span>订金冲抵</span>
                <span style="color: #ff0517"
                  >-￥{{ useDeposit.toFixed(2) }}</span
                >
              </div>
              <div class="amount-row">
                <span>零钱冲抵</span>
                <span style="color: #ff0517"
                  >-￥{{ useWallet.toFixed(2) }}</span
                >
              </div>
              <div class="amount-row">
                <span>实交金额</span>
                <span style="padding-left: 15px">
                  ￥{{ sette_amount.orderDueCollectPrice.toFixed(2) }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="divider"></div>
        <div class="charge-box">
          <div class="s-title">收款方式</div>
          <div class="s-content">
            <span class="diff-amount">
              <img src="@/assets/图片/icon_permission.png" />
              还差金额：<span style="color: #ff0517"
                >￥{{ surplus_amount }}</span
              ></span
            >
            <div class="pay-way-button-list">
              <div
                class="pay-way-button"
                v-for="(item, index) in payWayOptions"
                @click="selectPayWay(item)"
                :class="{ 'payway-button-selected': item.checked }"
                :key="index"
              >
                <div class="tag_bg" :style="`background:${item.color};`"></div>
                <span>{{ item.value }}</span>
                <span v-if="item.checked" class="tag_line">|</span>
                <i
                  v-if="item.checked"
                  class="el-icon-close"
                  @click="delPayWay($event, item)"
                ></i>
              </div>
            </div>
            <div class="payway-list">
              <div
                v-for="(item, index) in payment"
                :key="index"
                class="payway-row"
              >
                <span>{{ item.value }}</span>
                <span>
                  <div class="custom--select">
                    <el-input
                      v-model.trim="item.pay_price"
                      @blur="payWayBlur(item)"
                      placeholder="请输入"
                      :readonly="hold_state"
                      @focus="keepHoldState"
                      ><img
                        src="@/assets/图片/icon_del_ac.png"
                        slot="suffix"
                        alt=""
                        class="del-suffix"
                        @click="removePayway(item)"
                    /></el-input>
                  </div>
                </span>
                <!--如果是银行汇款方式-->
                <template v-if="item.pay_method == 'transfer'">
                  <span style="margin-right: 16px">付款银行账号</span>
                  <span
                    ><el-input
                      v-model="item.pay_account"
                      placeholder="付款银行账号"
                    ></el-input
                  ></span>
                </template>
                <!--如果是POS方式-->
                <template v-if="item.pay_method === 'POS'">
                  <el-radio-group
                    style="margin-left: 16px"
                    v-model="item.pos_channel"
                  >
                    <el-radio label="ScanQrCode">扫码</el-radio>
                    <el-radio label="SwipeCard">刷卡</el-radio>
                  </el-radio-group>
                </template>
                <!-- 如果是店铺付款 -->
                <template v-if="item.is_shop">
                  <span style="margin-right: 16px"
                    ><em style="color: #ff0517">*</em>第三方订单号</span
                  >
                  <span
                    ><el-input
                      style="width: 200px"
                      v-model="item.third_order_id"
                      placeholder="请输入第三方订单号（必填）"
                    ></el-input
                  ></span>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div class="divider"></div>
        <div class="extra-box">
          <div class="s-title">其他信息</div>
          <div class="s-content">
            <div style="align-items: baseline" class="you-hui-row">
              <span
                :class="[isPerformanceUserRequired ? 'tg-label--required' : '']"
              >
                业绩归属人
              </span>
              <div class="user-table">
                <div class="s-title">
                  <label style="width: 45%; padding-left: 50px"
                    >业绩归属人</label
                  >
                  <label style="width: 35%">销售角色</label>
                  <label style="width: 20%; text-align: center">操作</label>
                </div>
                <div class="s-content">
                  <div
                    v-for="(item, index) in invoice.performanceUser"
                    :key="index"
                    :class="['row', item.is_leave ? 'danger' : '']"
                  >
                    <div style="width: 45%">
                      <div class="custom--select">
                        <course-staff
                          :check_id.sync="item.userId"
                          :check_name.sync="item.userName"
                          staff_placeholder="请选择业绩归属人"
                        ></course-staff>
                      </div>
                    </div>
                    <div style="width: 35%">
                      <el-select
                        @change="
                          (val) => {
                            performanceUserChange(val, item);
                          }
                        "
                        :popper-append-to-body="false"
                        v-model="item.userRole"
                        placeholder="请选择销售角色"
                      >
                        <el-option
                          v-for="item in role_options"
                          :key="item.value"
                          :label="item.name"
                          :value="item.id"
                          :disabled="item.disabled"
                        >
                        </el-option>
                      </el-select>
                    </div>
                    <div
                      @click="delRow(index, item)"
                      style="width: 20%; text-align: center"
                    >
                      <img
                        src="@/assets/图片/icon_del_ac.png"
                        class="del-suffix"
                      />
                    </div>
                  </div>
                  <em @click="createRow" class="add-icon">
                    <img src="@/assets/图片/icon_add.png" class="del-suffix" />
                    添加业绩归属人</em
                  >
                </div>
              </div>
            </div>
            <div class="you-hui-row">
              <span>上传图片</span>
              <div>
                <el-upload
                  action="#"
                  list-type="picture-card"
                  accept="image/png, image/jpeg"
                  :file-list="fileList"
                  :limit="3"
                  ref="uploadImgs"
                  :before-upload="beforeUpload"
                  :http-request="uploadImg"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div style="height: 100%" slot="file" slot-scope="{ file }">
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      alt=""
                    />
                    <span class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file)"
                      >
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleRemove(file)"
                      >
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
                <el-dialog
                  title="预览"
                  :append-to-body="true"
                  :visible.sync="dialogVisible"
                >
                  <img width="100%" :src="dialogImageUrl" alt="" />
                </el-dialog>
              </div>
            </div>
            <div style="align-items: flex-start" class="you-hui-row">
              <span class="tg-label--required" style="margin-top: 16px"
                >内部备注</span
              >
              <span style="flex: 1"
                ><el-input
                  style="width: 100%; height: 88px"
                  v-model="invoice.innerRemark"
                  type="textarea"
                  placeholder="备注"
                  maxlength="800"
                ></el-input
              ></span>
            </div>
            <div style="align-items: flex-start" class="you-hui-row">
              <span style="margin-top: 16px">外部备注</span>
              <span style="flex: 1"
                ><el-input
                  maxlength="800"
                  style="width: 100%"
                  v-model="invoice.outerRemark"
                  type="textarea"
                  placeholder="备注"
                ></el-input
              ></span>
            </div>
            <div
              style="align-items: baseline"
              class="you-hui-row"
              v-if="is_payWay"
            >
              <span class="tg-label--required" style="margin-top: 16px"
                >发货方式</span
              >
              <span style="flex: 1">
                <el-select
                  v-model="invoice.delivery_method"
                  placeholder="请选择发货方式"
                >
                  <el-option label="到店领取" value="arrive"></el-option>
                  <el-option label="快递" value="jd"></el-option>
                </el-select>
              </span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-cls-footer">
        <div class="order-push">
          <!-- <el-checkbox :true-label="1" :false-label="2" v-model="is_push"
            >将订单推送给家长</el-checkbox
          > -->
        </div>
        <div>
          <el-button
            v-if="page_from == 'order_list'"
            class="tg-button--plain"
            type="plain"
            @click="back"
            >取消</el-button
          >
          <el-button
            v-else-if="page_from == 'charge_index' && hold_state"
            class="tg-button--plain"
            type="plain"
            @click.native="cancelOrder"
            >取消订单</el-button
          >
          <el-button
            v-show="page_from == 'charge_index' && !hold_state"
            class="tg-button--plain"
            type="plain"
            v-throttle="saveOrder"
            >保存订单</el-button
          >

          <el-button
            class="tg-button--primary"
            type="primary"
            :disabled="disableChargeBnt"
            @click="chargeConfirmTip"
            >收费</el-button
          >
        </div>
      </span>
      <lease-ares-dialog
        :tableData="leaseAresDialogTableDatad"
        v-if="leaseAresDialogTableDatad"
        @close="handclose"
        :dialogVisible="leaseAresDialogVisibled"
        @handleConfirm="handleConfirm"
        :button_show="false"
      ></lease-ares-dialog>
    </el-dialog>
    <!-- 优惠券 -->
    <Coupon
      ref="coupon"
      @confirm="couponConfirm"
      @init="getCouponList"
      @close="closeCoupon"
      @chooseOne="couponChoose"
      :loading="coupon_loading"
      :list="couponList"
      v-if="coupon_visible"
      :chooseIds.sync="couponIds"
    ></Coupon>
    <direct-pro ref="directPro" @confirm="saveDirect"></direct-pro>

    <el-dialog
      v-if="charge_really_visible"
      :visible="true"
      title="收费确认"
      width="460px"
      :before-close="
        () => {
          charge_really_visible = false;
        }
      "
    >
      <div class="charge-price-class">
        <el-row>
          <el-col :span="19">应交金额：</el-col>
          <el-col :span="5"
            >￥{{ sette_amount.orderOriginTotalPrice.toFixed(2) }}</el-col
          >
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="19">折后金额：</el-col>
          <el-col :span="5"
            >￥{{
              sette_amount.orderAfterDiscountTotalPrice.toFixed(2)
            }}</el-col
          >
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="19">直减冲抵：</el-col>
          <el-col :span="5"
            >-￥{{ Number(sette_amount.useDirectDiscount).toFixed(2) }}</el-col
          >
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="19">优惠券冲抵：</el-col>
          <el-col :span="5">-￥{{ Number(useCoupons).toFixed(2) }}</el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="19">订金冲抵：</el-col>
          <el-col :span="5">-￥{{ useDeposit.toFixed(2) }}</el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="19">零钱包冲抵：</el-col>
          <el-col :span="5">-￥{{ useWallet.toFixed(2) }}</el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="19">实交金额：</el-col>
          <el-col :span="5"
            >￥{{ sette_amount.orderDueCollectPrice.toFixed(2) }}</el-col
          >
        </el-row>
        <el-divider></el-divider>
        <span slot="footer" class="dialog-cls-footer" style="margin-top: 40px">
          <div class="check-list"></div>
          <div>
            <el-button
              class="tg-button--plain"
              type="plain"
              @click="charge_really_visible = false"
              >取消</el-button
            >
            <el-button
              class="tg-button--primary"
              type="primary"
              v-throttle="chargeConfirm"
              :loading="chargeBtnloading"
              >确定</el-button
            >
          </div>
        </span>
      </div>
    </el-dialog>
    <el-dialog
      v-if="qrcode_pay_visible"
      :visible="true"
      title="收款"
      width="460px"
      :before-close="() => ((hold_state = true), (qrcode_pay_visible = false))"
      class="qrcode_pay_dialog"
    >
      <div class="time-count">
        <span v-if="pay_status == 'unpaid'">支付中...</span>
        <span v-if="pay_status == 'paid'" class="tg-text--green">支付完成</span>
        <span v-if="pay_status == 'failed'" class="tg-text--red">支付失败</span>
        <!-- <span>温馨提示：请在20分钟内完成支付</span> -->
        <!-- <count-timer
          v-slot="timeObj"
          class="counter"
          :time="20 * 60 * 1000"
          @ended="counterEnd()"
        >
          {{ timeObj.mm }}:{{ timeObj.ss }}
        </count-timer> -->
      </div>
      <div v-if="qrUrl" class="settlement-qr-code">
        <VueQr
          draggable="false"
          :correctLevel="3"
          :dotScale="1"
          :margin="0"
          :size="336"
          :text="qrUrl"
        />
      </div>
      <div v-if="deadline_tip" class="deadline-tip">{{ deadline_tip }}</div>
    </el-dialog>
  </div>
</template>
<script>
import postManagementApi from "@/api/postManagement";
import chargeApi from "@/api/charge";
import couponApi from "@/api/discountCoupon";
import { mapState } from "vuex";
import { v4 as uuidv4 } from "uuid";
import timeFormat from "@/public/timeFormat"; // 日期转化
import VueQr from "vue-qr";
import discountApi from "@/api/discount";
import eBaoPay from "@/api/eBaoPay";
import orderApi from "@/api/order";
import periodApi from "@/api/period";
import inputNumber from "./inputNumber.vue";
import leaseAresDialog from "./leaseAresDialog.vue";
export default {
  name: "settlement",
  data() {
    return {
      survey_visible: false, // 是否有推送问卷权限
      coupon_loading: false,
      chargeBtnloading: false,
      inputState: false, // 手动输入为false 使用直减方案为true
      manualId: "", // 手动输入的折扣方案id
      only_choose_direct: true, // 只能选择直减
      only_manual: true, // 只能手输

      is_push: 1,
      qrcode_pay_visible: false,
      qrUrl: "",
      is_check_coup: true,
      useDirectDiscount: 0,
      useCoupons: 0,
      useDirectDiscount_is_check: true,
      useWallet_is_check: true,
      useDeposit_is_check: true,
      useWallet: 0,
      useDeposit: 0,
      memo: "",
      studentInfo: {
        // stu_balance: 0,
        // stu_prepay: 0,
        left_prepay: 0,
        left_cash: 0
      },
      table_list: [],
      table_statistics: {
        course: 0,
        article: 0,
        match: 0,
        total: 0
      },
      sette_amount: {
        orderAfterDiscountTotalPrice: 0,
        orderDueCollectPrice: 0,
        orderOriginTotalPrice: 0,
        useDirectDiscount: 0
      },

      dialogImageUrl: "",
      dialogVisible: false,
      coupon_visible: false,
      role_options: [],
      payWayOptions: [],
      payment: [],
      fileList: [],
      // coupon_discount: 0,
      charge_date: "",
      charge_type_list: [],
      autoFill: [],
      manualFill: [],
      isPerformanceUserRequired: false,
      // 其他信息相关
      invoice: {
        certs: [],
        innerRemark: "",
        outerRemark: "",
        performanceUser: [],
        delivery_method: ""
      },
      couponIds: [],
      aliy_img_url: [],
      couponList: [],
      useCouponsParam: [],
      charge_really_visible: false,
      pay_status: "unpaid",
      payResultTimer: null,
      disableChargeBnt: false,
      deadline_tip: "",
      hold_state: false,
      hold_state_text:
        "该订单已同步第三方支付平台，如要修改金额请取消订单重新收费",
      period_options: [],
      period: "",
      is_payWay: false,
      leaseAresDialogVisible: false,
      leaseAresDialogTableData: [
        {
          id: 1,
          name: "租赁订单1",
          price: 100,
          status: "已发货"
        },
        {
          id: 2,
          name: "租赁订单2",
          price: 200,
          status: "未发货"
        },
        {
          id: 3,
          name: "租赁订单3",
          price: 300,
          status: "已发货"
        }
      ],
      leaseAresDialogVisibled: false,
      leaseAresDialogTableDatad: []
    };
  },
  props: {
    order_id: {
      type: String,
      default: ""
    },
    page_from: {
      type: String,
      default: ""
    },
    // 是否处理租赁逻辑展示发货方式 防止其他页面通用影响
    is_lease_package: {
      type: Boolean,
      default: false
    },
    // 需要分摊的数据
    to_allocation_ids: {
      type: Array,
      default: () => []
    }
  },
  components: {
    VueQr,
    inputNumber,
    leaseAresDialog
  },
  directives: {
    // chargeOptionHover: {
    //   bind(el) {
    //     function aa() {
    //       return "hhkkashkak17278781";
    //     }
    //     el.addEventListener("mouseover", () => {
    //       let clientRect = el.getBoundingClientRect();
    //       let div = document.createElement("div");
    //       div.style.position = "fixed";
    //       div.style.top = clientRect.top + 40 + "px";
    //       div.style.left = clientRect.left + "px";
    //       div.style.zIndex = 9999;
    //       div.id = "chargeOptionHover";
    //       div.className = "balloon-top";
    //       div.innerHTML = aa();
    //       document.body.appendChild(div);
    //     });
    //     el.addEventListener("mouseout", function() {
    //       setTimeout(function() {
    //         let el = document.getElementById("chargeOptionHover");
    //         if (el) {
    //           document.body.removeChild(el);
    //         }
    //       }, 150);
    //     });
    //   },
    // },
  },
  created() {
    this.Oss.getAliyun();
    this.getPaymentMethod();
    this.getPosList();
  },
  mounted() {
    // 收费页面
    if (this.page_from === "charge_index") {
      this.charge_date = this.$parent.charge_date;
    }
    if (this.order_id) {
      this.getOrderInfo();
    } else {
      const { list, stu_info } = this.$parent;
      console.log(this.$parent);
      this.studentInfo = stu_info;
      console.log(stu_info, "stu_info");
      this.table_list = list;
      if (
        this.is_lease_package &&
        list[0].add_type === "article" &&
        (list[0].sales_method === "lease" || list[0].sales_method === 2)
      ) {
        // 发货方式选项
        this.is_payWay = true;
      }
      if (list.length) {
        this.handleChargeType();
      }
      this.getDirectSubtractionData();
      if (this.studentInfo.department_name !== "聂卫平围棋网校-新") {
        this.$store.dispatch("getChargeCategory", { channel: "all" });
      }
      // this.getCalcTableData();
      if (this.is_period) {
        this.getPeriodList();
      }
    }
  },
  computed: {
    ...mapState({
      ChargeCategory: (state) => state.dictionary.ChargeCategory
    }),

    isNetworkSchool() {
      return ["网校测试校区C", "聂卫平围棋网校-新"].includes(
        this.studentInfo.department_name
      );
    },
    // ----还差金额
    surplus_amount() {
      const { sette_amount, payment } = this;
      const orderDueCollectPrice = sette_amount.orderDueCollectPrice;
      let sum = 0;
      if (payment) {
        payment.map((item) => {
          if (item.pay_price) {
            sum += Number(item.pay_price);
          }
        });
      }
      return (orderDueCollectPrice - sum).toFixed(2);
    },
    // 是否显示期次
    is_period() {
      const has_course = this.table_list.some(
        (item) => item.add_type === "course"
      );
      const is_online_school =
        this.studentInfo.department_name === "聂卫平围棋网校-新";
      return has_course && is_online_school;
    }
  },
  watch: {
    fileList(val) {
      this.invoice.certs = this.fileList.map((item) => item.url);
      if (val && val.length < 3) {
        $(".el-upload--picture-card").show();
      } else {
        $(".el-upload--picture-card").hide();
      }
    },
    payment(val) {
      // 自动填充实交金额
      if (val && val.length === 1) {
        const { orderDueCollectPrice } = this.sette_amount;
        this.$set(val[0], "pay_price", orderDueCollectPrice);
      }
    },
    "studentInfo.department_name"(val) {
      console.log("department_name :>> ", val);
      if (val === "聂卫平围棋网校-新") {
        this.$store.dispatch("getChargeCategory", {
          channel: "tw"
        });
      } else {
        this.$store.dispatch("getChargeCategory", {
          channel: "offline"
        });
      }
    }
  },
  filters: {},
  beforeDestroy() {
    clearInterval(this.payResultTimer);
  },
  methods: {
    // 获取网校期次列表
    getPeriodList() {
      const course_name = [];
      this.table_list.map((item) => {
        if (item.add_type === "course") {
          course_name.push(item.name);
        }
      });
      periodApi
        .getChargeList({
          search_start: this.charge_date,
          search_end: this.charge_date,
          course_name: course_name.join(",")
        })
        .then((res) => {
          if (res.data.code === 0) {
            const arr = res.data.data || [];
            if (arr.length) {
              this.period_options = arr;
              if (!this.order_id) {
                this.period = arr[0];
              }
            }
          }
        });
    },

    // 向下取整金额保留小数点后3,默认保留小数点后2
    formatAmount(amount, num) {
      if (amount === undefined || amount === null) return "0.00";
      if (num === 3) {
        return Math.floor(amount * 1000) / 1000;
      }
      return Math.floor(amount * 100) / 100;
    },
    cancelOrder() {
      this.$confirm("此操作将取消订单，确定要进行此操作吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        orderApi
          .cancelOrder({
            order_id: this.order_id
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.resetData();
              this.back();
            } else {
              this.$message.error(res.data.message);
            }
          });
      });
    },
    keepHoldState() {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      }
    },
    // 匹配出销售角色的name值
    performanceUserChange(val, item) {
      item.role_name = this.role_options.find((role) => role.id === val)?.name;
    },
    getOrderInfo() {
      const { order_id } = this;
      if (order_id) {
        orderApi.getOrderInfo({ order_id }).then((res) => {
          const { code, data, message } = res.data;
          if (code === 0) {
            this.fillStudentInfo(data);
            this.fillOrderInfo(data);
            this.fillExtendInfo(data);
            this.fillPayment(data);

            this.fillOrderCoupons(data);
            this.fillTable(data);
            this.getDirectSubtractionData();
            if (this.is_period) {
              this.getPeriodList();
            }
          } else {
            this.$message.error(message);
          }
        });
      }
    },
    fillPayment(data) {
      const { payWayOptions } = this;
      const { payment } = data;
      payment?.forEach((item1) => {
        const checkedItem = payWayOptions.find(
          (item2) => item1.pay_method === item2.key
        );
        if (checkedItem) {
          checkedItem.checked = true;
          item1.value = checkedItem.value;
          item1.type = checkedItem.type;
          item1.is_shop = checkedItem.is_shop;
        }
        if (item1.pay_method === "POS") {
          // 修复历史的POS未选择方式，默认初始为扫码
          item1.pos_channel = item1.pos_channel || "ScanQrCode";
        }
      });
      this.payment = payment ?? [];
    },
    fillOrderInfo(data) {
      console.log(data, "data");
      const { order_info, wallet, entities, extend_info, items } = data;
      // 钱包
      this.useWallet = wallet.use_balance;
      this.useDeposit = wallet.use_prepay;
      // 结算
      this.sette_amount.orderOriginTotalPrice = order_info.original_price;
      this.sette_amount.orderAfterDiscountTotalPrice =
        order_info.discount_price;
      this.sette_amount.orderDueCollectPrice = order_info.actual_price;
      this.useDirectDiscount = order_info.direct_discount; // 用户输入的直减金额
      this.sette_amount.useDirectDiscount = order_info.direct_offset; // 计算出的直减金额
      this.useCoupons = order_info.coupon_offset;
      if (extend_info?.to_allocation_ids) {
        this.to_allocation_ids = extend_info?.to_allocation_ids; // 关联的租赁订单id
      }
      console.log(extend_info, "extend_info");
      if (
        this.is_lease_package &&
        entities?.[0]?.fee_type === "article" &&
        entities?.[0]?.sale_method === "lease"
      ) {
        // 发货方式选项
        this.is_payWay = true;
      }
      if (order_info.deadline > 0) {
        this.hold_state = true;
      }
      console.log(this.is_lease_package, entities, "entities");
      if (this.is_lease_package && extend_info?.to_allocation_ids) {
        this.getLeaseOrderList(this.to_allocation_ids, items);
      }
    },
    fillExtendInfo(data) {
      const { extend_info, performance, order_info } = data;
      const performanceUser = [];
      performance?.map((item) => {
        performanceUser.push({
          userId: item.employee_id,
          userName: item.employee_name,
          userRole: item.role_id,
          role_name: item.role_name
        });
      });
      this.invoice = {
        certs: extend_info.image,
        innerRemark: extend_info.inner_remark,
        outerRemark: extend_info.outer_remark,
        performanceUser,
        to_allocation_ids: extend_info.to_allocation_ids,
        delivery_method: order_info.delivery_method
      };

      const images = [];
      extend_info.image.map((item) => {
        images.push({
          uid: uuidv4(),
          url: item
        });
      });
      this.fileList = images;
      // 如果是从订单列表进行收费
      if (this.page_from === "order_list") {
        this.charge_date = order_info.charge_date;
      }
      this.period = order_info.tw_period || "";
    },
    fillStudentInfo(data) {
      const { user_info, wallet } = data;
      const { user_type, student_id, customer_id } = user_info;
      user_info.student_id =
        user_type === "customer" ? customer_id : student_id;
      this.studentInfo = {
        ...user_info,
        left_cash: wallet.balance,
        left_prepay: wallet.prepay
      };
      // this.getPaymentMethod();
    },
    selectPayWay(val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
        return;
      }
      if (val.is_shop) {
        // is_shop 的收款方式只能存在一种，如果存在则删除
        const isShopIndex = this.payment.findIndex((item) => {
          return item.is_shop;
        });
        this.payWayOptions.map((item) => {
          if (item.is_shop) item.checked = false;
        });
        if (isShopIndex !== -1) {
          this.payment.splice(isShopIndex, 1);
        }
      }
      if (val.type === "online") {
        const itemIndex = this.payment.findIndex((item) => {
          return item.type === "online";
        });
        if (itemIndex !== -1) {
          this.payment.splice(itemIndex, 1);
          this.payWayOptions.map((item) => {
            if (item.type === "online") item.checked = false;
          });
        }
      }
      val.checked = true;
      const index = this.payment.findIndex((item) => {
        return item.pay_method === val.key;
      });

      // 如果支付方式不存在，则添加
      if (index < 0) {
        const obj = {
          pay_method: val.key,
          type: val.type,
          value: val.value,
          is_shop: !!val.is_shop
        };
        if (obj.pay_method === "POS") {
          // POS机付款初始默认值为扫码方式
          obj.pos_channel = "ScanQrCode";
        }
        this.payment.push(obj);
      }
    },
    delPayWay(e, val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        e.stopPropagation();
        const index = this.payment.findIndex((item) => {
          return item.pay_method === val.key;
        });
        this.payment.splice(index, 1);

        val.checked = false;
        this.$forceUpdate();
      }
    },
    resetData() {
      this.$parent.list = [];
      this.$parent.stu_info = null;
      this.$parent.order_id = "";
      this.$parent.student_check_arr = [];
    },
    getPayResult(msn, order_id) {
      eBaoPay
        .getPayResult({
          order_id: msn
        })
        .then((res) => {
          console.log(order_id);
          const { data, code, message } = res.data;
          if (code === 0) {
            this.pay_status = data.pay_status;
            if (this.pay_status === "paid") {
              this.$message.success("收费成功！");
              this.$emit("paySuccess", order_id);
              this.resetData();
              if (this.getSendStatus()) {
                // this.showPushConfirm();
                this.getSurveyUsable(); // 获取推送问卷可用状态
              } else {
                this.back();
              }
            } else if (this.pay_status === "failed") {
              this.$message.error(message);
            }
          } else {
            this.$message.error(message);
          }
        })
        .catch(() => {
          this.$message.error("收费失败，请重试！");
          clearInterval(this.payResultTimer);
        });
    },

    counterEnd() {
      this.qrcode_pay_visible = false;
      this.order_id = "";
    },
    // 确认直减方案
    saveDirect(row) {
      this.direct_row = row;
      this.useDirectDiscount = row?.discount_money ?? 0;
      this.inputState = !!row; // 没有任何选中按直接输入方式，置为false
      this.useDirectDiscountBlur();
    },
    changeDirectNum() {
      if (this.inputState === true) {
        this.inputState = false;
        this.direct_row = null;
      }
    },
    /**
     * @description 获取直减折扣列表
     */
    getDirectSubtractionData() {
      // 打开页面清除传递的参数
      this.directParams = {
        department_id: this.studentInfo.department_id,
        charge_date: this.charge_date,
        plan_type: 2
      };
      let params = [];
      params = this.table_list.map((item) => {
        return {
          entity_id: item.id,
          entity_type: item.add_type,
          entity_price: item.standard_price * item.num,
          specification_id: item.specId
        };
      });
      this.directParams.entities = params;
      discountApi.getDiscountList(this.directParams).then((res) => {
        if (+res.status === 200) {
          if (res.data.length > 0) {
            this.only_choose_direct = res.data.every(
              (item) => item.plan_type === 2
            ); // 只有选择折扣
            this.only_manual = res.data.every((item) => item.plan_type === 3); // 只能手动输入
            this.manualId =
              res.data.find((item) => item.plan_type === 3)?.id ?? "";
          }
          this.getCalcTableData();
        } else {
          this.$message.error("获取直减折扣数据出错，请重试");
        }
      });
    },
    payWayBlur(item) {
      const val = item.pay_price;
      if (val) {
        if (!/^[0-9]+(.[0-9]{1,2})?$/.test(val)) {
          this.$message.info("输入数据格式有误，请重新输入！");
          item.pay_price = "";
        } else {
          item.pay_price = Number(item.pay_price);
        }
      }
    },

    useDirectDiscountBlur() {
      const { useDirectDiscount_is_check, useDirectDiscount } = this;
      if (useDirectDiscount_is_check) {
        if (useDirectDiscount) {
          if (!/^[0-9]+(.[0-9]{1,2})?$/.test(useDirectDiscount)) {
            this.$message.info("输入数据格式有误，请重新输入！");
            this.useDirectDiscount = 0;
          } else {
            this.useDirectDiscount = Number(useDirectDiscount);
            this.getCalcTableData();
          }
        }
      }
    },
    useWalletBlur() {
      if (!this.useWallet) {
        this.useWallet = 0;
      }
      console.log("this.useWallet :>> ", this.useWallet);
      if (this.useWallet_is_check) {
        this.getCalcTableData();
      }
    },
    useDepositBlur() {
      if (!this.useDeposit) {
        this.useDeposit = 0;
      }
      if (this.useDeposit_is_check) {
        this.getCalcTableData();
      }
    },
    discount_label(val, row) {
      const { planType, discountPlanName, discountRate } = row;
      if (planType === 3) {
        return `${discountRate.toFixed(2)}%`;
      } else {
        return discountPlanName;
      }
    },
    setExtendInfo(_parent) {
      const { expiration_date, date, day } = _parent;
      const extendInfo = {
        expireStrategy: expiration_date || "",
        afterDays: day,
        startTime: "",
        endTime: ""
      };
      // 指定日期
      if (expiration_date === "time_range") {
        if (date) {
          // 开始时间永远都是结算时的当天日期
          extendInfo.startTime = this.getDate(new Date());
          extendInfo.endTime = date;
        }
      }
      // 从收费当日起
      // if (expiration_date == 'on_paid') {
      //   if (_parent.date) {
      //     extendInfo.endTime = date;
      //   }
      // }
      return extendInfo;
    },
    getDate(time) {
      return timeFormat.GetDate(time);
    },
    get_itemDetails() {
      const itemDetails = [];
      this.table_list.map((_parent) => {
        console.log(_parent, "parent");
        const { planType } = _parent;
        let plan_id = _parent.discount;
        if (planType === 3) {
          plan_id = "a9279c8c-3d04-48b3-a525-04473a92c3d0";
        }
        const expire_strategy = this.setExtendInfo(_parent);

        const parent_row = {
          discount_method: _parent?.discount_method,
          apportion: _parent.apportion, // 是否分摊
          class_id: _parent.class_id || "",
          class_name: _parent.class_name || "",
          discounted_total_price: planType === 3 ? +_parent.total_price : null,
          discounted_unit_price:
            planType === 3 ? +_parent.discount_price : null,
          discount_plan: {
            plan_id,
            discount_money: 0,
            discount_rate: +_parent.discountRate,
            plan_name: planType === 3 ? "直接输入" : _parent.discountPlanName,
            gift_number: _parent.present_num,
            plan_type: planType
          },

          entity_id: _parent.id,
          entity_name: _parent.name,
          entity_num: _parent.num,
          entity_type: _parent.add_type,
          article_bank_id: _parent.article_bank_id || "",
          entity_unit: _parent.unit,
          expire_strategy: {
            end_time: expire_strategy.endTime,
            start_time: expire_strategy.startTime,
            strategy: expire_strategy.expireStrategy,
            validity_period: expire_strategy.afterDays
          },

          pid: "",
          specification: {
            specification_id: _parent.specId,
            specification_name: _parent.specName
          },
          fee_type: _parent.charge_type,
          is_forced_sell: _parent.is_forced_sell,
          purchase_type: _parent.purchase_type,
          standard_numb: _parent.standard_numb,
          sale_period: _parent.sales_cycle || 0
        };
        // 只有物品才有sale_method参数
        console.log(_parent, "parent_row");
        if (_parent.add_type === "article") {
          if (_parent.sales_method === 2 || _parent.sales_method === "lease") {
            parent_row.sale_method = "lease";
          } else {
            parent_row.sale_method = "purchase";
          }
        }
        itemDetails.push(parent_row);
        if (_parent.children) {
          const { children } = _parent;

          children.map((child) => {
            const { planType } = child;
            let plan_id = child.discount;
            if (planType === 3) {
              plan_id = "a9279c8c-3d04-48b3-a525-04473a92c3d0";
            }
            const child_row = {
              discount_method: child?.discount_method,
              apportion: child.apportion, // 是否分摊
              class_id: child.class_id || "",
              class_name: child.class_name || "",
              discounted_total_price:
                planType === 3 ? +child.total_price : null,
              discounted_unit_price:
                planType === 3 ? +child.discount_price : null,
              discount_plan: {
                plan_id,
                discount_money: 0,
                discount_rate: +child.discountRate,
                plan_name: planType === 3 ? "直接输入" : child.discountPlanName,
                gift_number: child.present_num,
                plan_type: planType
              },
              article_bank_id: child.article_bank_id || "",
              entity_id: child.product_id,
              entity_name: child.name,
              entity_num: child.num,
              entity_type: child.add_type,
              entity_unit: child.unit,
              expire_strategy: null, // 物品课程没有有效期至
              pid: _parent.id,
              specification: {
                specification_id: "", // 物品和赛事没有规格id
                specification_name: child.specName
              },
              fee_type: child.charge_type,
              is_forced_sell: child.is_forced_sell,
              purchase_type: child.purchase_type,
              standard_numb: child.standard_numb
            };
            itemDetails.push(child_row);
          });
        }
      });
      return itemDetails;
    },
    get_target_info() {
      const { studentInfo } = this;
      return {
        department_id: studentInfo.department_id,
        department_name: studentInfo.department_name,
        student_number: studentInfo.student_number,
        target_id: studentInfo.student_id,
        target_mobile: studentInfo.student_mobile,
        target_name: studentInfo.student_name,
        target_status: studentInfo.student_type,
        target_type: studentInfo.user_type,
        target_gender: studentInfo.student_gender
      };
    },
    // 结算页面的后台计算接口方法
    getCalcTableData() {
      const { studentInfo } = this;
      if (studentInfo) {
        const target = this.get_target_info();
        const itemDetails = this.get_itemDetails();
        const {
          useDirectDiscount_is_check,
          useWallet_is_check,
          is_check_coup,
          useWallet,
          useDeposit,
          useDeposit_is_check
        } = this;
        const useCoupons = this.useCouponsParam;
        const direct_plan = this.get_direct_plan_param();
        chargeApi
          .settlement_calculate({
            target,
            discount: {
              coupons: is_check_coup ? useCoupons : [],
              direct_plan: useDirectDiscount_is_check ? direct_plan : null
            },
            entities: itemDetails,
            wallet: {
              use_balance: useWallet_is_check ? useWallet : 0,
              use_prepay: useDeposit_is_check ? useDeposit : 0
            }
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.disableChargeBnt = false;
              this.combination(res.data);
            } else {
              this.$message.error(res.data.message);
              this.disableChargeBnt = true;
            }
          })
          .catch((err) => {
            console.error(err);
            this.$message.error("收费计算出错，请重试");
            this.disableChargeBnt = true;
          });
      }
    },
    setPropertyData(resp, loc) {
      const id = loc.parent_id ? loc.product_id : loc.id;
      if (resp.entity_id === id) {
        let charge_type_list = resp.charge_type_list;

        const isCourse = (item) => item.goods_type === "course";
        // pay_start,h5_pay为外部系统导入订单
        const isNotPayStart = (item) => item.category_id !== "pay_start";
        const isNotH5Pay = (item) => item.category_id !== "h5_pay";

        if (!resp.charge_type_list) {
          charge_type_list = this.ChargeCategory?.filter((item) =>
            [isCourse, isNotPayStart, isNotH5Pay].every((condition) =>
              condition(item)
            )
          );
        }

        const dueCollectPrice = resp.actual_price.toFixed(2); // 应收金额
        const originalTotalPrice = resp.original_total_price.toFixed(2); // 原总价
        const originalSinglePrice = resp.original_unit_price.toFixed(2); // 原单价

        const total_price = resp.discounted_total_price.toFixed(2); // 折后总价
        const discount_price = resp.discounted_unit_price.toFixed(2); // 折后单价

        loc.dueCollectPrice = dueCollectPrice;
        loc.standard_price = originalSinglePrice;
        loc.originalTotalPrice = originalTotalPrice;
        loc.total_price = total_price;
        loc.discount_price = discount_price;
        loc.total_apportion = resp.total_apportion.toFixed(2);
        this.$set(loc, "charge_type_list", charge_type_list);
      }
    },
    // 拼接组合表格数据
    combination(resp_data) {
      const { table_list } = this;
      const { entities } = resp_data.data;
      this.get_table_statistics_data(entities);
      entities.map((item) => {
        table_list.map((table) => {
          this.setPropertyData(item, table);
          if (table.children && table.children.length) {
            table.children.map((child) => {
              this.setPropertyData(item, child);
            });
          }
        });
      });
      this.fillOrderInfo(resp_data.data);
    },
    // 分类统计产品的总价
    get_table_statistics_data(entities) {
      let course = 0;
      let article = 0;
      let match = 0;
      let total = 0;
      entities.forEach((item) => {
        const money = item.discounted_total_price;
        if (item.entity_type === "course") {
          course += money;
        } else if (item.entity_type === "article") {
          article += money;
        } else if (item.entity_type === "match") {
          match += money;
        }
        total += money;
      });
      this.table_statistics = {
        course,
        article,
        match,
        total
      };
    },
    /**
     * msn --加密后的order_id
     *okay --加密后的deadline
     */
    openPayDialog(msn, okay, deadline, order_id) {
      this.qrcode_pay_visible = true;
      const { payment } = this;
      const that = this;
      const arr = payment.filter((item) => Number(item.pay_price) > 0);
      // 如果有二维码付款
      if (arr.findIndex((item) => item.pay_method === "qr_code") > -1) {
        this.qrUrl = `${window.location.origin}/qrPay?order_id=${msn}&dead_line=${okay}`;
        console.log("qrUrl :>> ", this.qrUrl);
        if (deadline) {
          // 后台返回的时间是纳秒
          const time = this.moment(deadline / 10e5).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.deadline_tip = `二维码有效期截至: ${time}，请您尽快支付`;
        }
        // 每3s获取一次支付结果
        this.payResultTimer = setInterval(() => {
          that.getPayResult(msn, order_id);
        }, 3000);
      }
      // pos机付款接口
      if (arr.findIndex((item) => item.pay_method === "POS") > -1) {
        const pay_price = arr.find(
          (item) => item.pay_method === "POS"
        ).pay_price;
        eBaoPay
          .posPay({
            order_id: msn,
            amount: pay_price
          })
          .then((res) => {
            const { code, message } = res.data;
            if (code === 0) {
              // 每3s获取一次支付结果
              this.payResultTimer = setInterval(() => {
                that.getPayResult(msn, order_id);
              }, 3000);
            } else {
              this.$message.error(message);
              this.qrcode_pay_visible = false;
            }
          })
          .catch((err) => {
            this.$message.error(err);
          });
      }
    },
    back() {
      if (this.payResultTimer) {
        clearInterval(this.payResultTimer);
      }

      this.$emit("close");
    },
    closeCoupon() {
      this.coupon_visible = false;
    },
    showCoupon() {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        this.coupon_visible = true;
      }
    },
    showDirectPro() {
      this.$refs.directPro.openDialog(this.directParams, this.direct_row);
    },

    validateBankNo() {
      const { payment } = this;
      const transfer = payment.find((item) => item.pay_method === "transfer");
      if (transfer && !transfer.pay_account) {
        this.$message.info("付款银行卡号不能为空！");
        return false;
      } else {
        return true;
      }
    },
    setCheckCouponParams(data) {
      const useCoupons = [];
      data.map((item, index) => {
        useCoupons.push({
          template_id: item.coupon_id,
          coupon_id: item.id,
          coupon_order: index,
          template_name: item.name,
          coupon_type: item.coupon_type,
          quota: item.quota
        });
      });
      this.useCouponsParam = useCoupons;
    },
    get_direct_plan_param() {
      // 使用直减方案
      const { direct_row, useDirectDiscount, manualId } = this;
      if (this.inputState) {
        return {
          discount_money: direct_row.discount_money,
          discount_rate: direct_row.discount_rate,
          gift_number: direct_row.gift_number,
          plan_id: direct_row.id,
          plan_name: direct_row.name,
          plan_type: direct_row.plan_type
        };
      } else {
        return {
          discount_money: Number(useDirectDiscount),
          discount_rate: 0,
          gift_number: 0,
          plan_id: manualId,
          plan_name: "直接输入",
          plan_type: 3
        };
      }
    },
    get_performance_param() {
      const arr = [];
      this.invoice.performanceUser.map((item) => {
        arr.push({
          employee_id: item.userId,
          employee_name: item.userName,
          role_id: item.userRole,
          role_name: item.role_name
        });
      });
      return arr;
    },
    get_payment_param() {
      return this.payment.filter((item) => item.pay_price >= 0);
    },
    getSaveOrderParams() {
      const itemDetails = this.get_itemDetails();
      const {
        invoice,
        is_check_coup,
        useDirectDiscount_is_check,
        useDeposit_is_check,
        useWallet_is_check,
        charge_date,
        useWallet,
        useDeposit,
        period
      } = this;

      const useCoupons = this.useCouponsParam;
      const target = this.get_target_info();
      const direct_plan = this.get_direct_plan_param();
      const performance = this.get_performance_param();
      const payment = this.get_payment_param();
      const params = {
        order_id: this.order_id,
        target,
        discount: {
          coupons: is_check_coup ? useCoupons : [],
          direct_plan: useDirectDiscount_is_check ? direct_plan : null
        },
        entities: itemDetails,
        extend_info: {
          image: invoice.certs,
          inner_remark: invoice.innerRemark,
          outer_remark: invoice.outerRemark,
          push_parents: this.is_push,
          charge_date,
          tw_period: period,
          delivery_method: invoice.delivery_method,
          to_allocation_ids: this.to_allocation_ids
        },
        performance,
        payment,

        wallet: {
          use_balance: useWallet_is_check ? useWallet : 0,
          use_prepay: useDeposit_is_check ? useDeposit : 0
        }
      };
      return params;
    },

    // 直营校结算时必须购买同类型的产品
    validate_different_product_type() {
      const { studentInfo, table_list } = this;
      if (studentInfo.department_name !== "聂卫平围棋网校-新") {
        if (
          table_list.some((item) => item.add_type !== table_list[0].add_type)
        ) {
          this.$message.info("产品类型不一致不允许结算！");
          return true;
        }
      }
    },

    // 直营校结算时必须产品的收费类型一致
    validate_different_charge_type() {
      const { studentInfo, table_list } = this;
      if (studentInfo.department_name !== "聂卫平围棋网校-新") {
        if (
          table_list.some(
            (item) => item.charge_type !== table_list[0].charge_type
          )
        ) {
          this.$message.info("收费类型不一致不允许提交！");
          return true;
        }
      }
    },
    // 保存订单
    saveOrder() {
      if (this.validateBankNo()) {
        if (this.validate_different_product_type()) {
          return;
        }
        if (this.validate_different_charge_type()) {
          return;
        }
        const params = this.getSaveOrderParams();

        params.action = "save"; // 保存订单操作
        // 业绩归属人信息必须完整
        const check_performance = params.performance.every(
          (item) => item.employee_id && item.role_id
        );
        if (!check_performance) {
          this.$message.info("业绩归属人信息不完整，请检查后重新提交！");
          return;
        }
        if (this.is_payWay && !params.extend_info.delivery_method) {
          this.$message.info("发货方式不能为空！");
          return;
        }

        // console.log("object :>> ", params);

        chargeApi
          .chargeSaveOrder(params)
          .then((res) => {
            const { code, message } = res.data;
            if (code === 0) {
              this.$message.success("保存订单成功！");
              this.resetData();
              this.back();
            } else {
              this.$message.error(message);
            }
          })
          .catch(() => {
            this.$message.error("保存订单失败，请重试！");
          });
      }
    },

    chargeConfirmTip() {
      const { surplus_amount, table_list } = this;

      if (this.validate_different_product_type()) {
        return;
      }
      // 有可选的收费类型时，提交时必须选择收费类型
      const arr = table_list.filter(
        (item) => item.add_type === "course" && item.charge_type === ""
      );

      if (arr.length) {
        this.$message.info("课程收费类型不能为空！");
        return;
      }
      if (this.validate_different_charge_type()) {
        return;
      }
      if (!this.validateBankNo()) {
        return;
      }
      if (this.payment.some((item) => item.is_shop && !item.third_order_id)) {
        this.$message.info("第三方订单号不能为空！");
        return;
      }
      if (surplus_amount > 0) {
        this.$message.info(`还差金额${surplus_amount}元，不能进行收费！`);
        return;
      }
      // 业绩归属人信息必须完整
      const check_performance = this.invoice.performanceUser.every(
        (item) => item.userId && item.userRole
      );
      if (!check_performance) {
        this.$message.info("业绩归属人信息不完整，请检查后重新提交！");
        return;
      }
      if (!this.invoice.innerRemark) {
        this.$message.info("内部备注不能为空！");
        return;
      }
      if (this.is_payWay && !this.invoice.delivery_method) {
        this.$message.info("发货方式不能为空！");
        return;
      }
      if (surplus_amount < 0) {
        this.$message.info(`还差金额不能小于0，请核对相关录入金额！`);
        return;
      }

      if (
        this.isPerformanceUserRequired &&
        !this.invoice.performanceUser.length
      ) {
        this.$message.info(`请添加业绩归属人！`);
        return;
      }
      this.charge_really_visible = true;
    },
    // 判断该学员有没有适用的问卷
    getSurveyUsable() {
      const { customer_id, user_type, student_id } = this.studentInfo;
      const id = user_type === "student" ? customer_id : student_id;
      discountApi
        .surveySendMultiple({
          survey_type: "sign_up",
          customer_id: [id]
        })
        .then((res) => {
          const { code, data } = res.data;
          if (code === 0) {
            if (data && data.length) {
              this.survey_visible = true;
              const student_id = data[0].student_id;
              this.showPushConfirm(student_id);
            } else {
              this.back();
            }
          } else {
            this.back();
          }
        })
        .catch(() => {
          this.back();
        });
    },
    showPushConfirm(student_id) {
      /// 询问框
      this.$confirm(`是否要给该学员推送问卷调查?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          // const { department_id, student_id } = this.studentInfo;
          discountApi
            .surveySend({
              survey_type: "sign_up",
              // campus_id: department_id,
              student_id: [student_id]
            })
            .then((res) => {
              const { code, message } = res.data;
              if (code === 0) {
                this.$refs.table.clearSelection();
                this.$message.success("推送成功！");
              } else {
                this.$message.error(message);
              }
              this.back();
            })
            .catch(() => {
              this.back();
              this.$message.error("推送失败！");
            });
        })
        .catch(() => {
          this.back();
        });
    },
    hasTargetCourseType(list, targetTypes) {
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (
          item.add_type === "course" &&
          targetTypes.includes(item.course_type)
        ) {
          return true;
        }
        // 如果有children，递归检查
        if (item.children && item.children.length > 0) {
          const hasTargetInChildren = this.hasTargetCourseType(
            item.children,
            targetTypes
          );
          if (hasTargetInChildren) {
            return true;
          }
        }
      }
      return false;
    },
    // 判断是否需要推送问卷调查
    getSendStatus() {
      console.log("111 :>> ", 111);
      if (!this.studentInfo || !Array.isArray(this.table_list)) {
        console.warn("必要的数据缺失");
        return false;
      }
      const { user_type, student_type } = this.studentInfo;

      // 递归判断购买课程数据中add_type为course的对象中是否有course_type=1或者course_type=3的数据
      const targetTypes = [1, 3]; // 课时包、常规课
      const hasTargetCourse = this.hasTargetCourseType(
        this.table_list,
        targetTypes
      );

      // 有推送模版并且是意向客户学员并且购买的课程有课时包、常规课
      if (!hasTargetCourse) {
        return false;
      }
      /*
       * 1. 学员类型为在读学员学员，且学员状态为试听或临加
       * 2. 或者学员类型为意向客户
       */
      const STUDENT_SPECIFIC_TYPES = new Set(["audition", "temp"]);
      function isSpecificStudentType(student_type) {
        return STUDENT_SPECIFIC_TYPES.has(student_type);
      }
      function checkUserType(user_type, student_type) {
        if (user_type === "student") {
          return isSpecificStudentType(student_type);
        }
        return user_type === "customer";
      }
      const isTargetUserType = checkUserType(user_type, student_type);
      return isTargetUserType;
    },
    // 收费提交
    chargeConfirm() {
      const params = this.getSaveOrderParams();
      params.action = "create"; // 订单收费操作

      // console.log("object :>> ", params);
      // return;
      this.chargeBtnloading = true;
      chargeApi.orderCheck(params).then((res) => {
        const { code, data } = res.data;
        if (code === 0) {
          if (data.is_exist) {
            this.$confirm("系统已存在相同订单，是否继续收费？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                this.orderSave(params);
              })
              .catch(() => {
                this.$router.push({
                  name: "orderManagement"
                });
              });
          } else {
            this.orderSave(params);
          }
        }
      });
    },
    orderSave(params) {
      const { sette_amount } = this;
      const only_offline = params.payment
        .filter((item) => item.pay_price > 0)
        .every((item) => item.type === "offline");
      chargeApi
        .chargeSaveOrder(params)
        .then((res) => {
          if (this.payResultTimer) {
            clearInterval(this.payResultTimer);
          }
          const { data, message, code } = res.data;
          if (code === 0) {
            this.order_id = data.order_id;
            if (only_offline || sette_amount.orderDueCollectPrice === 0) {
              this.resetData();
              this.$message.success("收费成功！");
              this.$emit("paySuccess", data.order_id);
              if (this.getSendStatus()) {
                // this.showPushConfirm();
                this.getSurveyUsable(); // 获取推送问卷可用状态
              } else {
                this.back();
              }
            } else {
              // 实交金额大于0且有线上付款方式才进行线上付款
              const { msn, okay, deadline, order_id } = data;
              this.openPayDialog(msn, okay, deadline, order_id);
            }
          } else {
            this.$message.error(message);
          }
          this.chargeBtnloading = false;
        })
        .catch((err) => {
          console.error(err);
          this.$message.error("收费失败，请重试！");
          this.chargeBtnloading = false;
        });
    },
    handleRemove(file) {
      this.fileList.map((item, index) => {
        if (file.uid === item.uid) {
          this.fileList.splice(index, 1);
        }
      });
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handleChargeType(val) {
      if (this.isNetworkSchool) {
        return false;
      }
      if (this.table_list.every((i) => i.charge_type)) {
        const params = {
          fee_type: this.table_list.map((i) => i.charge_type),
          department_id: this.studentInfo.department_id
        };
        if (this.studentInfo.user_type === "student") {
          params.student_id = this.studentInfo.student_id;
          params.customer_id = undefined;
        } else {
          params.student_id = undefined;
          params.customer_id = this.studentInfo.student_id;
        }
        chargeApi.feeTypeAutoMatch(params).then((res) => {
          const { code, data } = res.data;
          if (code === 0) {
            if (data.performances) {
              this.isPerformanceUserRequired = data.is_required;
              if (data.is_required) {
                if (data.performances === null) {
                  this.$alert("当前业绩归属人为空，请手动选择", "提示", {
                    confirmButtonText: "确定",
                    customClass: "performances_message"
                  });
                } else if (data.performances.some((i) => i.is_leave)) {
                  this.$alert("当前业绩归属人已离职，请手动选择", "提示", {
                    confirmButtonText: "确定",
                    customClass: "performances_message"
                  });
                }
              }
              this.autoFill = data.performances.map((i) => {
                return {
                  userId: i.employee_id,
                  userName: i.employee_name,
                  source: "auto",
                  userRole: i.role_id,
                  role_name: i.role_name,
                  is_leave: i.is_leave
                };
              });
            } else {
              this.autoFill = [];
              this.isPerformanceUserRequired = false;
            }
            this.invoice.performanceUser = [
              ...this.manualFill,
              ...this.autoFill
            ];
          } else {
            this.invoice.performanceUser = this.manualFill;
          }
        });
      }
    },
    // 添加归属人
    createRow() {
      this.manualFill.push({
        userId: "",
        userName: "",
        userRole: "",
        source: "manual",
        role_name: ""
      });
      this.invoice.performanceUser = [...this.manualFill, ...this.autoFill];
    },
    // 删除归属人
    delRow(index, item) {
      if (item.source === "auto") {
        const autoFillIndex = this.autoFill.findIndex(
          (i) => i.userId === item.userId
        );
        this.autoFill.splice(autoFillIndex, 1);
      } else {
        const manualFillIndex = this.manualFill.findIndex(
          (i) => i.userId === item.userId
        );
        this.manualFill.splice(manualFillIndex, 1);
      }
      this.invoice.performanceUser.splice(index, 1);
    },
    beforeUpload(file) {
      // const { uploadFiles } = this.$refs["uploadImgs"];
      const isLt20M = file.size / 1024 / 1024 > 20;
      if (isLt20M) {
        this.$message.info("上传头像图片大小不能超过 20MB!");
        return false;
      }
    },
    uploadImg(upload) {
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          this.fileList.push({
            name: res.objectKey,
            url: res.url
          });
        }
      });
    },
    getPosList() {
      postManagementApi
        .GetPostList({ is_enabled: true, office_type: "performance" })
        .then((res) => {
          if (+res.status === 200 && res.data) {
            this.role_options = res.data
              ? res.data.map((item) => {
                  return {
                    name: item.name,
                    id: item.id,
                    disabled: item.disabled
                  };
                })
              : [];
          }
        });
    },
    // 选择完优惠券回调
    couponConfirm(data) {
      // const { count, discount } = data;
      // this.coupon_discount = discount;
      this.coupon_visible = false;
      this.setCheckCouponParams(data);
      this.getCalcTableData();
    },
    removePayway(val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        this.payment.splice(
          this.payment.findIndex((item) => item.pay_method === val.pay_method),
          1
        );
        this.payWayOptions.map((item) => {
          if (item.key === val.pay_method) {
            item.checked = false;
          }
        });
        this.$forceUpdate();
      }
    },

    getCouponList() {
      const { useCouponsParam } = this;
      if (useCouponsParam.length) {
        this.couponIds = useCouponsParam.map((item) => item.coupon_id);
        // 之前选择过优惠券,需要回调可叠加接口
        this.getCouponCanUseList(this.getCouponCanOverlayList);
      } else {
        this.getCouponCanUseList();
      }
    },

    // 获取可使用优惠券信息
    getCouponCanUseList(cb = () => {}) {
      const { student_id } = this.studentInfo;
      const course_list = [];
      this.table_list.map((item) => {
        course_list.push({
          course_id: item.id,
          couse_type: item.add_type,
          price: +(Number(item.total_price) * 100).toFixed(2) // 后台以分为单位
        });
      });
      const params = {
        course_list,
        charge_date: this.charge_date,
        student_id
      };
      // 如果是创建后的订单
      if (this.order_id) {
        params.used_coupon_ids = this.couponIds;
      }
      couponApi.getCouponCanUseList(params).then((res) => {
        const { code, message, data } = res.data;
        if (code === 0) {
          this.couponList = data || [];
          this.couponList.map((item) => {
            const checked = this.couponIds.includes(item.id);
            this.$set(item, "checked", checked);
            // 优惠券1-可用， 2-不可用
            this.$set(item, "disabled", item.available !== 1);
          });
          cb();
        } else {
          this.$message.error(message);
        }
      });
    },

    // 设置不可用的
    setCouponCanOverlayDisabled(data) {
      if (data) {
        const ids = data.map((item) => item.id);
        this.couponList.map((item) => {
          // 剩下的没选择的券
          if (!item.checked) {
            this.$set(item, "disabled", !ids.includes(item.id)); // 没有匹配上，设置为disabled
          }
        });
      }
    },

    // 获取可叠加优惠券
    getCouponCanOverlayList() {
      this.coupon_loading = true;
      const { student_id } = this.studentInfo;
      const course_list = [];
      this.table_list.map((item) => {
        course_list.push({
          course_id: item.id,
          couse_type: item.add_type,
          price: +(Number(item.total_price) * 100).toFixed(2) // 后台以分为单位
        });
      });

      const coupon_id = this.couponIds.at(-1) ?? [];
      const params = {
        coupon_id,
        course_list,
        charge_date: this.charge_date,
        student_id,
        used_coupon_ids: this.couponIds
      };
      // 可叠加接口只请求一次
      couponApi
        .getCouponCanOverlayList(params)
        .then((res) => {
          const { code, message, data } = res.data;
          if (code === 0) {
            this.setCouponCanOverlayDisabled(data || []);
          } else {
            // this.setCouponCanOverlayDisabled([]);
            this.$message.error(message);
          }
          this.coupon_loading = false;
        })
        .catch(() => {
          this.coupon_loading = false;
        });
    },
    // 每次点击优惠券
    couponChoose() {
      const { couponIds } = this;
      // 只要有一张优惠券就不再请求可叠加优惠券
      if (couponIds.length && couponIds.length === 1) {
        this.getCouponCanOverlayList();
      }
      if (!couponIds.length) {
        // 全部反选掉，重新获取所有可用优惠券
        this.getCouponCanUseList();
      }
    },
    // 回显优惠券相关数据
    fillOrderCoupons(data) {
      const { discount } = data;
      if (discount.coupons) {
        this.couponIds = discount.coupons.map((item) => item.coupon_id);
        this.useCouponsParam = discount.coupons;
      }
    },

    async getPaymentMethod() {
      let department_id = "";
      if (this.order_id) {
        department_id = this.studentInfo.department_id;
      } else {
        const { stu_info } = this.$parent;
        department_id = stu_info?.department_id;
      }
      const res = await chargeApi.paymentMethod({
        department_id
      });

      const { data } = res.data;
      this.payWayOptions = data.filter((item) => item.business === "charge");
    },
    // 列表中的有效期至
    getExtendInfo(obj) {
      const { extendInfo } = obj;
      if (extendInfo) {
        const { strategy, validity_period, end_time } = extendInfo;
        obj.expiration_date = strategy;
        obj.day = validity_period;
        // 指定日期
        if (strategy === "time_range") {
          obj.date = end_time;
        }
      }
    },
    // 填充产品列表数据
    fillTable(data) {
      const that = this;
      const { items } = data;

      function setProperty(rowObj) {
        const { discount_plan } = rowObj;
        const { plan_id, plan_type, plan_name, discount_rate } = discount_plan;
        const obj = {
          id: rowObj.goods_id,
          name: rowObj.goods_name,
          specId: rowObj.specification.specification_id,
          specName: rowObj.specification.specification_name,
          expiration_date: "",
          date: "",
          day: null,
          pay_notification_url: rowObj.callbackAddress || "",
          standard_price: rowObj.original_unit_price,
          num: rowObj.goods_num,
          discount_method: rowObj?.discount_method,
          discount: plan_id, // 折扣的model值
          discountRate: discount_rate,
          discountPlanName: plan_name,
          planType: plan_type,
          discountReadonly: true,
          discount_price: rowObj.discount_unit_price,
          total_price: rowObj.discount_total_price,
          present_num: rowObj.gift_num,
          apportion: rowObj.apportion,
          total_apportion: rowObj.total_apportion || 0,
          add_type: rowObj.goods_type,
          unit: rowObj.goods_unit,
          is_forced_sell: rowObj.is_forced_sell,
          parent_id: rowObj.pid,
          is_related: !!rowObj.pid, // 是否是关联带的物品或者赛事
          charge_type: rowObj.fee_type || "",
          class_id: rowObj.class_id,
          class_name: rowObj.class_name,
          purchase_type: rowObj.purchase_type, // 课程规格类型
          standard_numb: rowObj.standard_numb,
          extendInfo: rowObj.expire_strategy || null,
          article_bank_id:
            rowObj.add_type === "article" ? rowObj.article_bank_id : "",
          children: [],
          fee_type: rowObj.fee_type,
          sales_method: rowObj.sale_method,
          sales_cycle: rowObj.sale_period
        };
        that.getExtendInfo(obj, rowObj);
        return obj;
      }
      const parent_arr = [];

      items.map((parent_row) => {
        if (parent_row.pid === "") {
          const obj = setProperty(parent_row);
          parent_arr.push(obj);
        }
      });
      parent_arr.map((item) => {
        items.map((parent_row) => {
          if (parent_row.pid) {
            if (item.id === parent_row.pid) {
              const obj = setProperty(parent_row);
              obj.product_id = parent_row.goods_id;
              item.children.push(obj);
            }
          }
        });
      });
      this.table_list = parent_arr;
      console.log(this.table_list, "table_list");
      this.handleChargeType();
    },
    // 获取租赁订单列表
    async getLeaseOrderList(to_allocation_ids, items) {
      const { student_id } = this.studentInfo;
      const { data } = await chargeApi.getLeaseOrderList({
        wallet_good_ids: [to_allocation_ids],
        related_goods_id: items.map((item) => item.goods_id),
        has_left: true,
        student_id
      });
      if (data.code === 0) {
        console.log(data, "data");
        if (data.data) {
          this.leaseAresDialogTableDatad = data.data;
          this.leaseAresDialogVisibled = true;
        }
      } else {
        this.$message.error(data.message);
      }
    },
    handclose() {
      this.leaseAresDialogVisibled = false;
      this.leaseAresDialogTableDatad = [];
    },
    handleConfirm(data) {
      console.log(data, "data");
      this.leaseAresDialogVisibled = false;
      this.leaseAresDialogTableDatad = [];
    }
  }
};
</script>
<style lang="less" scoped>
.settlement-class {
  .directBox {
    ::v-deep .el-input__suffix {
      height: 31px;
      top: 0px;
      line-height: 27px;
      right: 0;
    }

    .searchBtn {
      display: inline-block;
      width: 32px !important;
      height: 100%;
      cursor: pointer;
      user-select: none;
    }
  }

  /deep/ .tg-table__box {
    margin: 0;
    margin-top: 16px;
    box-shadow: none;

    .row__box--flex {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
    }

    .mark {
      width: 15px;
      height: 15px;
      margin-right: 4px;
    }

    .charge-type-list-select {
      .el-input {
        width: 110px;
      }
    }

    .product-name {
      .cell {
        display: inline-flex;
        align-items: center;
      }
    }
  }

  ::v-deep .el-dialog__body {
    padding: 0 16px 0 16px;
    overflow: auto;
    height: 700px;
  }

  .tg-dialog__content {
    .s-title {
      background-color: #f5f8fc;
      color: #475669;
      height: 48px;
      line-height: 48px;
      padding-left: 16px;
      border: 1px solid #2d80ed;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      font-weight: 500;
      box-sizing: border-box;
    }

    .s-content {
      padding: 24px 16px;
      border: 1px solid #e0e6ed;
      border-top: 0;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;

      .pay-way-button-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 16px;

        .pay-way-button {
          margin-bottom: 10px;
          cursor: pointer;
          height: 32px;
          min-width: 96px;
          // position: relative;
          text-align: center;
          // line-height: 32px;
          border-radius: 4px;
          margin-right: 20px;
          box-sizing: border-box;
          padding: 0 12px;
          font-size: 14px;
          background: #fff;
          color: #2d80ed;
          border: 1px solid #2d80ed;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;

          /deep/ .el-icon-close {
            background-image: url("~@/assets/图片/icon_close_white.png");
            width: 10px;
            height: 10px;
          }

          &:hover {
            background: #ebf4ff;
            color: #2d80ed;

            .el-icon-close {
              background-image: url("~@/assets/图片/icon_close.png");
            }
          }

          // .tag_bg {
          //   width: 100%;
          //   height: 100%;
          //   border-radius: 4px;
          //   opacity: 0.12;
          //   position: absolute;
          //   left: 0;
          //   top: 0;
          // }
          .tag_line {
            padding: 0 8px;
          }

          // /deep/.el-icon-close {
          //   border-radius: 50%;
          //   text-align: center;
          //   position: relative;
          //   cursor: pointer;
          //   font-size: 12px;
          //   height: 16px;
          //   line-height: 16px;
          //   vertical-align: middle;
          //   top: -1px;
          //   background-image: none;
          //   &:hover {
          //     background-image: none;
          //   }
          // }
        }

        .payway-button-selected {
          background: #2d80ed;
          color: #fff;
        }
      }

      .payway-list {
        display: flex;
        width: 100%;
        align-items: center;
        flex-wrap: wrap;

        .payway-row {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          margin-right: 16px;
          width: 100%;

          span:nth-child(1) {
            width: 100px;
            margin-right: 12px;
          }

          span:nth-child(2) {
            text-align: left;
          }

          span:nth-child(3) {
            margin-left: 16px;
          }
        }
      }

      .you-hui-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        span:nth-child(1) {
          width: 100px;
        }

        span:nth-child(2) {
          text-align: left;
        }

        span:nth-child(3) {
          margin-left: 16px;
        }

        &:last-child {
          margin-bottom: 0px;
        }
      }
    }

    .tg-label--required:before {
      content: "*";
      color: #ff0317;
      margin-right: 4px;
    }

    .stu-info {
      margin-top: 16px;

      .info {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        border: 1px solid #e0e6ed;
        border-top: 0;
        display: flex;
        align-items: center;
        height: 52px;
        padding: 0 16px;

        span {
          margin-right: 20px;
          color: #475669;

          em {
            font-style: normal;
            color: #8492a6;
            margin-right: 16px;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .tg-table-bottom {
      padding: 16px 16px;
      border-bottom: 1px solid #e0e6ed;
      padding-left: 678px;

      span {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        em {
          font-style: normal;
          width: 100px;
          text-align: left;
          margin-left: 132px;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .section-middle {
      display: flex;
      justify-content: space-between;
      margin-top: 32px;

      .s-left {
        // width: 50%;
        flex: 1;
        margin-right: 16px;

        .you-hui-box {
          margin-bottom: 40px;
        }
      }

      .settlement-amount-box {
        width: 335px;

        .amount-row {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          justify-content: space-between;

          span:nth-child(1) {
            width: 74px;
            text-align: right;
          }

          span:nth-child(2) {
            width: 112px;
            text-align: left;
            box-sizing: border-box;
            padding-left: 15px;
          }

          &:last-child {
            margin-bottom: 0;

            span:nth-child(2) {
              padding-left: 0px;
            }
          }

          .prefix {
            display: block;
            text-align: right !important;
            margin-top: 6px;
            width: 20px !important;
            color: #333;
            margin-left: 5px;
          }
        }
      }
    }

    .charge-box {
      .diff-amount {
        display: flex;
        align-items: center;
        border: 1px solid #2d80ed;
        border-radius: 4px;
        justify-content: center;
        width: 280px;
        padding: 6px 0;
        background-color: #f5f8fc;
        margin-bottom: 16px;

        img {
          width: 12px;
          height: 12px;
          margin-right: 6px;
        }
      }

      .you-hui-row {
        span:nth-child(1) {
          min-width: 60px;
          text-align: left;
          margin-left: 0;
          margin-right: 16px;
          width: 100px;
        }
      }
    }

    .extra-box {
      margin-bottom: 30px;

      .you-hui-row {
        span:nth-child(1) {
          width: 95px;
          text-align: left;
        }
      }

      .user-table {
        width: 100%;
        margin-left: 6px;

        .s-title {
          display: flex;
          align-items: center;
          padding: 0px 16px;
          border-radius: 4px;
          font-weight: normal;

          label {
            line-height: 100%;
            display: block;
            box-sizing: border-box;
          }
        }

        .s-content {
          padding: 0px;
          border: 0;

          .row {
            padding: 12px 16px;
            margin: 0;
            width: 100%;

            label {
              display: block;
              box-sizing: border-box;
            }

            &.danger {
              background: #f56c6cbd;
              border-radius: 4px;
            }

            display: flex;
            align-items: center;
            border-bottom: 1px solid #e0e6ed;
          }

          .add-icon {
            font-style: normal;
            color: @base-color;
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 12px 0;
            border-bottom: 1px solid #e0e6ed;

            img {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}

/deep/ .extra-box {
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 98px;
  }

  .el-upload-list__item {
    width: 100px;
    height: 100px;
  }

  .el-upload-list__item-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .el-textarea__inner {
    height: 88px;
    width: 100%;
  }
}

::v-deep .dialog-cls-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .check-list {
    font-size: @text-size_normal;
    color: #475669;

    span {
      color: @base-color;
      font-weight: bold;
    }
  }
}

.more {
  width: 16px;
  height: 4px;
}

.del-suffix {
  width: 14px;
  height: 14px;
  cursor: pointer;
}

.custom--select {
  & > ::v-deep .el-input .el-input__inner {
    cursor: pointer;
  }

  & > ::v-deep .el-input .el-input__suffix-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: inherit;
    margin-right: 10px;
    cursor: pointer;
  }
}

.charge-type-class {
  ::v-deep .el-dialog__body {
    padding: 16px;
    overflow: auto;
    background-color: #fff;
  }

  ::v-deep .tg-dialog__content {
    height: 150px;

    .el-select {
      width: 586px;

      .el-input {
        width: 586px;
      }
    }
  }

  ::v-deep .el-input.is-focus::after {
    border: none;
  }
}

/deep/.qrcode_pay_dialog {
  .el-dialog__body {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    min-height: 200px;
  }
}

.divider {
  width: 100%;
  margin: 32px 0;
  height: 1px;
  background-color: #e0e6ed;
  padding: 0;
}

/deep/ .charge-price-class {
  padding: 0 20px;

  .el-divider--horizontal {
    margin: 16px 0 !important;
  }
}

.settlement-qr-code {
  display: flex;
  align-items: center;
  justify-content: center;
}

.time-count {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  & > span {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .counter {
    color: red;
    font-size: 18px;
    margin-left: 10px;
  }
}

.deadline-tip {
  color: red;
  font-size: 13px;
  margin-top: 10px;
}

.tg-dialog__header-title {
  display: flex;
  justify-content: space-between;
  padding-right: 90px;
  align-content: center;

  /deep/ .el-select-dropdown {
    top: 40px !important;
  }

  /deep/ .el-input.is-focus::after {
    border: 0;
  }
}
</style>
