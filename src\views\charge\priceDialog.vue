<template>
  <div class="vessel">
    <el-dialog
      :visible="true"
      title="结算中心"
      width="1300px"
      :before-close="close"
      class="settlement-class"
    >
      <div class="tg-dialog__content">
        <div class="stu-info">
          <div class="s-title">结算</div>
          <div class="info">
            <span>
              应交金额:
              <span style="color: red">¥{{ price }}</span></span
            >
            <span>
              实交金额:
              <inputNumber
                :controls="false"
                :precision="2"
                :min="0"
                v-model="price"
                :readonly="hold_state"
                @focus="keepHoldState"
              ></inputNumber>
            </span>
            <span>
              <el-select v-model="priceType" placeholder="请选择">
                <el-option
                  v-for="item in priceTypeList"
                  :key="item.category_id"
                  :label="item.category_name"
                  :value="item.category_id"
                >
                </el-option>
              </el-select>
              <span style="color: green; margin-left: 10px"
                >¥{{ price }}</span
              ></span
            >
          </div>
        </div>
        <div class="divider"></div>
        <div class="charge-box">
          <div class="s-title">收款方式</div>
          <div class="s-content">
            <span class="diff-amount">
              <img src="@/assets/图片/icon_permission.png" />
              还差金额：<span style="color: #ff0517"
                >￥{{ surplus_amount }}</span
              ></span
            >
            <div class="pay-way-button-list">
              <div
                class="pay-way-button"
                v-for="(item, index) in payWayOptions"
                @click="selectPayWay(item)"
                :class="{ 'payway-button-selected': item.checked }"
                :key="index"
              >
                <div class="tag_bg" :style="`background:${item.color};`"></div>
                <span>{{ item.value }}</span>
                <span v-if="item.checked" class="tag_line">|</span>
                <i
                  v-if="item.checked"
                  class="el-icon-close"
                  @click="delPayWay($event, item)"
                ></i>
              </div>
            </div>
            <div class="payway-list">
              <div
                v-for="(item, index) in payment"
                :key="index"
                class="payway-row"
              >
                <span>{{ item.value }}</span>
                <span>
                  <div class="custom--select">
                    <el-input
                      v-model.trim="item.pay_price"
                      @blur="payWayBlur(item)"
                      placeholder="请输入"
                      :readonly="hold_state"
                      @focus="keepHoldState"
                      ><img
                        src="@/assets/图片/icon_del_ac.png"
                        slot="suffix"
                        alt=""
                        class="del-suffix"
                        @click="removePayway(item)"
                    /></el-input>
                  </div>
                </span>
                <!--如果是银行汇款方式-->
                <template v-if="item.pay_method == 'transfer'">
                  <span style="margin-right: 16px">付款银行账号</span>
                  <span
                    ><el-input
                      v-model="item.pay_account"
                      placeholder="付款银行账号"
                    ></el-input
                  ></span>
                </template>
                <!--如果是POS方式-->
                <template v-if="item.pay_method === 'POS'">
                  <el-radio-group
                    style="margin-left: 16px"
                    v-model="item.pos_channel"
                  >
                    <el-radio label="ScanQrCode">扫码</el-radio>
                    <el-radio label="SwipeCard">刷卡</el-radio>
                  </el-radio-group>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div class="divider"></div>
        <div class="extra-box">
          <div class="s-title">其他信息</div>
          <div class="s-content">
            <div style="align-items: baseline" class="you-hui-row">
              <span>业绩归属人</span>
              <div class="user-table">
                <div class="s-title">
                  <label style="width: 45%; padding-left: 50px"
                    >业绩归属人</label
                  >
                  <label style="width: 35%">销售角色</label>
                  <label style="width: 20%; text-align: center">操作</label>
                </div>
                <div class="s-content">
                  <div
                    v-for="(item, index) in invoice.performanceUser"
                    :key="index"
                    class="row"
                  >
                    <div
                      style="
                        width: 45%;
                        padding-left: 50px;
                        box-sizing: border-box;
                      "
                    >
                      <div class="custom--select">
                        <course-staff
                          :check_id.sync="item.userId"
                          :check_name.sync="item.userName"
                          staff_placeholder="请选择业绩归属人"
                        ></course-staff>
                      </div>
                    </div>
                    <div style="width: 35%">
                      <el-select
                        @change="
                          (val) => {
                            performanceUserChange(val, item);
                          }
                        "
                        :popper-append-to-body="false"
                        v-model="item.userRole"
                        placeholder="请选择销售角色"
                      >
                        <el-option
                          v-for="item in role_options"
                          :key="item.value"
                          :label="item.name"
                          :value="item.id"
                          :disabled="item.disabled"
                        >
                        </el-option>
                      </el-select>
                    </div>
                    <div
                      @click="delRow(index)"
                      style="width: 20%; text-align: center"
                    >
                      <img
                        src="@/assets/图片/icon_del_ac.png"
                        class="del-suffix"
                      />
                    </div>
                  </div>
                  <em @click="createRow" class="add-icon">
                    <img src="@/assets/图片/icon_add.png" class="del-suffix" />
                    添加业绩归属人</em
                  >
                </div>
              </div>
            </div>
            <div class="you-hui-row">
              <span>上传图片</span>
              <div>
                <el-upload
                  action="#"
                  list-type="picture-card"
                  accept="image/png, image/jpeg"
                  :file-list="fileList"
                  :limit="3"
                  ref="uploadImgs"
                  :before-upload="beforeUpload"
                  :http-request="uploadImg"
                >
                  <i slot="default" class="el-icon-plus"></i>
                  <div slot="file" slot-scope="{ file }">
                    <img
                      class="el-upload-list__item-thumbnail"
                      :src="file.url"
                      alt=""
                    />
                    <span class="el-upload-list__item-actions">
                      <span
                        class="el-upload-list__item-preview"
                        @click="handlePictureCardPreview(file)"
                      >
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleRemove(file)"
                      >
                        <i class="el-icon-delete"></i>
                      </span>
                    </span>
                  </div>
                </el-upload>
                <el-dialog
                  title="预览"
                  :append-to-body="true"
                  :visible.sync="dialogVisible"
                >
                  <img width="100%" :src="dialogImageUrl" alt="" />
                </el-dialog>
              </div>
            </div>
            <div style="align-items: flex-start" class="you-hui-row">
              <span class="tg-label--required" style="margin-top: 16px"
                >内部备注</span
              >
              <span style="flex: 1"
                ><el-input
                  style="width: 100%; height: 88px"
                  v-model="invoice.innerRemark"
                  type="textarea"
                  placeholder="备注"
                ></el-input
              ></span>
            </div>
            <div style="align-items: flex-start" class="you-hui-row">
              <span style="margin-top: 16px">外部备注</span>
              <span style="flex: 1"
                ><el-input
                  style="width: 100%"
                  v-model="invoice.outerRemark"
                  type="textarea"
                  placeholder="备注"
                ></el-input
              ></span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-cls-footer">
        <div>
          <el-button
            class="tg-button--plain"
            type="plain"
            @click="cancelOrder"
            v-if="hold_state"
            >取消订单</el-button
          >
          <el-button class="tg-button--plain" type="plain" @click="close" v-else
            >取消</el-button
          >

          <el-button
            class="tg-button--primary"
            type="primary"
            :loading="chargeBtnloading"
            @click="confirmPrice"
            >确定</el-button
          >
        </div>
      </span>
    </el-dialog>

    <el-dialog
      v-if="qrcode_pay_visible"
      :visible="true"
      title="收款"
      width="460px"
      :before-close="() => ((hold_state = true), (qrcode_pay_visible = false))"
      class="qrcode_pay_dialog"
    >
      <div class="time-count">
        <span v-if="pay_status == 'unpaid'">支付中...</span>
        <span v-if="pay_status == 'paid'" class="tg-text--green">支付完成</span>
        <span v-if="pay_status == 'failed'" class="tg-text--red">支付失败</span>
      </div>
      <div v-if="qrUrl" class="settlement-qr-code">
        <VueQr
          draggable="false"
          :correctLevel="3"
          :dotScale="1"
          :margin="0"
          :size="336"
          :text="qrUrl"
        />
      </div>
      <div v-if="deadline_tip" class="deadline-tip">{{ deadline_tip }}</div>
    </el-dialog>
  </div>
</template>

<script>
import chargeApi from "@/api/charge";
import orderApi from "@/api/order";
import VueQr from "vue-qr";
import eBaoPay from "@/api/eBaoPay";
import postManagementApi from "@/api/postManagement";
import { v4 as uuidv4 } from "uuid";
import { mapState } from "vuex";
import inputNumber from "./inputNumber.vue";

// import { price_type_list } from "@/public/dict";
export default {
  data() {
    return {
      // is_push: 1,
      qrcode_pay_visible: false,
      qrUrl: "",
      pay_status: "unpaid",
      price: undefined,
      priceType: "",
      // priceTypeList: [],
      payment: [],
      payWayOptions: [],
      role_options: [],
      fileList: [],
      invoice: {
        certs: [],
        innerRemark: "",
        outerRemark: "",
        performanceUser: []
      },
      dialogImageUrl: "",
      dialogVisible: false,
      payResultTimer: null,
      chargeBtnloading: false,
      charge_date: "",
      deadline_tip: "",
      studentInfo: null,
      hold_state: false,
      hold_state_text:
        "该订单已同步第三方支付平台，如要修改金额请取消订单重新收费"
    };
  },
  components: {
    VueQr,
    inputNumber
  },
  props: {
    order_id: {
      type: String,
      default: ""
    },
    page_from: {
      type: String,
      default: ""
    }
  },
  computed: {
    ...mapState({
      ChargeCategory: (state) => state.dictionary.ChargeCategory,
      // 订金收费类型
      priceTypeList() {
        console.log(this.$parent.stu_info.department_id, this.ChargeCategory);
        return this.$parent.stu_info.department_id !== "ci1fmgdsrj8s73b043a0" &&
          this.$parent.stu_info.department_id !== "ci3g2euhnt0c73f04si0" &&
          this.$parent.stu_info.department_id !== "2" &&
          this.$parent.stu_info.department_id !== "cnprbbm9ojcs73ekg8v0"
          ? this.ChargeCategory.filter(
              (item) =>
                item.category_name === "预存-续费" ||
                item.category_name === "预存-订金"
            )
          : this.ChargeCategory.filter(
              (item) =>
                item.goods_type === "prestore" &&
                item.category_id !== "deposit_start"
            );
      }
    }),

    // ----还差金额
    surplus_amount() {
      const { price, payment } = this;
      let sum = 0;
      if (payment) {
        payment.map((item) => {
          if (item.pay_price) {
            sum += Number(item.pay_price);
          }
        });
      }
      return (Number(price ?? 0) - sum).toFixed(2);
    }
  },
  watch: {
    fileList(val) {
      this.invoice.certs = this.fileList.map((item) => item.url);
      if (val && val.length < 3) {
        $(".el-upload--picture-card").show();
      } else {
        $(".el-upload--picture-card").hide();
      }
    },
    payment(val) {
      // 自动填充实交金额
      if (val && val.length === 1) {
        const { price } = this;
        this.$set(val[0], "pay_price", price);
      }
    }
  },
  mounted() {
    // this.priceTypeList = price_type_list;

    this.getPosList();
    // 收费页面
    if (this.page_from === "charge_index") {
      this.charge_date = this.$parent.charge_date;
    }
    if (this.order_id) {
      this.getOrderInfo();
    } else {
      const { stu_info } = this.$parent;
      this.studentInfo = stu_info;
      this.getPaymentMethod();
    }
  },
  beforeDestroy() {
    clearInterval(this.payResultTimer);
  },
  methods: {
    cancelOrder() {
      this.$confirm("此操作将取消订单，确定要进行此操作吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        orderApi
          .cancelOrder({
            order_id: this.order_id
          })
          .then((res) => {
            if (res.data.code === 0) {
              this.$emit("cancelOrderSuccess");
              // location.reload();
              // this.$router.go(0);
            } else {
              this.$message.error(res.data.message);
            }
          });
      });
    },
    keepHoldState() {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      }
    },
    // 匹配出销售角色的name值
    performanceUserChange(val, item) {
      item.role_name = this.role_options.find((role) => role.id === val)?.name;
    },
    getOrderInfo() {
      const { order_id } = this;
      if (order_id) {
        orderApi.getOrderInfo({ order_id }).then((res) => {
          const { code, data, message } = res.data;
          if (code === 0) {
            this.fillStudentInfo(data);
            this.fillOrderInfo(data);
            this.fillExtendInfo(data);
            this.getPaymentMethod(data);
          } else {
            this.$message.error(message);
          }
        });
      }
    },
    fillPayment(data) {
      const { payWayOptions } = this;
      const { payment } = data;
      payment?.forEach((item1) => {
        const checkedItem = payWayOptions.find(
          (item2) => item1.pay_method === item2.key
        );
        checkedItem.checked = true;
        item1.value = checkedItem.value;
        item1.type = checkedItem.type;
        if (item1.pay_method === "POS") {
          // 修复历史的POS未选择方式，默认初始为扫码
          item1.pos_channel = item1.pos_channel || "ScanQrCode";
        }
      });
      this.payment = payment ?? [];
    },
    fillOrderInfo(data) {
      const { items } = data;
      this.price = items[0]?.original_unit_price;
      this.priceType = items[0]?.fee_type;
    },
    fillStudentInfo(data) {
      const { user_info, wallet } = data;
      const { user_type, student_id, customer_id } = user_info;
      user_info.student_id =
        user_type === "customer" ? customer_id : student_id;
      this.studentInfo = {
        ...user_info,
        left_cash: wallet.balance,
        left_prepay: wallet.prepay
      };
    },
    fillExtendInfo(data) {
      const { extend_info, performance, order_info } = data;
      const performanceUser = [];
      performance?.map((item) => {
        performanceUser.push({
          userId: item.employee_id,
          userName: item.employee_name,
          userRole: item.role_id,
          role_name: item.role_name
        });
      });
      this.invoice = {
        certs: extend_info.image,
        innerRemark: extend_info.inner_remark,
        outerRemark: extend_info.outer_remark,
        performanceUser
      };
      const images = [];
      extend_info.image.map((item) => {
        images.push({
          uid: uuidv4(),
          url: item
        });
      });
      this.fileList = images;
      // 如果是从订单列表进行收费
      if (this.page_from === "order_list") {
        this.charge_date = order_info.charge_date;
      }
      if (order_info.deadline > 0) {
        this.hold_state = true;
      }
    },
    get_target_info() {
      const { studentInfo } = this;
      return {
        department_id: studentInfo.department_id,
        department_name: studentInfo.department_name,
        student_number: studentInfo.student_number,
        target_id: studentInfo.student_id,
        target_mobile: studentInfo.student_mobile,
        target_name: studentInfo.student_name,
        target_status: studentInfo.student_type,
        target_type: studentInfo.user_type,
        target_gender: studentInfo.student_gender
      };
    },

    close() {
      if (this.payResultTimer) {
        clearInterval(this.payResultTimer);
      }
      this.$emit("close");
    },

    async getPaymentMethod(d) {
      let department_id = "";
      if (this.order_id) {
        department_id = this.studentInfo.department_id;
      } else {
        const { stu_info } = this.$parent;
        department_id = stu_info?.department_id;
      }
      const res = await chargeApi.paymentMethod({
        department_id
      });
      const { data } = res.data;
      this.payWayOptions = data.filter(
        (item) => item.business === "charge" && !item.is_shop
      );
      // 已创建的订单
      if (d) {
        this.fillPayment(d);
      }
    },

    getPosList() {
      postManagementApi
        .GetPostList({ is_enabled: true, office_type: "performance" })
        .then((res) => {
          if (+res.status === 200 && res.data) {
            this.role_options = res.data
              ? res.data.map((item) => {
                  return {
                    name: item.name,
                    id: item.id,
                    disabled: item.disabled
                  };
                })
              : [];
          }
        });
    },
    payWayBlur(item) {
      const val = item.pay_price;
      if (val) {
        if (!/^[0-9]+(.[0-9]{1,2})?$/.test(val)) {
          this.$message.info("输入数据格式有误，请重新输入！");
          item.pay_price = "";
        } else {
          item.pay_price = Number(item.pay_price);
        }
      }
    },
    removePayway(val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        this.payment.splice(
          this.payment.findIndex((item) => item.pay_method === val.pay_method),
          1
        );
        this.payWayOptions.map((item) => {
          if (item.key === val.pay_method) {
            item.checked = false;
          }
        });
        this.$forceUpdate();
      }
    },
    selectPayWay(val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        if (val.type === "online") {
          const itemIndex = this.payment.findIndex((item) => {
            return item.type === "online";
          });
          if (itemIndex !== -1) {
            this.payment.splice(itemIndex, 1);
            this.payWayOptions.map((item) => {
              if (item.type === "online") item.checked = false;
            });
          }
        }
        val.checked = true;
        const index = this.payment.findIndex((item) => {
          return item.pay_method === val.key;
        });
        if (index < 0) {
          const obj = {
            pay_method: val.key,
            type: val.type,
            value: val.value
          };
          if (obj.pay_method === "POS") {
            // POS机付款初始默认值为扫码方式
            obj.pos_channel = "ScanQrCode";
          }
          this.payment.push(obj);
        }
      }
    },
    delPayWay(e, val) {
      if (this.hold_state) {
        this.$message.error(this.hold_state_text);
      } else {
        e.stopPropagation();
        const index = this.payment.findIndex((item) => {
          return item.pay_method === val.key;
        });
        this.payment.splice(index, 1);

        val.checked = false;
        this.$forceUpdate();
      }
    },
    // 添加归属人
    createRow() {
      this.invoice.performanceUser.push({
        userId: "",
        userName: "",
        userRole: "",
        role_name: ""
      });
    },
    // 删除归属人
    delRow(i) {
      this.invoice.performanceUser.splice(i, 1);
    },
    beforeUpload(file) {
      // const { uploadFiles } = this.$refs["uploadImgs"];
      const isLt20M = file.size / 1024 / 1024 > 20;
      if (isLt20M) {
        this.$message.info("上传头像图片大小不能超过 20MB!");
        return false;
      }
    },
    uploadImg(upload) {
      const f = upload.file;
      const suffix = f.name.match(/[^.]+$/)[0];
      const name = `${uuidv4()}.${suffix}`;
      const copyFile = new File([f], name);
      this.Oss.uploadFile(copyFile).then((res) => {
        if (res.code === 0) {
          this.fileList.push({
            name: res.objectKey,
            url: res.url
          });
        }
      });
    },
    handleRemove(file) {
      this.fileList.map((item, index) => {
        if (file.uid === item.uid) {
          this.fileList.splice(index, 1);
        }
      });
    },
    // 预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },

    // 校验银行卡格式
    validateBankNo() {
      const { payment } = this;
      const transfer = payment.find((item) => item.pay_method === "transfer");
      if (transfer && !transfer.pay_account) {
        this.$message.info("付款银行卡号不能为空！");
        return false;
      } else {
        return true;
      }
    },
    // 确认收费
    confirmPrice() {
      const { surplus_amount } = this;
      if (!this.price) {
        this.$message.info(`请输入预存金额!`);
        return;
      }

      if (!this.priceType) {
        this.$message.info(`请选择预存类型!`);
        return;
      }
      if (!this.validateBankNo()) {
        return;
      }
      if (surplus_amount > 0) {
        this.$message.info(`还差金额${surplus_amount}元，不能进行收费！`);
        return;
      }
      // 业绩归属人信息必须完整
      const check_performance = this.invoice.performanceUser.every(
        (item) => item.userId && item.userRole
      );
      if (!check_performance) {
        this.$message.info("业绩归属人信息不完整，请检查后重新提交！");
        return;
      }

      if (surplus_amount < 0) {
        this.$message.info(`还差金额不能小于0，请核对相关录入金额！`);
        return;
      }
      if (!this.invoice.innerRemark) {
        this.$message.info("内部备注不能为空！");
        return;
      }

      this.$confirm("此操作将进行收费，确定要进行收费吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.chargeConfirm();
        })
        .catch(() => {});
    },
    get_performance_param() {
      const arr = [];
      this.invoice.performanceUser.map((item) => {
        arr.push({
          employee_id: item.userId,
          employee_name: item.userName,
          role_id: item.userRole,
          role_name: item.role_name
        });
      });
      return arr;
    },
    get_payment_param() {
      return this.payment.filter((item) => item.pay_price >= 0);
    },
    getSaveOrderParams() {
      const { invoice, price, priceType, order_id, charge_date } = this;
      const target = this.get_target_info();
      const performance = this.get_performance_param();
      const payment = this.get_payment_param();
      const data = {
        order_id,
        target,
        entities: [
          {
            original_unit_price: +price,
            standard_numb: 1,
            entity_name: "预存",
            entity_type: "prestore",
            fee_type: priceType,
            entity_num: 1
          }
        ],
        extend_info: {
          image: invoice.certs,
          inner_remark: invoice.innerRemark,
          outer_remark: invoice.outerRemark,
          // push_parents: this.is_push,
          charge_date
        },
        performance,
        payment
      };
      return data;
    },
    openPayDialog(msn, okay, deadline) {
      this.qrcode_pay_visible = true;
      const { payment } = this;
      const that = this;
      const arr = payment.filter((item) => Number(item.pay_price) > 0);
      // 二维码付款
      if (arr.findIndex((item) => item.pay_method === "qr_code") > -1) {
        this.qrUrl = `${window.location.origin}/qrPay?order_id=${msn}&dead_line=${okay}`;
        console.log("qrUrl==>", this.qrUrl);
        if (deadline) {
          // 后台返回的时间是纳秒
          const time = this.moment(deadline / 10e5).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.deadline_tip = `二维码有效期截至: ${time}，请您尽快支付`;
        }
        // 每3s获取一次支付结果
        this.payResultTimer = setInterval(() => {
          that.getPayResult(msn);
        }, 3000);
      }
      // pos机付款接口
      if (arr.findIndex((item) => item.pay_method === "POS") > -1) {
        const pay_price = arr.find(
          (item) => item.pay_method === "POS"
        ).pay_price;
        eBaoPay
          .posPay({
            order_id: msn,
            amount: pay_price
          })
          .then((res) => {
            const { code, message } = res.data;

            if (code === 0) {
              // 每3s获取一次支付结果
              this.payResultTimer = setInterval(() => {
                that.getPayResult(msn);
              }, 3000);
            } else {
              this.$message.error(message);
              this.qrcode_pay_visible = false;
            }
          })
          .catch((err) => {
            this.$message.error(err);
          });
      }
    },
    getPayResult(msn) {
      eBaoPay
        .getPayResult({
          order_id: msn
        })
        .then((res) => {
          const { data, code, message } = res.data;
          if (code === 0) {
            this.pay_status = data.pay_status;
            if (this.pay_status === "paid") {
              this.$message.success("收费成功！");
              this.resetData();
              this.close();
            } else if (this.pay_status === "failed") {
              this.$message.error(message);
            }
          } else {
            this.$message.error(message);
          }
        })
        .catch(() => {
          this.$message.error("收费失败，请重试！");
          clearInterval(this.payResultTimer);
        });
    },
    resetData() {
      this.$parent.list = [];
      this.$parent.stu_info = null;
      this.$parent.prestore_order_id = "";
      this.$parent.student_check_arr = [];
    },
    // 收费提交
    chargeConfirm() {
      const params = this.getSaveOrderParams();
      params.action = "create"; // 订单收费操作
      // console.log("object :>> ", params);
      // return;
      this.chargeBtnloading = true;
      chargeApi.orderCheck(params).then((res) => {
        const { code, data } = res.data;
        if (code === 0) {
          if (data.is_exist) {
            this.$confirm("系统已存在相同订单，是否继续收费？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                this.orderSave(params);
              })
              .catch(() => {
                this.$router.push({
                  name: "orderManagement"
                });
              });
          } else {
            this.orderSave(params);
          }
        }
      });
    },
    orderSave(params) {
      const only_offline = params.payment
        .filter((item) => item.pay_price > 0)
        .every((item) => item.type === "offline");
      chargeApi
        .chargeSaveOrder(params)
        .then((res) => {
          if (this.payResultTimer) {
            clearInterval(this.payResultTimer);
          }
          const { data, message, code } = res.data;
          if (code === 0) {
            this.order_id = data.order_id;
            if (only_offline) {
              this.resetData();
              this.$message.success("收费成功！");
              this.$emit("paySuccess", data.order_id);
              this.close();
            } else {
              // 实交金额大于0且有线上付款方式才进行线上付款
              const { msn, okay, deadline } = data;
              this.openPayDialog(msn, okay, deadline);
            }
          } else {
            this.$message.error(message);
          }
          this.chargeBtnloading = false;
        })
        .catch(() => {
          this.$message.error("提交失败，请重试！");
          this.chargeBtnloading = false;
        });
    }
  }
};
</script>
<style lang="less" scoped>
.settlement-class {
  .tg-table__box {
    margin: 0;
    margin-top: 16px;
    box-shadow: none;
    .row__box--flex {
      display: inline-flex;
      flex-direction: row;
      align-items: center;
    }
    .mark {
      width: 15px;
      height: 15px;
      margin-right: 4px;
    }
    /deep/ .charge-type-list-select {
      .el-input {
        width: 100px;
      }
    }
  }
  ::v-deep .el-dialog__body {
    padding: 16px;
    overflow: auto;
    height: 700px;
  }
  .tg-dialog__content {
    .s-title {
      background-color: #f5f8fc;
      color: #475669;
      height: 48px;
      line-height: 48px;
      padding-left: 16px;
      border: 1px solid #2d80ed;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      font-weight: 500;
      box-sizing: border-box;
    }
    .s-content {
      padding: 24px 16px;
      border: 1px solid #e0e6ed;
      border-top: 0;
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
      .pay-way-button-list {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 16px;
        .pay-way-button {
          cursor: pointer;
          height: 32px;
          min-width: 96px;
          margin-bottom: 10px;
          // position: relative;
          text-align: center;
          // line-height: 32px;
          border-radius: 4px;
          margin-right: 20px;
          box-sizing: border-box;
          padding: 0 12px;
          font-size: 14px;
          background: #fff;
          color: #2d80ed;
          border: 1px solid #2d80ed;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          /deep/ .el-icon-close {
            background-image: url("~@/assets/图片/icon_close_white.png");
            width: 10px;
            height: 10px;
          }
          &:hover {
            background: #ebf4ff;
            color: #2d80ed;
            .el-icon-close {
              background-image: url("~@/assets/图片/icon_close.png");
            }
          }
          // .tag_bg {
          //   width: 100%;
          //   height: 100%;
          //   border-radius: 4px;
          //   opacity: 0.12;
          //   position: absolute;
          //   left: 0;
          //   top: 0;
          // }
          .tag_line {
            padding: 0 8px;
          }

          // /deep/.el-icon-close {
          //   border-radius: 50%;
          //   text-align: center;
          //   position: relative;
          //   cursor: pointer;
          //   font-size: 12px;
          //   height: 16px;
          //   line-height: 16px;
          //   vertical-align: middle;
          //   top: -1px;
          //   background-image: none;
          //   &:hover {
          //     background-image: none;
          //   }
          // }
        }
        .payway-button-selected {
          background: #2d80ed;
          color: #fff;
        }
      }
      .payway-list {
        display: flex;
        width: 100%;
        align-items: center;
        flex-wrap: wrap;
        .payway-row {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          margin-right: 16px;
          width: 100%;
          span:nth-child(1) {
            width: 100px;
            margin-right: 12px;
          }

          span:nth-child(2) {
            text-align: left;
          }

          span:nth-child(3) {
            margin-left: 16px;
          }
        }
      }
      .you-hui-row {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        span:nth-child(1) {
          width: 100px;
        }
        span:nth-child(2) {
          text-align: left;
        }
        span:nth-child(3) {
          margin-left: 16px;
        }
        &:last-child {
          margin-bottom: 0px;
        }
      }
    }
    .tg-label--required:before {
      content: "*";
      color: #ff0317;
      margin-right: 4px;
    }
    .stu-info {
      margin-top: 16px;
      .info {
        border-bottom-left-radius: 4px;
        border-bottom-right-radius: 4px;
        border: 1px solid #e0e6ed;
        border-top: 0;
        display: flex;
        align-items: center;
        height: 52px;
        padding: 0 16px;
        span {
          margin-right: 24px;
          color: #475669;
          em {
            font-style: normal;
            color: #8492a6;
            margin-right: 16px;
          }
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    .tg-table-bottom {
      padding: 16px 16px;
      border-bottom: 1px solid #e0e6ed;
      padding-left: 678px;
      span {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        em {
          font-style: normal;
          width: 100px;
          text-align: left;
          margin-left: 132px;
        }
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
    .section-middle {
      display: flex;
      justify-content: space-between;
      margin-top: 32px;
      .s-left {
        // width: 50%;
        flex: 1;
        margin-right: 16px;
        .you-hui-box {
          margin-bottom: 40px;
        }
      }
      .settlement-amount-box {
        width: 335px;
        .amount-row {
          display: flex;
          align-items: center;
          margin-bottom: 16px;
          justify-content: space-between;
          span:nth-child(1) {
            width: 74px;
            text-align: right;
          }
          span:nth-child(2) {
            width: 112px;
            text-align: left;
            box-sizing: border-box;
            padding-left: 15px;
          }
          &:last-child {
            margin-bottom: 0;
            span:nth-child(2) {
              padding-left: 0px;
            }
          }
          .prefix {
            display: block;
            text-align: right !important;
            margin-top: 6px;
            width: 20px !important;
            color: #333;
            margin-left: 5px;
          }
        }
      }
    }
    .charge-box {
      .diff-amount {
        display: flex;
        align-items: center;
        border: 1px solid #2d80ed;
        border-radius: 4px;
        justify-content: center;
        width: 280px;
        padding: 6px 0;
        background-color: #f5f8fc;
        margin-bottom: 16px;
        img {
          width: 12px;
          height: 12px;
          margin-right: 6px;
        }
      }
      .you-hui-row {
        span:nth-child(1) {
          min-width: 60px;
          text-align: left;
          margin-left: 0;
          margin-right: 16px;
          width: 100px;
        }
      }
    }
    .extra-box {
      margin-bottom: 30px;
      .you-hui-row {
        span:nth-child(1) {
          width: 85px;
          text-align: left;
        }
      }
      .user-table {
        width: 100%;
        margin-left: 6px;
        .s-title {
          display: flex;
          align-items: center;
          padding: 0px 16px;
          border-radius: 4px;
          font-weight: normal;
          label {
            line-height: 100%;
            display: block;
            box-sizing: border-box;
          }
        }
        .s-content {
          padding: 0px;
          border: 0;
          .row {
            display: flex;
            align-items: center;
            border-bottom: 1px solid #e0e6ed;
            margin: 0;
            padding: 10px 0;
            label {
              display: block;
              box-sizing: border-box;
            }
          }
          .add-icon {
            font-style: normal;
            color: @base-color;
            display: flex;
            align-items: center;
            cursor: pointer;
            padding: 12px 0;
            border-bottom: 1px solid #e0e6ed;
            img {
              margin-right: 5px;
            }
          }
        }
      }
    }
  }
}
/deep/ .extra-box {
  .el-upload--picture-card {
    width: 100px;
    height: 100px;
    line-height: 98px;
  }
  .el-upload-list__item {
    width: 100px;
    height: 100px;
  }
  .el-upload-list__item-actions {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10px;
    box-sizing: border-box;
  }
  .el-textarea__inner {
    height: 88px;
    width: 100%;
  }
}
::v-deep .dialog-cls-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .check-list {
    font-size: @text-size_normal;
    color: #475669;
    span {
      color: @base-color;
      font-weight: bold;
    }
  }
}
.more {
  width: 16px;
  height: 4px;
}
.del-suffix {
  width: 14px;
  height: 14px;
  cursor: pointer;
}
.custom--select {
  & > ::v-deep .el-input .el-input__inner {
    cursor: pointer;
  }
  & > ::v-deep .el-input .el-input__suffix-inner {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: inherit;
    margin-right: 10px;
    cursor: pointer;
  }
}
.charge-type-class {
  ::v-deep .el-dialog__body {
    padding: 16px;
    overflow: auto;
    background-color: #fff;
  }
  ::v-deep .tg-dialog__content {
    height: 150px;

    .el-select {
      width: 586px;

      .el-input {
        width: 586px;
      }
    }
  }
  ::v-deep .el-input.is-focus::after {
    border: none;
  }
}
/deep/.qrcode_pay_dialog {
  .el-dialog__body {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    min-height: 200px;
  }
}
.time-count {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  & > span {
    font-size: 18px;
    margin-bottom: 10px;
  }
  .counter {
    color: red;
    font-size: 18px;
    margin-left: 10px;
  }
}
.divider {
  width: 100%;
  margin: 32px 0;
  height: 1px;
  background-color: #e0e6ed;
  padding: 0;
}
.deadline-tip {
  color: red;
  font-size: 13px;
  margin-top: 10px;
}
</style>
