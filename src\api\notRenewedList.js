import axios from "../http";
import Vue from "vue";
import qs from "qs";

// 全国
function renewNoList(data) {
  const new_data = qs.stringify(data, {
    arrayFormat: "repeat"
  });
  return axios
    .get(`/api/school-service/renew-no/list?${new_data}`)
    .then((response) => {
      return response.data;
    })
    .catch((error) => {
      Vue.prototype.$message.error(error.err);
    })
    .finally();
}
export default {
  async renewNoList(data) {
    return renewNoList(data);
  }
};
